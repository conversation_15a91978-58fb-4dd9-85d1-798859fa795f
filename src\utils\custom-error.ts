import { config } from '../config/env.config';

export class AppError extends Error {
  public readonly statusCode: number;
  public readonly isOperational: boolean;

  constructor(message: string, statusCode = 500) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    if (config.NODE_ENV !== 'production') {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}