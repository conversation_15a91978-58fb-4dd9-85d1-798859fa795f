// import { Router } from 'express';
// import RolesController from '../controllers/roles.controller';
// import { checkPermissions, isAuthenticated } from '../../../middlewares/auth.middleware';
// import { validateRequest, validateUUIDParam } from '../../../middlewares/zod-validation.middleware';
// import { ModuleEnum } from '../../../enum/module.enum';
// import { ModuleAction } from '../../../enum/module-action';

// const router = Router();
// const rolesController = new RolesController();

// // router.get('', isAuthenticated, rolesController.getAllRoles.bind(rolesController));
// // router.post('/', isAuthenticated, checkPermissions([{ module: 'roles', action: 'create' }]), validateRequest(createRoleSchema), rolesController.createRole.bind(rolesController));
// // router.patch('/:roleId', isAuthenticated, checkPermissions([{ module: 'roles', action: 'update' }]), validateRequest(updateRolePermissionsSchema), validateUUIDParam('roleId'), rolesController.updateRolePermission.bind(rolesController));
// // router.get('/:roleId', isAuthenticated, checkPermissions([{ module: ModuleEnum.ROLES, action: ModuleAction.READ }]), validateUUIDParam('roleId'), rolesController.getRoleById.bind(rolesController));

// export default router;
