import { ModuleEnum } from '../enum/module.enum';
import { RoleEnum } from '../enum/role.enum';


export const EMAIL_QUEUE = 'email_queue';
export const SMS_QUEUE = 'sms_queue';


export const RolePermissions = {
  [RoleEnum.SYSTEM_ADMIN]: {
    [ModuleEnum.CLIENTS]: ['create', 'read', 'update', 'delete', 'approve'],
    [ModuleEnum.QUOTES]: ['create', 'read', 'update', 'delete', 'approve'],
    [ModuleEnum.CLAIMS]: ['create', 'read', 'update', 'delete', 'approve'],
    [ModuleEnum.POLICIES]: ['create', 'read', 'update', 'delete', 'approve'],
    [ModuleEnum.PAYMENTS]: ['create', 'read', 'update', 'delete', 'approve'],
    [ModuleEnum.AUTH]: ['create', 'read', 'update', 'delete'], // full access to auth
  },

  [RoleEnum.SALES_REP]: {
    [ModuleEnum.CLIENTS]: ['create', 'read', 'update'],
    [ModuleEnum.QUOTES]: ['create', 'read', 'update'],
    [ModuleEnum.CLAIMS]: [],
    [ModuleEnum.POLICIES]: [],
    [ModuleEnum.PAYMENTS]: [],
    [ModuleEnum.AUTH]: ['read'], // limited to read (e.g., view own data)
  },

  [RoleEnum.CLAIMS_HANDLER]: {
    [ModuleEnum.CLIENTS]: [],
    [ModuleEnum.QUOTES]: [],
    [ModuleEnum.CLAIMS]: ['read', 'update'],
    [ModuleEnum.POLICIES]: [],
    [ModuleEnum.PAYMENTS]: [],
    [ModuleEnum.AUTH]: ['read'],
  },

  [RoleEnum.UNDERWRITER]: {
    [ModuleEnum.CLIENTS]: [],
    [ModuleEnum.QUOTES]: ['read', 'update', 'approve'],
    [ModuleEnum.CLAIMS]: [],
    [ModuleEnum.POLICIES]: ['read', 'update', 'approve'],
    [ModuleEnum.PAYMENTS]: [],
    [ModuleEnum.AUTH]: ['read'], // limited to read
  },

  [RoleEnum.FINANCE_OFFICER]: {
    [ModuleEnum.CLIENTS]: [],
    [ModuleEnum.QUOTES]: [],
    [ModuleEnum.CLAIMS]: [],
    [ModuleEnum.POLICIES]: [],
    [ModuleEnum.PAYMENTS]: ['read', 'update'],
    [ModuleEnum.AUTH]: ['read'],
  },
};
