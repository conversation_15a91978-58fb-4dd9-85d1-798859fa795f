import { Router } from 'express';
import { ModuleEnum } from '../../../enum/module.enum';
import { ModuleAction } from '../../../enum/module-action';
import BrokingSlipController from '../controllers/broking-slip.controller';
import { checkPermissions } from '../../../middlewares/auth.middleware';
import {
  generateBrokingSlipSchema,
  sendBrokingSlipToInsurerSchema,
  updateBrokingSlipSchema,
} from '../validations/broking-slip.schema';
import {
  validateRequest,
  validateUUIDParam
} from '../../../middlewares/zod-validation.middleware';

const router = Router();
const brokingSlipController = new BrokingSlipController();

router.post(
  '/',
  checkPermissions([{ module: ModuleEnum.BROKING_SLIP, action: ModuleAction.CREATE }]),
  validateRequest(generateBrokingSlipSchema),
  brokingSlipController.generateBrokingSlip.bind(brokingSlipController)
);

router.get(
  '/',
  checkPermissions([{ module: ModuleEnum.BROKING_SLIP, action: ModuleAction.READ }]),
  brokingSlipController.listBrokingSlips.bind(brokingSlipController)
);

router.get(
  '/dashboard',
  checkPermissions([{ module: ModuleEnum.BROKING_SLIP, action: ModuleAction.READ }]),
  brokingSlipController.brokingSlipDashBoardData.bind(brokingSlipController)
);

router.get(
  '/:id',
  checkPermissions([{ module: ModuleEnum.BROKING_SLIP, action: ModuleAction.READ }]),
  validateUUIDParam('id'),
  brokingSlipController.getBrokingSLip.bind(brokingSlipController)
);

router.patch(
  '/:id',
  checkPermissions([{ module: ModuleEnum.BROKING_SLIP, action: ModuleAction.UPDATE }]),
  validateUUIDParam('id'),
  validateRequest(updateBrokingSlipSchema),
  brokingSlipController.updateBrokingSlip.bind(brokingSlipController)
);

router.post(
  '/insurers',
  checkPermissions([{ module: ModuleEnum.BROKING_SLIP, action: ModuleAction.CREATE }]),
  validateRequest(sendBrokingSlipToInsurerSchema),
  brokingSlipController.sendBrokingSlipToInsurers.bind(brokingSlipController)
);

export default router;
