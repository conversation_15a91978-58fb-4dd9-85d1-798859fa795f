import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';


export class Category extends Model<
  InferAttributes<Category>,
  InferCreationAttributes<Category>
> {
  declare id: CreationOptional<string>;
  declare name: string;
  declare policy_id?: string;
  declare summary_of_cover?: CreationOptional<string>;
  declare default_rate: number;
  declare extensions?: CreationOptional<Array<{ name: string; value: number }>>;
  declare createdAt?: CreationOptional<Date>;
  declare updatedAt?: CreationOptional<Date>;
}

export function initCategoryModel(sequelize: Sequelize): typeof Category {
  Category.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      summary_of_cover: {
        type: DataTypes.TEXT,
      },
      extensions: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      policy_id: {
        type: DataTypes.STRING,
        allowNull: true
      },
      default_rate: {
        type: DataTypes.DECIMAL(5, 2),
        allowNull: true
      }
    },
    {
      sequelize,
      modelName: 'Category',
      tableName: 'categories',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          unique: true,
          fields: ['name', 'policy_id']

        }
      ]
    },
  );
  return Category;
}
