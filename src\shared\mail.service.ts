import { Resend } from 'resend';
import * as fs from 'fs';
import * as path from 'path';
import * as handlebars from 'handlebars';
import chunk from 'lodash.chunk';
import { config } from '../config/env.config';
import logger from '../utils/logger';
import { MailProvider } from '../enum/mail-providers.enum';

export class MailService {
  private resend: Resend;

  constructor() {
    this.resend = new Resend(config.RESEND_API_KEY as string);
  }

  /**
   * Load and compile the email template using Handlebars
   * @param templateName - Name of the template file (e.g., "welcome-email.html")
   * @param data - Data to inject into the template
   */
  private compileTemplate(templateName: string, data: Record<string, any>): string {
    const templatePath = path.join(__dirname, '../shared/templates', templateName);
    const templateSource = fs.readFileSync(templatePath, 'utf-8');
    const template = handlebars.compile(templateSource);
    return template(data);
  }

  /**
   * Send an email using the configured provider
   * @param to - Recipient email
   * @param subject - Email subject
   * @param templateName - Template file name
   * @param data - Dynamic template data
   * @param from - Optional sender override
   */
  async sendMail({
    to,
    subject,
    templateName,
    data,
    from,
  }: {
    to: string | string[];
    subject: string;
    templateName: string;
    data: Record<string, any>;
    from?: string;
  }) {
    const html = this.compileTemplate(templateName, data);
    const sender = from || `Koguna Babura <${config.DEFAULT_EMAIL_SENDER}>`;

    try {
      if (config.MAIL_PROVIDER === MailProvider.RESEND) {
        const recipients = Array.isArray(to) ? to : [to];
        const batches = chunk(recipients, 50); // break into groups of 50

        for (const [index, batch] of batches.entries()) {
          logger.info(`📤 Sending batch ${index + 1}/${batches.length} (${batch.length} recipients)...`);
          const batchPayload = batch.map(email => ({
            from: sender,
            to: [email],
            subject,
            html,
          }));

          const response = await this.resend.batch.send(batchPayload);

          if (response?.data) {
            logger.info(`✅ Email batch ${index + 1} sent via Resend`);
          } else {
            logger.error(`❌ Email batch ${index + 1} failed ${JSON.stringify(response)}`);
          }
        }

        return { success: true, message: 'All batches processed.' };
      }

      if (config.MAIL_PROVIDER === MailProvider.SES) {
        logger.info('SES selected but not implemented');
        return {
          success: true,
          message: 'SES selected, but no implementation available yet.',
        };
      }

      throw new Error('Invalid mail provider configured');
    } catch (error: any) {
      logger.error(`Error sending email: ${error.message}`);
      throw new Error('Failed to send email');
    }
  }
}

