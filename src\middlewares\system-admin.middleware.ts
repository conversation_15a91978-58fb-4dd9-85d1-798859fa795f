import { Response, NextFunction } from 'express';
import { RoleEnum } from '../enum/role.enum';
import { RequestWithAuditMetadata } from '../interfaces/custom-request.interface';

export const CheckSystemAdmin = (
  req: RequestWithAuditMetadata,
  res: Response,
  next: NextFunction,
) => {
  const user = req.user;

  if (!user || user.role !== RoleEnum.SYSTEM_ADMIN) {
    res.status(403).json({
      message: 'Forbidden: You do not have the required permissions to access this resource.',
    });
    return;
  }
  next();
};
