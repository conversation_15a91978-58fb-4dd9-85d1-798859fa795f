// src/models/Role.ts
import {
  Model,
  DataTypes,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  Sequelize,
} from 'sequelize';
import { Permission } from './permission.model';

export class Role extends Model<InferAttributes<Role>, InferCreationAttributes<Role>> {
  declare id: CreationOptional<string>;
  declare name: string;
  declare permissions?: Permission[];
  declare description: string;
  declare is_system_role?: CreationOptional<boolean>;

  // These magic methods are automatically provided by Sequelize when you define a 'belongsToMany' association
  declare addPermission: (permission: Permission | Permission[]) => Promise<void>;
  declare addPermissions: (permissions: Permission[]) => Promise<void>;
  declare getPermissions: () => Promise<Permission[]>;
  declare setPermissions: (permissions: Permission[]) => Promise<void>;
  declare removePermission: (permission: Permission) => Promise<void>;
}

export function initRoleModel(sequelize: Sequelize): typeof Role {
  Role.init(
    {
      id: { type: DataTypes.UUID, defaultValue: DataTypes.UUIDV4, primaryKey: true },
      name: { type: DataTypes.STRING, unique: true, allowNull: false },
      description: { type: DataTypes.STRING, allowNull: false },
      is_system_role: { type: DataTypes.BOOLEAN, defaultValue: false },
    },
    {
      sequelize,
      modelName: 'Role',
      tableName: 'roles',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          fields: ['is_system_role'],
        },
      ],
    },
  );

  return Role;
}
