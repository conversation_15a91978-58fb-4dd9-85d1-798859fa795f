import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';

export class LoadingType extends Model<
  InferAttributes<LoadingType>,
  InferCreationAttributes<LoadingType>
> {
  declare id: CreationOptional<string>;
  declare name: string;
  declare value: number;
  declare enabled: boolean;
  declare createdAt?: CreationOptional<Date>;
  declare updatedAt?: CreationOptional<Date>;
}

export function initLoadingTypeModel(sequelize: Sequelize): typeof LoadingType {
  LoadingType.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true
      },
      value: {
        type: DataTypes.DECIMAL(4, 1),
        allowNull: false,
      },
      enabled: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      sequelize,
      modelName: 'LoadingType',
      tableName: 'loading_types',
      timestamps: true,
      paranoid: true,
    },
  );

  return LoadingType;
}