import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';


export class Assets extends Model<
  InferAttributes<Assets>,
  InferCreationAttributes<Assets>
> {
  declare id: CreationOptional<string>;
  declare name: string;
  declare createdAt?: CreationOptional<Date>;
  declare updatedAt?: CreationOptional<Date>;
}

export function initAssetsModel(sequelize: Sequelize): typeof Assets {
  Assets.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
    },
    {
      sequelize,
      modelName: 'Assets',
      tableName: 'assets',
      timestamps: true,
      paranoid: true,
    },
  );
  return Assets;
}
