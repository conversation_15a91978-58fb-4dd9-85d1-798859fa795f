import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  ForeignKey,
} from 'sequelize';
import { Quotation } from './quotation.model';
import { ChangeType } from '../../../enum/change-type.enum';

export class QuotationHistory extends Model<
  InferAttributes<QuotationHistory>,
  InferCreationAttributes<QuotationHistory>
> {
  declare id: CreationOptional<string>;
  declare quotation_id: ForeignKey<string>;
  declare version: number;
  declare data: Record<string, any>;
  declare change_type: string;
  declare changed_by?: ForeignKey<string>;
  declare createdAt?: CreationOptional<Date>;
  declare updatedAt?: CreationOptional<Date>;
}

export function initQuotationHistoryModel(sequelize: Sequelize): typeof QuotationHistory {
  QuotationHistory.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      quotation_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: { model: Quotation, key: 'id' },
        onDelete: 'CASCADE',
      },
      version: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      data: {
        type: DataTypes.JSONB,
        allowNull: false,
      },
      change_type: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: ChangeType.CREATE
      },
      changed_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: 'QuotationHistory',
      tableName: 'quotation_histories',
      timestamps: true,
      paranoid: true
    }
  );

  return QuotationHistory;
}
