// import { Request, Response } from 'express';
// import RolesService from '../services/roles.service';
// import logger from '../../../utils/logger';
// import { PaginationDto } from '../../../utils/pagination';

// export default class RolesController {
//   private roleService: RolesService;
//   constructor() {
//     this.roleService = new RolesService();
//   }

//   // /**
//   //    * Get all roles
//   //    * @returns {Promise<Response>} - A promise that resolves to a response object containing the roles.
//   //    */
//   // async getAllRoles(req: Request, res: Response): Promise<Response> {
//   //   try {
//   //     const paginationDto = new PaginationDto(req.query);
//   //     const result = await this.roleService.listAllRoles(paginationDto);
//   //     return res.status(200).json({ message: 'Roles fetched successfully', data: result });
//   //   } catch (error) {
//   //     return res.status(400).json({
//   //       message: error?.errors?.[0]?.message || error.message || 'Failed to fetch roles',
//   //     });
//   //   }
//   // }

//   // /**
//   //  *
//   //  * @param req - The request object containing the role ID.
//   //  * @param res - The response object to send back the result.
//   //  * @returns {Promise<Response>} - A promise that resolves to a response object containing the role.
//   //  */

//   // async createRole(req: Request, res: Response): Promise<Response> {
//   //   try {
//   //     const { name, permissions, description } = req.body;
//   //     const result = await this.roleService.createRole(name, permissions, description);
//   //     return res.status(201).json({ message: 'Role created successfully', data: result });
//   //   } catch (error) {
//   //     return res.status(400).json({
//   //       message: error?.errors?.[0]?.message || error.message || 'Failed to create role',
//   //     });
//   //   }
//   // }

//   // async updateRolePermission(req: Request, res: Response) {
//   //   const { permissions, roleName } = req.body;
//   //   const { roleId } = req.params;
//   //   try {
//   //     const result = await this.roleService.updateRolePermissions(roleId, permissions, roleName);
//   //     return res.status(200).json({ message: 'Role permissions updated successfully', data: result });
//   //   } catch (error) {
//   //     logger.error(`Error updating role permissions: ${error}`);
//   //     return res.status(400).json({
//   //       message: error?.errors?.[0]?.message || error.message || 'Failed to update role permissions',
//   //     });
//   //   }
//   // }

//   // async getRoleById(req: Request, res: Response) {
//   //   const { roleId } = req.params;
//   //   try {
//   //     const result = await this.roleService.getRoleById(roleId);
//   //     return res.status(200).json({ message: 'Role fetched successfully', data: result });
//   //   } catch (error) {
//   //     logger.error(`Error fetching role by ID: ${error}`);
//   //     return res.status(400).json({
//   //       message: error?.errors?.[0]?.message || error.message || 'Failed to fetch role',
//   //     });
//   //   }
//   // }
// }
