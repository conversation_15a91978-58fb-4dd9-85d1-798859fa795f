import Decimal from 'decimal.js/decimal';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { AuditLogEnum, ErrorCode } from '../../../enum/trail-action.enum';
import { ModuleEnum } from '../../../enum/module.enum';
import { ContactList } from '../models/contact-list.model';
import logger from '../../../utils/logger';
import { PaginationDto, PaginationMetadataDto, PaginationResultDto } from '../../../utils/pagination';
import { CacheKeys } from '../../../enum/cache-keys.enum';
import { AppError } from '../../../utils/custom-error';
import { OtpHelper } from '../../../utils/otp-helper';
import redis from '../../../utils/redis-client';
import { JWTService } from '../../../utils/jwt';
import { BrokingSlip } from '../../broking-slip/models/broking-slip.model';
import { EMAIL_QUEUE } from '../../../constants/role-permission.constant';
import { rabbitMQService } from '../../workers/rabbitmq/rabbitmq.service';
import { InsurerBrokingSlips } from '../models/insurer-brokingSlips.model';
import { Slip } from '../../broking-slip/models/slip.model';
import { Policy } from '../../policy/models/policy.model';
import { Clients } from '../../clients/models/clients.model';
import { ProvideQuotationDTO } from '../validations/insurer.validation';
import { Insurer } from '../models/insurer.model';
import { Quotation } from '../../quotation/models/quotation.model';
import cacheService from '../../../utils/cache.service';
import { QuotationHistory } from '../../quotation/models/quotation-history.model';
import { ChangeType } from '../../../enum/change-type.enum';

/**
 * Service for handling insurer authentication-related operations.
 */
export default class InsurerPortalService {
  private readonly messageQueue = rabbitMQService;

  /**
     * Generate and Send Insurer Access Code
     * @param contactId - Contact Id
     * @returns - The generated OTP access code
     */
  async generateAndSendAccessCode(contactId: string): Promise<string> {
    try {
      const contact = await ContactList.findByPk(contactId);
      if (!contact) {
        throw new AppError('Contact not found', ErrorCode.NOT_FOUND);
      }

      const otp = OtpHelper.generateOTP(6);
      await redis.setex(`${CacheKeys.INSURER_LOGIN}${contactId}`, 300, otp); // Store OTP in Redis for 5 minutes

      await this.messageQueue.sendToQueue(EMAIL_QUEUE, {
        to: contact.email,
        subject: `Your OTP Code is ${otp}`,
        templateName: 'broking-slip-access-code.html',
        data: {
          contactName: contact.full_name,
          accessCode: otp,
          expiryMinutes: 5,
          date: new Date().toLocaleString(),
        },
      });

      return otp;
    } catch (error) {
      logger.error('Error generating/sending access code for broking slip', error);
      throw new AppError(
        error.message || 'Internal server error',
        error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR
      );
    }
  }


  /**
   * 
   * @param accessCode - Access code provided
   * @param contactId - Contact ID
   * @param ipAddress - User IP Address
   * @param userAgent - User device info
   * @returns - Broking Slip with relations if access code matches
   */
  async verifyAccessCode(
    accessCode: string,
    contactId: string,
    ipAddress: string,
    userAgent: string
  ) {
    try {
      const contact = await ContactList.findByPk(contactId);

      if (!contact) {
        await AuditLogService.logEvent({
          userId: '',
          eventType: AuditLogEnum.APPROVE_RESOURCE,
          module: ModuleEnum.AUTH,
          action_description: 'Contact not found',
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('Contact not found', ErrorCode.NOT_FOUND);
      }

      const storedOtp = await redis.get(`${CacheKeys.INSURER_LOGIN}${contactId}`);

      if (storedOtp !== accessCode) {
        await AuditLogService.logEvent({
          userId: '',
          eventType: AuditLogEnum.LOGIN_FAILED,
          module: ModuleEnum.AUTH,
          action_description: `Invalid access code for contact ${contactId}`,
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('Invalid OTP', ErrorCode.BAD_REQUEST);
      }
      const { token, tokenExpiresIn } = JWTService.generateContactToken(contact);

      await redis.del(`${CacheKeys.INSURER_LOGIN}${contactId}`);

      return {
        contact,
        token: {
          accessToken: token,
          expiresIn: tokenExpiresIn
        }
      };

    } catch (error) {
      logger.error('Error verifying access code for broking slip', error);
      throw new AppError(
        error.message || 'Internal server error',
        error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * List BrokingSlips sent to Insurers
   * @param contactId - Contact ID
   * @param insurerId - Optional Insurer ID to filter by
   * @param paginationDto - Pagination options
   * @returns 
   */
  async listInsurersBrokingSlips(
    contactId: string,
    insurerId: string,
    paginationDto: PaginationDto
  ): Promise<PaginationResultDto<BrokingSlip>> {
    try {
      const { limit, skip, order } = paginationDto;

      const cacheKey = cacheService.generateKey(CacheKeys.INSURER_BROKING_SLIPS, {
        // contactId,
        insurerId,
        pagination: paginationDto
      });

      const cachedData = await cacheService.get<PaginationResultDto<BrokingSlip>>(cacheKey);

      if (cachedData) {
        return new PaginationResultDto<BrokingSlip>(cachedData.data, cachedData.meta);
      }

      const { count, rows } = await BrokingSlip.findAndCountAll({
        include: [
          // join through InsurerBrokingSlips for filtering
          {
            model: InsurerBrokingSlips,
            as: 'insurerBrokingSlips',
            required: true,
            where: { insurer_id: insurerId, contact_id: contactId },
            attributes: []
          },
          { model: Clients, as: 'client' },
          {
            model: Slip,
            as: 'slips',
            include: [{ model: Policy, as: 'policy' }],
          },
        ],
        limit,
        offset: skip,
        order: [['createdAt', order]],
        distinct: true,
      });

      const metadata = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });

      const result = new PaginationResultDto<BrokingSlip>(rows, metadata);

      await cacheService.set(cacheKey, result, 3600); // cache for 1 hour

      return result;
    } catch (error) {
      logger.error(`Error listing broking slips for insurer ${insurerId}: ${error}`);
      throw new AppError(
        error.message || 'Internal server error',
        error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Get Broking Slip By ID
   * @param brokingSlipId - Broking Slip ID
   * @returns - Matching Broking SLip
   */
  async getBrokingSlipById(brokingSlipId: string): Promise<BrokingSlip> {
    const brokingSlip = await BrokingSlip.findByPk(brokingSlipId, {
      include: [
        { model: Clients, as: 'client' },
        {
          model: Slip,
          as: 'slips',
          include: [{ model: Policy, as: 'policy' }],
        },
      ],
    });

    if (!brokingSlip) {
      throw new AppError('Broking slip not found', ErrorCode.NOT_FOUND);
    }

    return brokingSlip;
  }

  /**
   * 
   * @param insurerId - Insurer ID
   * @param ipAddress - IpAddress of the request origin
   * @param userAgent - User agent string from the request
   * @param data - Quotation data
   * @returns - Returns the created quotation
   */
  async provideQuote(
    insurerId: string,
    ipAddress: string,
    userAgent: string,
    contactId: string,
    data: ProvideQuotationDTO,
  ): Promise<Quotation> {
    try {
      const [brokingSlip, contact] = await Promise.all([
        BrokingSlip.findByPk(data.brokingSlipId),
        ContactList.findByPk(contactId)
      ]);

      if (!contact) {
        logger.error('Contact not found');
        throw new AppError('Contact not found', ErrorCode.NOT_FOUND);
      }

      if (!brokingSlip) {
        logger.error('Contact not found');
        throw new AppError('Broking slip not found', ErrorCode.NOT_FOUND);
      }

      const insurer = await Insurer.findByPk(insurerId);
      if (!insurer) {
        logger.error('Contact not found');
        throw new AppError('Insurer not found', ErrorCode.NOT_FOUND);
      }

      const quotation = await Quotation.create({
        broking_slip_id: data.brokingSlipId,
        insurer_id: insurerId,
        contact_id: contactId,
        discounts: data.discounts?.map((d) => ({
          name: d.name ?? '',
          value: d.value ?? 0,
          enabled: d.enabled ?? false,
        })),

        loadings: data.loadings?.map((l) => ({
          name: l.name ?? '',
          value: l.value ?? 0,
          enabled: l.enabled ?? false,
        })),

        supporting_documents: data.supporting_documents ?? [],
        base_rate: data.base_rate,
        broker_commission: data.broker_commission,
        commission_value: new Decimal(data.commission_value).toFixed(2),
        total_premium_payable: new Decimal(data.total_premium_payable).toFixed(2),
        premium_due: new Decimal(data.premium_due).toFixed(2),
        add_on_cover_premium: data.add_on_cover_premium,
        sum_insured: new Decimal(data.sum_insured).toFixed(2),
        base_premium: new Decimal(data.base_premium).toFixed(2),
        taxes: new Decimal(data.taxes).toFixed(2),
        net_premium: new Decimal(data.net_premium).toFixed(2),
      });


      await Promise.all([
        AuditLogService.logEvent({
          userId: null,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.QUOTES,
          action_description: `Quotation ${quotation.id} created for broking slip ${data.brokingSlipId} by insurer ${insurerId}`,
          ip_address: ipAddress,
          device_info: userAgent,
        }),
        cacheService.invalidateByPattern(`${CacheKeys.QUOTATION_LIST}:*`),
        cacheService.invalidateByPattern(`${CacheKeys.INSURER_QUOTATION_LIST}:*`),
        QuotationHistory.create({
          quotation_id: quotation.id,
          version: 1,
          data: quotation.toJSON(),
          change_type: ChangeType.CREATE,
          changed_by: contactId,
        })
      ]);

      return quotation;
    } catch (error) {
      logger.error(`Error creating quotation ${error}`);

      throw new AppError(
        error.message || 'Internal server error',
        error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR
      );
    }
  }
}