import { PaginationDto, PaginationMetadataDto, PaginationResultDto } from '../../../utils/pagination';
import { Quotation } from '../models/quotation.model';
import cacheService from '../../../utils/cache.service';
import { CacheKeys } from '../../../enum/cache-keys.enum';
import { BrokingSlip } from '../../../modules/broking-slip/models/broking-slip.model';
import { Clients } from '../../../modules/clients/models/clients.model';
import { Slip } from '../../../modules/broking-slip/models/slip.model';
import logger from '../../../utils/logger';
import { AppError } from '../../../utils/custom-error';
import { ErrorCode } from '../../../enum/trail-action.enum';

export default class QuotationService {

  /**
   * List All Quotations
   * @param paginationDto - Pagination data
   * @returns 
   */
  async listAllQuotations(
    paginationDto: PaginationDto
  ): Promise<PaginationResultDto<Quotation>> {
    try {
      const { order, limit, skip } = paginationDto;
      const cacheKey = cacheService.generateKey(CacheKeys.QUOTATION_LIST, {
        pagination: PaginationDto
      });
      const cachedData = await cacheService.get<PaginationResultDto<Quotation>>(cacheKey);

      if (cachedData) {
        return new PaginationResultDto(cachedData.data, cachedData.meta);
      }

      const { count, rows } = await Quotation.findAndCountAll({
        include: [
          {
            model: BrokingSlip,
            as: 'brokingSlip',
            include: [
              { model: Clients, as: 'client' },
              { model: Slip, as: 'slips' }
            ]
          }
        ],
        limit,
        offset: skip,
        order: [['createdAt', order]]
      });
      const metadata = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count
      });

      const result = new PaginationResultDto(rows, metadata);

      await cacheService.set(cacheKey, result, 3600);
      return result;

    } catch (error) {
      logger.error(`Failed to list quotations: ${error.message}`);
      throw new AppError('Failed to list quotations', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * List an Insurer Quotations
   * @param insurerId - Insurer ID
   * @param paginationDto - Pagination DTO
   * @returns - Returns a list of quotations for the given insurer
   */
  async listInsurerQuotations(
    insurerId: string,
    paginationDto: PaginationDto
  ): Promise<PaginationResultDto<Quotation>> {
    try {
      const { order, limit, skip } = paginationDto;
      const cacheKey = cacheService.generateKey(CacheKeys.INSURER_QUOTATION_LIST, {
        pagination: PaginationDto
      });
      const cachedData = await cacheService.get<PaginationResultDto<Quotation>>(cacheKey);

      if (cachedData) {
        return new PaginationResultDto(cachedData.data, cachedData.meta);
      }

      const { count, rows } = await Quotation.findAndCountAll({
        where: { insurer_id: insurerId },
        include: [
          {
            model: BrokingSlip,
            as: 'brokingSlip',
            include: [
              { model: Clients, as: 'client' },
              { model: Slip, as: 'slips' }
            ]
          }
        ],
        limit,
        offset: skip,
        order: [['createdAt', order]]
      });
      const metadata = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count
      });

      const result = new PaginationResultDto(rows, metadata);

      await cacheService.set(cacheKey, result, 3600); // cache 1 hour
      return result;

    } catch (error) {
      logger.error(`Failed to list quotations: ${error.message}`);
      throw new AppError('Failed to list quotations', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }
}