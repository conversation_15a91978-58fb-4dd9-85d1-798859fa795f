import morgan, { StreamOptions } from 'morgan';
import logger from '../utils/logger';
import { config } from '../config/env.config';

// Custom stream to write morgan logs through winston
const stream: StreamOptions = {
  write: (message) => logger.http(message.trim()),
};

// Skip logging during tests
const skip = () => config.NODE_ENV === 'test';

// Morgan middleware setup
const httpLogger = morgan('combined', { stream, skip });

export default httpLogger;
