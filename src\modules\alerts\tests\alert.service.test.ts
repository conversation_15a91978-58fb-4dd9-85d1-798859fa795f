import AlertService from '../services/alert.service';
import { Alerts } from '../models/alerts.model';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { PaginationDto } from '../../../utils/pagination';

jest.mock('../../../config/env.config', () => ({
  config: {
    VALID_DOMAINS: ['@company.com'],
    FRONTEND_URL: ['https://frontend.com'],
    DEFAULT_EMAIL_SENDER: '<EMAIL>',
  }
}));

jest.mock('../models/alerts.model');
jest.mock('../../audit-trail-service/services/audit-trail.service');
jest.mock('../../../utils/logger', () => ({ error: jest.fn(), info: jest.fn() }));

const mockAlert = {
  id: 'alert-id',
  alert_type: 'EMAIL',
  update: jest.fn(),
  destroy: jest.fn(),
  toJSON: function () { return this; },
};

describe('AlertService', () => {
  const service = new AlertService();
  const mockAuditLog = AuditLogService.logEvent as jest.Mock;

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createOrUpdateAlerts', () => {
    it('should update existing alerts and create new ones', async () => {
      (Alerts.findOne as jest.Mock)
        .mockResolvedValueOnce({ ...mockAlert, update: jest.fn() }) // first alert exists
        .mockResolvedValueOnce(null); // second alert does not exist
      (Alerts.create as jest.Mock).mockResolvedValueOnce(mockAlert);

      const alertsData = [
        { alert_type: 'EMAIL', message: 'msg1' },
        { alert_type: 'SMS', message: 'msg2' },
      ];

      const result = await service.createOrUpdateAlerts('user-id', 'ip', 'agent', alertsData);

      expect(Alerts.findOne).toHaveBeenCalledTimes(2);
      expect(mockAuditLog).toHaveBeenCalled();
      expect(result.length).toBe(2);
    });

    it('should handle errors and log them', async () => {
      (Alerts.findOne as jest.Mock).mockRejectedValue(new Error('DB error'));
      await expect(
        service.createOrUpdateAlerts('user-id', 'ip', 'agent', [{ alert_type: 'EMAIL' }])
      ).rejects.toThrow('DB error');
    });
  });

  describe('createAlert', () => {
    it('should create a new alert if not exists', async () => {
      (Alerts.findOne as jest.Mock).mockResolvedValue(null);
      (Alerts.create as jest.Mock).mockResolvedValue(mockAlert);

      const result = await service.createAlert('user-id', 'ip', 'agent', { alert_type: 'EMAIL' });
      expect(result).toEqual(mockAlert);
    });

    it('should throw if alert type already exists', async () => {
      (Alerts.findOne as jest.Mock).mockResolvedValue(mockAlert);

      await expect(
        service.createAlert('user-id', 'ip', 'agent', { alert_type: 'EMAIL' })
      ).rejects.toThrow('Alert type already exists');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should handle errors and log them', async () => {
      (Alerts.findOne as jest.Mock).mockRejectedValue(new Error('DB error'));
      await expect(
        service.createAlert('user-id', 'ip', 'agent', { alert_type: 'EMAIL' })
      ).rejects.toThrow('DB error');
    });
  });

  describe('updateAlert', () => {
    it('should update an existing alert', async () => {
      const updateMock = jest.fn();
      (Alerts.findByPk as jest.Mock).mockResolvedValue({ ...mockAlert, update: updateMock });

      const result = await service.updateAlert('alert-id', { message: 'updated' }, 'user-id', 'ip', 'agent');
      expect(updateMock).toHaveBeenCalledWith({ message: 'updated' });
      expect(result).toHaveProperty('id', 'alert-id');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw if alert not found', async () => {
      (Alerts.findByPk as jest.Mock).mockResolvedValue(null);

      await expect(
        service.updateAlert('alert-id', { message: 'updated' }, 'user-id', 'ip', 'agent')
      ).rejects.toThrow('Alert not found');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should handle errors and log them', async () => {
      (Alerts.findByPk as jest.Mock).mockRejectedValue(new Error('DB error'));
      await expect(
        service.updateAlert('alert-id', { message: 'updated' }, 'user-id', 'ip', 'agent')
      ).rejects.toThrow('DB error');
    });
  });

  describe('getAlerts', () => {
    it('should return paginated alerts', async () => {
      (Alerts.findAndCountAll as jest.Mock).mockResolvedValue({ count: 1, rows: [mockAlert] });
      const paginationDto = new PaginationDto({});

      const result = await service.getAlerts(paginationDto);
      expect(result.data.length).toBe(1);
      expect(result.meta.itemCount).toBe(1);
    });

    it('should handle errors and log them', async () => {
      (Alerts.findAndCountAll as jest.Mock).mockRejectedValue(new Error('DB error'));
      const paginationDto = new PaginationDto({});
      await expect(service.getAlerts(paginationDto)).rejects.toThrow('Failed to fetch alerts');
    });
  });

  describe('deleteAlert', () => {
    it('should delete an alert if found', async () => {
      const destroyMock = jest.fn();
      (Alerts.findByPk as jest.Mock).mockResolvedValue({ ...mockAlert, destroy: destroyMock });

      await expect(
        service.deleteAlert({ alertId: 'alert-id', userId: 'user-id', ipAddress: 'ip', userAgent: 'agent' })
      ).resolves.toBeUndefined();
      expect(destroyMock).toHaveBeenCalled();
    });

    it('should throw if alert not found', async () => {
      (Alerts.findByPk as jest.Mock).mockResolvedValue(null);

      await expect(
        service.deleteAlert({ alertId: 'alert-id', userId: 'user-id', ipAddress: 'ip', userAgent: 'agent' })
      ).rejects.toThrow('Alert not found');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });
});