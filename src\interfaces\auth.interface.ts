export interface InviteUserPayload {
  email: string;
  first_name: string;
  last_name: string;
  roleId: string;
  phone_number: string;
  department: string;
  userId: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface RegisterSystemAdminPayload {
  email: string;
  password?: string;
  first_name: string;
  last_name: string;
  roleId: string;
  phone_number: string;
  department: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface LoginPayload {
  email: string;
  password: string;
  ipAddress: string;
  userAgent: string;
  rememberMe?: boolean;
}
