import { AuditLog } from '../models/audit.model';
import { AuditLogEnum } from '../../../enum/trail-action.enum';
import logger from '../../../utils/logger';
import { AuditLogQueryParams } from '../../../interfaces/audit-log.interface';
import { PaginationDto, PaginationMetadataDto, PaginationResultDto } from '../../../utils/pagination';
import { Op } from 'sequelize';
import { User } from '../../../modules/auth-service/models/user.model';
import { AppError } from '../../../utils/custom-error';

/**
 * AuditLogService class
 * Handles logging actions performed on resources.
 */
class AuditLogService {
  /**
   * Log an action to the audit log.
   * @param param - An object containing the parameters for logging.
   * @param param.userId - The ID of the user performing the action.
   * @param param.eventType - The type of event (e.g., create, update, delete).
   * @param param.module - The module where the action occurred.
   * @param param.entity_id - The ID of the entity being acted upon (optional).
   * @param param.action_description - A description of the action performed (optional).
   * @param param.error_code - An error code if the action failed (optional).
   * @param param.ip_address - The IP address of the user (optional).
   * @param param.device_info - Information about the user's device (optional).
   * @param param.meta - Additional metadata related to the action (optional).
   * @returns A promise that resolves to the created AuditLog object.
   */
  static async logEvent(param: {
    userId: string;
    eventType: AuditLogEnum;
    module: string;
    entity_id?: string;
    action_description?: string;
    error_code?: number;
    ip_address?: string;
    device_info?: string;
  }): Promise<AuditLog | undefined> {
    try {
      const {
        userId,
        eventType,
        module,
        entity_id,
        action_description,
        error_code,
        ip_address,
        device_info,
      } = param;
      const code = String(error_code);

      const auditLog = await AuditLog.create({
        user_id: userId,
        event_type: eventType,
        module,
        entity_id,
        action_description,
        error_code: code,
        ip_address,
        device_info,
        created_at: new Date(),
      });

      return auditLog;
    } catch (error) {
      logger.error(`Error logging audit action: ${error}`);
    }
  }



  /**
   * View Audit Logs based on filters.
   * If no filters are provided, all logs will be returned.
   * @param filters - An object containing filter criteria (optional).
   * @param paginationDto - Pagination options.
   * @returns A promise that resolves to an array of audit logs. With Pagination
   */

  async viewAuditLogs(
    filters: AuditLogQueryParams = {},
    paginationDto: PaginationDto
  ): Promise<PaginationResultDto<AuditLog>> {
    try {
      const { limit, order, skip } = paginationDto;
      const whereClause: any = {};

      if (filters.user_id) {
        whereClause.user_id = filters.user_id;
      }
      if (filters.event_type) {
        whereClause.event_type = filters.event_type;
      }
      if (filters.module) {
        whereClause.module = filters.module;
      }
      if (filters.startDate || filters.endDate) {
        whereClause.created_at = {
          ...(filters.startDate && { [Op.gte]: filters.startDate }),
          ...(filters.endDate && { [Op.lte]: filters.endDate }),
        };
      }

      if (filters.ip_address) {
        whereClause.ip_address = filters.ip_address;
      }
      if (filters.error_code) {
        whereClause.error_code = filters.error_code;
      }
      if (filters.entity_id) {
        whereClause.entity_id = filters.entity_id;
      }

      if (filters.searchQuery) {
        whereClause[Op.or] = [
          { event_type: { [Op.like]: `%${filters.searchQuery}%` } },
          { module: { [Op.like]: `%${filters.searchQuery}%` } },
          { ip_address: { [Op.like]: `%${filters.searchQuery}%` } },
          { error_code: { [Op.like]: `%${filters.searchQuery}%` } },
          { action_description: { [Op.like]: `%${filters.searchQuery}%` } },
        ];
      }

      const { count, rows } = await AuditLog.findAndCountAll({
        where: whereClause,
        limit,
        offset: skip,
        order: [['created_at', order]],
        include: [
          {
            model: User,
            as: 'user',
            attributes: { exclude: ['password'] },
          },
        ],
      });

      const meta = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });

      return new PaginationResultDto(rows, meta);

    } catch (error) {
      logger.error(`Error fetching audit logs: ${error}`);
      throw new AppError(`Failed to fetch audit logs: ${error.message}`);
    }
  }
}

export default AuditLogService;
