import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  ForeignKey,
} from 'sequelize';
import { BrokingSlip } from './broking-slip.model';
import { Policy } from '../../policy/models/policy.model';

export class Slip extends Model<InferAttributes<Slip>, InferCreationAttributes<Slip>> {
  declare id: CreationOptional<string>;
  declare brokingSlipId: ForeignKey<string>;
  declare policyId: string;
  declare policy_category: string;
  declare summary_of_cover?: string;
  declare sections?: CreationOptional<
    Array<{
      name: string;
      data?: Array<{ name: string; label?: string; value?: string }>;
    }>
  >;
  declare schedule?: CreationOptional<{
    name: string;
    data?: Array<{ name: string; value: number;[key: string]: unknown }>;
  }>;
  declare extensions?: CreationOptional<Array<{ name: string; value: number }>>;
  declare discounts?: Array<{ name: string; value: number; enabled: boolean }>;
  declare loadings?: CreationOptional<Array<{ name: string; value: number; enabled: boolean }>>;
  declare supporting_documents?: CreationOptional<Array<string>>;
  declare base_rate?: number;
  declare broker_commission?: number;
  declare sum_insured?: string;
  declare gross_premium?: string;
  declare net_premium?: string;
  declare order?: CreationOptional<string[]>;
  declare createdAt?: CreationOptional<Date>;
  declare updatedAt?: CreationOptional<Date>;
}

export function initSlipModel(sequelize: Sequelize): typeof Slip {
  Slip.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      brokingSlipId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: { model: BrokingSlip, key: 'id' },
        onDelete: 'CASCADE',
      },
      policyId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: { model: Policy, key: 'id' },
        // onDelete: 'CASCADE',
      },
      policy_category: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      summary_of_cover: {
        type: DataTypes.TEXT,
      },
      sections: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      schedule: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      extensions: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      discounts: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      loadings: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      base_rate: {
        type: DataTypes.DECIMAL(18, 2),
      },
      broker_commission: {
        type: DataTypes.DECIMAL(18, 2),
      },
      sum_insured: {
        type: DataTypes.DECIMAL(18, 2),
      },
      gross_premium: {
        type: DataTypes.DECIMAL(18, 2),
      },
      net_premium: {
        type: DataTypes.DECIMAL(18, 2),
      },
      supporting_documents: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      order: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
        defaultValue: [],
      },
    },
    {
      sequelize,
      modelName: 'Slip',
      tableName: 'slips',
      timestamps: true,
      paranoid: true,
    }
  );

  return Slip;
}
