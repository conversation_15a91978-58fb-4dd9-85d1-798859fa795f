export enum AuditLogEnum {
  CREATE_USER = 'invite',
  ASSIGN_ROLE = 'assign_role',
  REMOVE_ROLE = 'remove_role',
  CREATE_RESOURCE = 'create',
  UPDATE_RESOURCE = 'update',
  DELETE_RESOURCE = 'delete',
  APPROVE_RESOURCE = 'approve',
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILED = 'login_failure',
  LOGOUT = 'logout_success',
  LOGOUT_FAILED = 'logout_failed',
  REGISTER_SUCCESS = 'register_success',
  REGISTER_FAILED = 'register_failed',
  PASSWORD_RESET = 'password_reset',
  SSO_LOGIN = 'sso_login',
  API_ERROR = 'api_error',
  OTHER = 'other',
  UNAUTHORIZED = 'unauthorized',
}

export enum ErrorCode {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  NOT_FOUND = 404,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  BAD_REQUEST = 400,
  CONFLICT = 409,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503,
}
