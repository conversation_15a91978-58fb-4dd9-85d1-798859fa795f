import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';

export class DiscountType extends Model<
  InferAttributes<DiscountType>,
  InferCreationAttributes<DiscountType>
> {
  declare id: CreationOptional<string>;
  declare name: string;
  declare value: number;
  declare enabled: boolean;
  declare createdAt?: CreationOptional<Date>;
  declare updatedAt?: CreationOptional<Date>;
}

export function initDiscountTypeModel(sequelize: Sequelize): typeof DiscountType {
  DiscountType.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true
      },
      value: {
        type: DataTypes.DECIMAL(4, 1),
        allowNull: false,
      },
      enabled: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      sequelize,
      modelName: 'DiscountType',
      tableName: 'discount_types',
      timestamps: true,
      paranoid: true,
    },
  );

  return DiscountType;
}
