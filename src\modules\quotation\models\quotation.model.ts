import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  ForeignKey,
} from 'sequelize';
import { BrokingSlipStatusEnum } from '../../../enum/broking-slip-status.enum';
// import { Policy } from '../../policy/models/policy.model';
import { BrokingSlip } from '../../../modules/broking-slip/models/broking-slip.model';
import { ContactList } from '../../../modules/insurer/models/contact-list.model';
import { Insurer } from '../../../modules/insurer/models/insurer.model';

export class Quotation extends Model<InferAttributes<Quotation>, InferCreationAttributes<Quotation>> {
  declare id: CreationOptional<string>;
  declare broking_slip_id: ForeignKey<string>;
  declare insurer_id: ForeignKey<string>;
  declare contact_id: ForeignKey<string>;
  // declare policyId: string;
  // declare policy_category: string;
  // declare summary_of_cover?: string;
  // declare sections?: CreationOptional<
  //   Array<{
  //     name: string;
  //     data?: Array<{ name: string; label?: string; value?: string }>;
  //   }>
  // >;
  // declare schedule?: CreationOptional<{
  //   name: string;
  //   data?: Array<{ name: string; value: number;[key: string]: unknown }>;
  // }>;
  declare extensions?: CreationOptional<Array<{ name: string; value: number }>>;
  declare discounts?: Array<{ name: string; value: number; enabled: boolean }>;
  declare loadings?: CreationOptional<Array<{ name: string; value: number; enabled: boolean }>>;
  declare supporting_documents?: CreationOptional<Array<string>>;
  declare base_rate?: number;
  declare sum_insured?: string;
  declare base_premium?: string;
  declare net_premium?: string;
  declare taxes?: string;
  declare broker_commission?: number;
  declare commission_value?: string;
  declare total_premium_payable?: string;
  declare premium_due: string;
  declare add_on_cover_premium?: string;
  declare status?: string;
  declare createdAt?: CreationOptional<Date>;
  declare updatedAt?: CreationOptional<Date>;
}

export function initQuotationModel(sequelize: Sequelize): typeof Quotation {
  Quotation.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      broking_slip_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: { model: BrokingSlip, key: 'id' },
        onDelete: 'CASCADE',
      },
      insurer_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: { model: Insurer, key: 'id' },
        onDelete: 'CASCADE',
      },
      contact_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: { model: ContactList, key: 'id' },
        onDelete: 'CASCADE',
      },
      // policyId: {
      //   type: DataTypes.UUID,
      //   allowNull: false,
      //   references: { model: Policy, key: 'id' },
      //   // onDelete: 'CASCADE',
      // },
      // policy_category: {
      //   type: DataTypes.STRING,
      //   allowNull: false,
      // },
      // summary_of_cover: {
      //   type: DataTypes.TEXT,
      // },
      // sections: {
      //   type: DataTypes.JSONB,
      //   allowNull: true,
      // },
      // schedule: {
      //   type: DataTypes.JSONB,
      //   allowNull: true,
      // },
      extensions: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      discounts: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      loadings: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      base_rate: {
        type: DataTypes.DECIMAL(18, 2),
      },
      broker_commission: {
        type: DataTypes.DECIMAL(18, 2),
      },
      commission_value: {
        type: DataTypes.DECIMAL(18, 2),
      },
      total_premium_payable: {
        type: DataTypes.DECIMAL(18, 2),
      },
      premium_due: {
        type: DataTypes.DECIMAL(18, 2),
      },
      add_on_cover_premium: {
        type: DataTypes.STRING,
      },
      sum_insured: {
        type: DataTypes.DECIMAL(18, 2),
      },
      base_premium: {
        type: DataTypes.DECIMAL(18, 2),
      },
      taxes: {
        type: DataTypes.DECIMAL(18, 2),
      },
      // gross_premium: {
      //   type: DataTypes.DECIMAL(18, 2),
      // },
      net_premium: {
        type: DataTypes.DECIMAL(18, 2),
      },
      supporting_documents: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: BrokingSlipStatusEnum.DRAFT,
      },
    },
    {
      sequelize,
      modelName: 'Quotation',
      tableName: 'quotations',
      timestamps: true,
      paranoid: true,
    }
  );

  return Quotation;
}
