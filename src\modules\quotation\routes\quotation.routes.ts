import { Router } from 'express';
import QuotationController from '../controllers/quotation.controller';
import { isInsurerAuthenticated } from '../../../middlewares/insurer.middleware';
import { checkPermissions } from '../../../middlewares/auth.middleware';
import { ModuleEnum } from '../../../enum/module.enum';
import { ModuleAction } from '../../../enum/module-action';


const router = Router();
const quotationController = new QuotationController();

router.get('/',
  checkPermissions([{
    module: ModuleEnum.QUOTES,
    action: ModuleAction.READ,
  }
  ]),
  quotationController.listAllQuotations.bind(quotationController)
);
router.get(
  '/insurer',
  isInsurerAuthenticated,
  quotationController.listInsurerQuotations.bind(quotationController)
);

export default router;
