export enum Order {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class PaginationDto {
  readonly order: Order;
  readonly page: number;
  readonly limit: number;

  constructor(query: any) {
    this.order = query.order?.toUpperCase() === 'ASC' ? Order.ASC : Order.DESC;
    this.page = Math.max(Number(query.page) || 1, 1);
    this.limit = Math.min(Math.max(Number(query.limit) || 10, 1));
  }

  get skip(): number {
    return (this.page - 1) * this.limit;
  }
}
