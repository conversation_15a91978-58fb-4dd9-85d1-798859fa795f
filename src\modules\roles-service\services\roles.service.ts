
import { literal, Op } from 'sequelize';
import logger from '../../../utils/logger';
import { Role } from '../../auth-service/models/role.model';
import { Permission } from '../../auth-service/models/permission.model';
import {
  PaginationDto,
  PaginationMetadataDto,
  PaginationResultDto,
} from '../../../utils/pagination';
import { AssignPermissionPayload } from '../../../interfaces/admin.interface';
import AuditLogService from '../../../modules/audit-trail-service/services/audit-trail.service';
import { AuditLogEnum, ErrorCode } from '../../../enum/trail-action.enum';
import { ModuleEnum } from '../../../enum/module.enum';
import { SearchQuery } from '../../../interfaces/query.interface';
import { RolePermission } from '../../../modules/auth-service/models/role-permission.model';
import { db } from '../../../database/db';
import { AppError } from '../../../utils/custom-error';

/**
 * RolesService class to manage roles and permissions
 * @class RolesService
 * @description This class provides methods to manage roles and permissions in the system.
 * It includes methods to list all roles, create a new role, update role permissions, and delete a role.
 */
export default class RolesService {
  constructor() {
    // Initialize the service
  }

  /**
   * List all roles
   * @param paginationDto - Pagination options
   * @param searchQuery - Search query options
   * @description Fetches all roles with pagination and optional search. Removes system admin role from the list.
   * @returns {Promise<Role[]>}
   */
  async listAllRoles(paginationDto: PaginationDto, searchParam: SearchQuery): Promise<PaginationResultDto<Role>> {
    try {
      const { order, limit, skip } = paginationDto;
      const { searchQuery } = searchParam;
      const whereClause = {
        // is_system_role: { [Op.ne]: true },
      };

      if (searchQuery) {
        whereClause[Op.and] = [
          {
            [Op.or]: [
              { name: { [Op.iLike]: `%${searchQuery}%` } },
              { description: { [Op.iLike]: `%${searchQuery}%` } },
            ],
          },
        ];
      }

      const { count, rows } = await Role.findAndCountAll({
        where: whereClause,
        limit,
        offset: skip,
        distinct: true,
        order: [['createdAt', order]],
        include: [
          {
            model: Permission,
            as: 'permissions',
            through: { attributes: [] }
          },
        ],
      });
      const meta = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });
      return new PaginationResultDto(rows, meta);
    } catch (error) {
      logger.error(`Error fetching roles: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }


  /**
   * @param name - The name of the role to create
   * @returns {Promise<Role>} - The created role object
   * @description Creates a new role with the given name. If the role already exists, it throws an error.
   * @throws {Error} - If the role already exists or if there is an error during creation.
   */
  async createRole(data: {
    name: string,
    permissions: Array<{ module: string; action: string, description: string }>,
    description: string,
    initiatorId: string,
    ipAddress: string,
    userAgent: string;
  }): Promise<Role> {
    const { name, permissions, description, initiatorId, ipAddress, userAgent } = data;
    try {
      const [role, created] = await Role.findOrCreate({
        where: { name },
        defaults: { name, description },
      });

      if (created) {
        logger.info(`Role created: ${role.name}`);

        const permissionInstances = await Promise.all(
          permissions.map(async (perm) => {
            const [permission] = await Permission.findOrCreate({
              where: { module: perm.module, action: perm.action },
              defaults: {
                module: perm.module,
                action: perm.action as 'create' | 'read' | 'update' | 'delete' | 'approve',
                description: perm.description,
              },
            });

            return permission;
          })
        );

        await role.addPermissions(permissionInstances); // Sequelize magic method

        logger.info(`Permissions for role ${role.name} updated successfully`);

        const roleWithPermissions = await Role.findByPk(role.id, {
          include: [
            {
              model: Permission,
              as: 'permissions',
              through: { attributes: [] }, // Optional: exclude join table from response
            },
          ],
        });

        // Audit log
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.ROLES,
          action_description: 'Role created successfully',
          ip_address: ipAddress,
          device_info: userAgent,
        });

        return roleWithPermissions!;

      } else {
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.ROLES,
          action_description: 'Role already exists',
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        logger.error(`Role already exists: ${role.name}`);
        throw new AppError('Role already exists', ErrorCode.CONFLICT);
      }
    } catch (error) {
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.ROLES,
        action_description: `Error during role creation: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error creating role ${name}:`, error);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update role permissions
   * @param roleId - The ID of the role to update
   * @param permissions - An array of permissions to assign to the role
   * @param {module: string, action: string} permissions
   * @param {string} roleName - The new name for the role (optional)
   * @returns {Promise<Role>}
   * @description Updates the permissions of a role. If a role name is provided, it updates the role name as well.
   */
  async updateRolePermissions(data: {
    roleId: string,
    roleDescription: string,
    permissions: { module: string; action: string, description: string }[],
    initiatorId: string,
    ipAddress: string,
    userAgent: string,
    roleName?: string,
  }): Promise<Role> {
    const {
      roleId,
      permissions,
      initiatorId,
      ipAddress,
      userAgent,
      roleName,
      roleDescription
    } = data;

    try {
      const role = await Role.findByPk(roleId, {
        include: [
          {
            model: Permission,
            as: 'permissions',
            through: { attributes: [] }
          }
        ]
      });

      if (!role) {
        throw new AppError('Role not found', ErrorCode.NOT_FOUND);
      }
      if (roleName) {
        const existingRole = await Role.findOne({ where: { name: roleName, id: { [Op.ne]: role.id } } });
        if (existingRole) {
          await AuditLogService.logEvent({
            userId: initiatorId,
            eventType: AuditLogEnum.UPDATE_RESOURCE,
            module: ModuleEnum.ROLES,
            action_description: 'Error during role update: Role name already exists',
            error_code: ErrorCode.BAD_REQUEST,
            ip_address: ipAddress,
            device_info: userAgent,
          });
          throw new AppError('Role name already exists', ErrorCode.CONFLICT);
        }
        await role.update({ name: roleName, description: roleDescription ?? role.description });
      }

      // Create the permission instances and associate them with the role
      const permissionInstances = await Promise.all(
        permissions.map(async (perm) => {
          const [permission, created] = await Permission.findOrCreate({
            where: { module: perm.module, action: perm.action },
            defaults: {
              module: perm.module,
              action: perm.action as 'create' | 'read' | 'update' | 'delete' | 'approve',
              description: perm.description,
            },
          });

          if (!created && permission.description !== perm.description) {
            await permission.update({ description: perm.description });
          }

          return permission;
        })
      );

      // Add the new permissions to the role (without clearing old ones)
      await role.setPermissions(permissionInstances);

      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.ROLES,
        action_description: 'Role permissions updated successfully',
        ip_address: ipAddress,
        device_info: userAgent,
      });

      logger.info(`Permissions for role ${role.name} updated successfully`);

      const updatedRole = await Role.findByPk(roleId, {
        include: [
          {
            model: Permission,
            as: 'permissions',
            through: { attributes: [] }
          }
        ]
      });


      return updatedRole;
    } catch (error) {
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.ROLES,
        action_description: `Error during role permissions update: ${error.message}`,
        error_code: ErrorCode.BAD_REQUEST,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error updating role permissions: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get role by ID
   * @param roleId - The ID of the role to get its permissions
   * @returns {Promise<Role>} - The role object with permissions
   */
  async getRoleById(roleId: string): Promise<Role> {
    try {
      const role = await Role.findByPk(roleId, {
        include: [
          {
            model: Permission,
            as: 'permissions',
            through: { attributes: [] }
          },
        ],
      });

      if (!role) {
        throw new AppError('Role not found', ErrorCode.NOT_FOUND);
      }

      return role;
    } catch (error) {
      logger.error(`Error fetching role: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   *
   * @param roleId - The ID of the role to get its permissions
   * @description Fetches the permissions associated with a specific role.
   * @throws {Error} - If the role is not found or if there is an error during fetching.
   * @returns {Promise<Permission[]>} - An array of permissions associated with the role.
   */

  async fetchRolePermissions(
    roleId: string,
    paginationDto: PaginationDto,
  ): Promise<PaginationResultDto<Permission>> {
    try {
      const { limit, order, skip } = paginationDto;

      const role = await Role.findByPk(roleId, {
        include: [
          {
            model: Permission,
            as: 'permissions',
            through: { attributes: [] },
          },
        ],
      });

      if (!role) {
        throw new AppError('Role not found', ErrorCode.NOT_FOUND);
      }

      // Fetch permissions for the role using the RolePermission join table
      const { count, rows } = await Permission.findAndCountAll({
        limit,
        offset: skip,
        order: [['createdAt', order]],
        include: [
          {
            model: Role,
            as: 'roles',
            where: { id: roleId },
            through: { attributes: [] },
            attributes: []
          },
        ],
      });

      const meta = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });

      logger.info(`Permissions for role ${roleId} fetched successfully`);

      return new PaginationResultDto(rows, meta);
    } catch (error) {
      logger.error(`Error fetching role permissions: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }


  /**
 * @param paginationDto - Pagination options
 * @description Fetches all permissions with pagination.
 * @throws {Error} - If there is an error during fetching.
 * @returns {Promise<PaginationResultDto<Permission>>} - A paginated result of permissions.
 */
  async listAvailablePermissions(
    paginationDto: PaginationDto,
    searchParam: SearchQuery,
  ): Promise<PaginationResultDto<Permission>> {
    try {
      const { order, limit, skip } = paginationDto;
      const { searchQuery } = searchParam;
      const whereClause: any = {};

      if (searchQuery) {
        whereClause[Op.and] = [
          {
            [Op.or]: [
              literal(`CAST("Permission"."module" AS TEXT) ILIKE '%${searchQuery}%'`),
              literal(`CAST("Permission"."action" AS TEXT) ILIKE '%${searchQuery}%'`),
              literal(`CAST("Permission"."description" AS TEXT) ILIKE '%${searchQuery}%'`),
            ],
          },
        ];
      }

      // Pagination and fetching permissions along with the role information from the RolePermission join table
      const { count, rows } = await Permission.findAndCountAll({
        where: whereClause,
        limit,
        offset: skip,
        order: [['createdAt', order]],
        include: [
          {
            model: Role,
            through: { attributes: [] },
            as: 'roles',
            // where: { is_system_role: { [Op.ne]: true } },
            attributes: [],
          },
        ],
      });

      const meta = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });
      return new PaginationResultDto(rows, meta);
    } catch (error) {
      logger.error(`Error fetching permissions: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }


  /**
   *
   * @param payload - The payload containing roleId, permissionId, moduleName, action, initiatorId, ipAddress, and userAgent
   * @description Assigns a permission to a role. If the role or permission does not exist, it throws an error.
   * @throws {Error} - If the role or permission is not found or if there is an error during assignment.
   * @returns {Promise<Permission>} - The assigned permission object
   */
  async assignPermissionToRole(payload: AssignPermissionPayload): Promise<Permission> {
    try {
      const { roleId, permissionId, moduleName, action, description } = payload;
      const role = await Role.findByPk(roleId);

      if (!role) {
        await AuditLogService.logEvent({
          userId: payload.initiatorId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.ROLES,
          action_description: 'Error during permission assignment: Role not found',
          error_code: ErrorCode.NOT_FOUND,
          ip_address: payload.ipAddress,
          device_info: payload.userAgent,
        });
        throw new AppError(`Role with ID ${roleId} not found.`, ErrorCode.NOT_FOUND);
      }

      // Find the permission
      const permission = await Permission.findByPk(permissionId);
      if (!permission) {
        await AuditLogService.logEvent({
          userId: payload.initiatorId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.ROLES,
          action_description: 'Error during permission assignment: Permission not found',
          error_code: ErrorCode.NOT_FOUND,
          ip_address: payload.ipAddress,
          device_info: payload.userAgent,
        });
        throw new AppError(`Permission with ID ${permissionId} not found.`, ErrorCode.NOT_FOUND);
      }

      // Update permission if needed (e.g., update module, action, description)
      permission.module = moduleName;
      permission.action = action as 'create' | 'read' | 'update' | 'delete' | 'approve';
      permission.description = description || permission.description;
      await permission.save(); // Save the updated permission

      // Create or find the RolePermission association
      await RolePermission.findOrCreate({
        where: { roleId, permissionId },
        defaults: {
          roleId,
          permissionId,
        },
      });

      // Log success if the permission is assigned
      await AuditLogService.logEvent({
        userId: payload.initiatorId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.ROLES,
        action_description: 'Permission assigned to role successfully',
        ip_address: payload.ipAddress,
        device_info: payload.userAgent,
      });

      // Log the permission creation
      logger.info(`Permission for ${role.name} - ${moduleName} - ${action} assigned successfully`);

      return permission;  // Return the updated Permission object
    } catch (error) {
      // Log the error during assignment
      await AuditLogService.logEvent({
        userId: payload.initiatorId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.ROLES,
        action_description: `Error during permission assignment: ${error.message}`,
        error_code: ErrorCode.BAD_REQUEST,
        ip_address: payload.ipAddress,
        device_info: payload.userAgent,
      });
      logger.error(`Error assigning permission to role: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }


  /**
   * Delete a role
   * @param roleId - The ID of the role to delete
   * @param initiatorId - The ID of the user initiating the deletion
   * @param ipAddress - The IP address of the user
   * @param userAgent - The user agent of the user's device
   * @returns {Promise<void>}
   * @description Deletes a role by its ID. If the role is not found, it throws an error.
   */
  async deleteRole({ roleId, initiatorId, ipAddress, userAgent }: { roleId: string; initiatorId: string; ipAddress: string; userAgent: string }): Promise<void> {
    try {
      const role = await Role.findByPk(roleId);

      if (!role) {
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.DELETE_RESOURCE,
          module: ModuleEnum.ROLES,
          action_description: 'Error during role deletion: Role not found',
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('Role not found', ErrorCode.NOT_FOUND);
      }

      const transaction = await db.transaction();

      try {
        await RolePermission.destroy({ where: { roleId }, transaction });
        await role.destroy({ transaction });
        await transaction.commit();
      } catch (error) {
        await transaction.rollback();
        logger.error(`An error occured during deletion ${error}`);
      }

      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.DELETE_RESOURCE,
        module: ModuleEnum.ROLES,
        action_description: 'Role deleted successfully',
        ip_address: ipAddress,
        device_info: userAgent,
      });

      logger.info(`Role ${role.name} deleted successfully`);
      return;
    } catch (error) {
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.DELETE_RESOURCE,
        module: ModuleEnum.ROLES,
        action_description: `Error during role deletion: ${error.message}`,
        error_code: ErrorCode.BAD_REQUEST,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error deleting role ${roleId}:`, error);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }
}
