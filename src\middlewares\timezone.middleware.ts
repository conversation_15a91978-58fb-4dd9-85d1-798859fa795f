import { Request, Response, NextFunction } from 'express';
import { DateTime } from 'luxon';
import { Company } from '../modules/admin/models/company.model';
import logger from '../utils/logger';


export function timezoneMiddleware() {
  return async function (req: Request, res: Response, next: NextFunction) {
    let timezone = 'UTC';

    try {
      const company = await Company.findOne();
      const companyTimezone = company?.system_preferences?.timezone;
      if (companyTimezone && DateTime.local().setZone(companyTimezone).isValid) {
        timezone = companyTimezone;
        logger.info(`Using company timezone: ${timezone}`);
      } else {
        logger.warn(`Invalid or missing company timezone: ${companyTimezone}, falling back to UTC`);
      }
    } catch (error) {
      logger.error('[TimezoneMiddleware] Error fetching company:', error);
    }

    // Convert incoming datetime fields to UTC
    if (req.body && hasDateTimeFields(req.body)) {
      convertToUTC(req.body, timezone);
    }

    // Intercept outgoing response
    const originalJson = res.json;
    res.json = (body: any) => {
      if (body && hasDateTimeFields(body)) {
        convertFromUTC(body, timezone);
      }
      return originalJson.call(res, body);
    };

    next();
  };
}

function hasDateTimeFields(payload: any, visited = new Set()): boolean {
  if (typeof payload !== 'object' || payload === null) return false;
  if (visited.has(payload)) return false;

  visited.add(payload);

  if (Array.isArray(payload)) {
    return payload.some(item => hasDateTimeFields(item, visited));
  }

  for (const key of Object.keys(payload)) {
    const value = payload[key];
    if (isDateLike(value)) return true;
    if (typeof value === 'object' && hasDateTimeFields(value, visited)) return true;
  }

  return false;
}

function convertToUTC(payload: any, timezone: string, visited = new Set()) {
  if (typeof payload !== 'object' || payload === null) return;
  if (visited.has(payload)) return;

  visited.add(payload);

  if (Array.isArray(payload)) {
    for (const item of payload) {
      convertToUTC(item, timezone, visited);
    }
    return;
  }

  for (const key of Object.keys(payload)) {
    const value = payload[key];

    if (isDateLike(value)) {
      try {
        const isoString = value instanceof Date ? value.toISOString() : value;
        const dateTime = DateTime.fromISO(isoString, { zone: timezone });
        if (!dateTime.isValid) {
          logger.warn(`[convertToUTC] Invalid date for key ${key}: ${isoString}`);
          return;
        }
        payload[key] = dateTime.toUTC().toISO();
      } catch (error) {
        logger.error(`[convertToUTC] Error converting ${value} for key ${key}:`, error);
      }
    } else if (typeof value === 'object') {
      convertToUTC(value, timezone, visited);
    }
  }
}

function convertFromUTC(payload: any, timezone: string, visited = new Set()) {
  if (typeof payload !== 'object' || payload === null) return;
  if (visited.has(payload)) return;

  visited.add(payload);

  if (Array.isArray(payload)) {
    for (const item of payload) {
      convertFromUTC(item, timezone, visited);
    }
    return;
  }

  for (const key of Object.keys(payload)) {
    const value = payload[key];

    if (value === null || value === undefined) continue;

    if (isDateLike(value)) {
      try {
        const isoString = value instanceof Date ? value.toISOString() : value;
        const dateTime = DateTime.fromISO(isoString, { zone: 'UTC' });

        if (!dateTime.isValid) {
          logger.warn(`[convertFromUTC] Invalid date for key ${key}: ${isoString}`);
          payload[key] = null;
          continue;
        }

        payload[key] = dateTime.setZone(timezone).toISO();
      } catch (error) {
        logger.error(`[convertFromUTC] Error converting ${value} for key ${key}:`, error);
        payload[key] = value; // fallback to original
      }
    } else if (typeof value === 'object') {
      convertFromUTC(value, timezone, visited);
    }
  }
}

function isDateLike(value: any): boolean {
  if (value instanceof Date) {
    return !isNaN(value.getTime());
  }
  if (typeof value === 'string') {
    const parsed = DateTime.fromISO(value, { zone: 'UTC' });
    if (parsed.isValid) {
      return true;
    }
  }
  return false;
}