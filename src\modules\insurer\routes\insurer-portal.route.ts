import { Router } from 'express';
import { validateRequest, validateUUIDParam } from '../../../middlewares/zod-validation.middleware';
import { generateAccessCodeSchema, provideQuotationSchema, verifyAccessCodeSchema } from '../validations/insurer.validation';
import InsurerPortalController from '../controllers/insurer-portal.controller';
import { isInsurerAuthenticated } from '../../../middlewares/insurer.middleware';

const router = Router();
const insurerPortalController = new InsurerPortalController();


router.post(
  '/auth',
  // isInsurerAuthenticated,
  validateRequest(generateAccessCodeSchema),
  insurerPortalController.generateAndSendAccessCode.bind(insurerPortalController)
);

router.post(
  '/verify-access-code',
  // isInsurerAuthenticated,
  validateRequest(verifyAccessCodeSchema),
  insurerPortalController.verifyAccessCode.bind(insurerPortalController)
);

router.get(
  '/broking-slips',
  isInsurerAuthenticated,
  insurerPortalController.listInsurersBrokingSlips.bind(insurerPortalController)
);

router.get(
  '/broking-slips/:brokingSlipId',
  isInsurerAuthenticated,
  validateUUIDParam('brokingSlipId'),
  insurerPortalController.getBrokingSlipById.bind(insurerPortalController)
);

router.post(
  '/quotes',
  isInsurerAuthenticated,
  validateRequest(provideQuotationSchema),
  insurerPortalController.provideQuote.bind(insurerPortalController)
);


export default router;
