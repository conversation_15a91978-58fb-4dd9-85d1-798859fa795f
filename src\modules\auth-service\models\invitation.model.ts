import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  ForeignKey,
} from 'sequelize';

export class Invitation extends Model<
  InferAttributes<Invitation>,
  InferCreationAttributes<Invitation>
> {
  declare id: CreationOptional<string>;
  declare invited_by: ForeignKey<string>;
  declare invitee_email: string;
  declare invite_token: string;
  declare assigned_role_id: ForeignKey<string>;
  declare expires_at: Date;
  declare is_accepted: CreationOptional<boolean>;
  declare createdAt: CreationOptional<Date>;
}

export function initInvitationModel(sequelize: Sequelize): typeof Invitation {
  Invitation.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      invited_by: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      invitee_email: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      invite_token: {
        type: DataTypes.TEXT,
        allowNull: false,
        unique: true,
      },
      assigned_role_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      expires_at: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      is_accepted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      sequelize,
      modelName: 'Invitation',
      tableName: 'invitations',
      updatedAt: false,
      indexes: [
        {
          unique: true,
          fields: ['invite_token'],
        },
      ],
    },
  );

  return Invitation;
}
