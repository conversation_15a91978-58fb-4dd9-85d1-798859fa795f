import {
  CreationOptional,
  DataTypes,
  ForeignKey,
  InferAttributes,
  InferCreationAttributes,
  Model,
  Sequelize
} from 'sequelize';
import { BrokingSlip } from '../../broking-slip/models/broking-slip.model';
import { Insurer } from './insurer.model';
import { ContactList } from './contact-list.model';

export class InsurerBrokingSlips extends Model<
  InferAttributes<InsurerBrokingSlips>,
  InferCreationAttributes<InsurerBrokingSlips>
> {
  declare id: CreationOptional<string>;
  declare broking_slip_id: ForeignKey<string>;
  declare insurer_id: ForeignKey<string>;
  declare contact_id: ForeignKey<string>;
  declare updatedAt?: Date;
  declare createdAt?: Date;
}


export function initInsurerBrokingSlipsModel(sequelize: Sequelize): typeof InsurerBrokingSlips {
  InsurerBrokingSlips.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      broking_slip_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: BrokingSlip,
          key: 'id',
        },
        onDelete: 'CASCADE'
      },
      insurer_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: Insurer,
          key: 'id',
        },
        onDelete: 'CASCADE'
      },
      contact_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: ContactList,
          key: 'id',
        },
        onDelete: 'CASCADE'
      },
    },
    {
      sequelize,
      modelName: 'InsurerBrokingSlips',
      tableName: 'insurer_broking_slips',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          unique: true,
          fields: ['broking_slip_id', 'contact_id', 'insurer_id'], // composite constraint
          name: 'uniq_broking_contact_insurer',
        },
      ],
    },
  );

  return InsurerBrokingSlips;
}