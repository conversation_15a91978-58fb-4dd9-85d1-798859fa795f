import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';

export class Company extends Model<
  InferAttributes<Company>,
  InferCreationAttributes<Company>
> {
  declare id: CreationOptional<string>;
  declare security_policy?: Record<string, any>;
  declare general_parameters?: Record<string, any>;
  declare system_preferences?: Record<string, any>;
  declare createdAt?: Date;
  declare updatedAt?: Date;
}

export function initCompanyModel(sequelize: Sequelize): typeof Company {
  Company.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      security_policy: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      general_parameters: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      system_preferences: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: 'Company',
      tableName: 'companies',
      timestamps: true,
      paranoid: true,
    },
  );

  return Company;
}
