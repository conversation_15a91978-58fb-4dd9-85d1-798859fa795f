import { Request, Response } from 'express';
import { RequestWithAuditMetadata } from '../../../interfaces/custom-request.interface';
import InsurerService from '../services/insurer.service';
import { PaginationDto } from '../../../utils/pagination';
import { InsurerFilter } from '../../../interfaces/insurer-filter.interface';
import { InsurerStatus } from '../../../enum/insurer-status.enum';

/**
 * InsurerController class
 */
export default class InsurerController {
  private insurerService: InsurerService;

  constructor() {
    this.insurerService = new InsurerService();
  }

  /**
   * Creates a new insurer with the provided data.
   */
  async createInsurer(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const data = req.body;

    const response = await this.insurerService.createInsurer(userId, ipAddress, userAgent, data);
    return res.status(201).json({ data: response, message: 'Insurer created successfully' });
  }

  /**
   * Lists all insurers with optional filtering and pagination.
   */
  async listInsurers(req: Request, res: Response): Promise<Response> {
    const paginationDto = new PaginationDto(req.query);
    const searchQuery = req.query.searchQuery as string;
    const filter: InsurerFilter = {
      ...(req.query.status && { status: req.query.status as InsurerStatus }),
      ...(req.query.startDate && { startDate: new Date(req.query.startDate as string) }),
      ...(req.query.endDate && { endDate: new Date(req.query.endDate as string) }),
      ...(req.query.name && { name: req.query.name as string }),
    };

    const result = await this.insurerService.listInsurers(paginationDto, searchQuery, filter);
    return res.status(200).json({ data: result, message: 'Insurers retrieved successfully' });
  }

  /**
   * Retrieves an insurer by its ID.
   */
  async getInsurerById(req: Request, res: Response): Promise<Response> {
    const { id } = req.params;

    const response = await this.insurerService.getInsurerById(id);
    return res.status(200).json({ data: response, message: 'Insurer retrieved successfully' });
  }

  /**
   * Updates an insurer by its ID.
   */
  async updateInsurer(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { id } = req.params;
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const data = req.body;

    const response = await this.insurerService.updateInsurer(id, userId, ipAddress, userAgent, data);
    return res.status(200).json({ data: response, message: 'Insurer updated successfully' });
  }

  /**
   * Deletes an insurer by its ID.
   */
  async deleteInsurer(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { id } = req.params;
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;

    await this.insurerService.deleteInsurer(id, userId, ipAddress, userAgent);
    return res.status(200).json({ message: 'Insurer deleted successfully' });
  }

  /**
   * Creates a new contact for an insurer.
   */
  async addContactToInsurer(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const { insurerId } = req.params;
    const data = req.body;

    const response = await this.insurerService.addContactToInsurer(
      userId,
      ipAddress,
      userAgent,
      insurerId,
      data
    );
    return res.status(201).json({ data: response, message: 'Contact created successfully' });
  }

  /**
   * Retrieves all contacts for a specific insurer.
   */
  async getContactsByInsurer(req: Request, res: Response): Promise<Response> {
    const { insurerId } = req.params;
    const paginationDto = new PaginationDto(req.query);

    const response = await this.insurerService.listContactsByInsurerId(insurerId, paginationDto);
    return res.status(200).json({ data: response, message: 'Contacts retrieved successfully' });
  }

  /**
   * Updates a contact for a specific insurer.
   */
  async updateContact(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { id, insurerId } = req.params;
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const data = req.body;

    const response = await this.insurerService.updateContact(
      userId,
      ipAddress,
      userAgent,
      id,
      insurerId,
      data
    );
    return res.status(200).json({ data: response, message: 'Contact updated successfully' });
  }

  /**
   * Deletes a contact for a specific insurer.
   */
  async deleteContact(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { id } = req.params;
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;

    await this.insurerService.deleteContact(userId, ipAddress, userAgent, id);
    return res.status(200).json({ message: 'Contact deleted successfully' });
  }
}