import teamRoutes from '../modules/team/routes/team.router';
import adminRoutes from '../modules/admin/routes/admin.route';
import clientRoutes from '../modules/clients/routes/clients.route';
import authRoutes from '../modules/auth-service/routes/auth.routes';
// import rolesRoutes from '../modules/roles-service/routes/roles.router';
import fileRoutes from '../modules/file/routes/file.route';
import policiesRoutes from '../modules/policy/routes/policy.routes';
import brokingSlipRoute from '../modules/broking-slip/routes/broking-slip.route';
import insurerRoutes from '../modules/insurer/routes/insurer.route';
import insurerPortalRoutes from '../modules/insurer/routes/insurer-portal.route';
import quotationRoutes from '../modules/quotation/routes/quotation.routes';

import { Router } from 'express';

const router = Router();

const defaultRoutes = [
  {
    path: '/auth',
    route: authRoutes,
  },
  {
    path: '/admin',
    route: adminRoutes,
  },
  {
    path: '/teams',
    route: teamRoutes,
  },
  {
    path: '/clients',
    route: clientRoutes
  },
  {
    path: '/files',
    route: fileRoutes
  },
  {
    path: '/policies',
    route: policiesRoutes,
  },
  {
    path: '/broking-slips',
    route: brokingSlipRoute
  },
  {
    path: '/insurers',
    route: insurerRoutes
  },
  {
    path: '/insurer-portal',
    route: insurerPortalRoutes
  },
  {
    path: '/quotations',
    route: quotationRoutes
  },
];

defaultRoutes.forEach((route) => {
  router.use(route.path, route.route);
});

export default router;
