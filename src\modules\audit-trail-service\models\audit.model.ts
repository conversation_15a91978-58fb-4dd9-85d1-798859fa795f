// src/models/AuditLog.ts
import {
  Model,
  DataTypes,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  Sequelize,
} from 'sequelize';
import { ModuleAction } from '../../../enum/module-action';
import { AuditLogEnum } from '../../../enum/trail-action.enum';

export class AuditLog extends Model<InferAttributes<AuditLog>, InferCreationAttributes<AuditLog>> {
  declare id: CreationOptional<number>;
  declare user_id: string;
  declare event_type: string;
  declare action_description?: string;
  declare error_code?: string;
  declare module: string;
  declare entity_id: string;
  declare ip_address?: string;
  declare device_info?: string;
  declare created_at: Date;
}

export function initAuditLogModel(sequelize: Sequelize): typeof AuditLog {
  AuditLog.init(
    {
      id: { type: DataTypes.UUID, defaultValue: DataTypes.UUIDV4, primaryKey: true },
      user_id: { type: DataTypes.UUID, allowNull: true },
      event_type: { type: DataTypes.STRING, allowNull: false, defaultValue: ModuleAction.READ },
      module: { type: DataTypes.STRING, allowNull: false, defaultValue: AuditLogEnum.OTHER },
      entity_id: { type: DataTypes.UUID, allowNull: true },
      action_description: { type: DataTypes.STRING, allowNull: true },
      error_code: { type: DataTypes.STRING, allowNull: true },
      created_at: { type: DataTypes.DATE, defaultValue: DataTypes.NOW },
      ip_address: { type: DataTypes.STRING, allowNull: true },
      device_info: { type: DataTypes.STRING, allowNull: true },
    },
    {
      sequelize,
      modelName: 'AuditLog',
      tableName: 'audit_logs',
      timestamps: false,
      indexes: [
        {
          unique: false,
          fields: ['user_id'],
        },
        {
          unique: false,
          fields: ['event_type'],
        },
        {
          unique: false,
          fields: ['module'],
        },
        {
          unique: false,
          fields: ['created_at'],
        },
      ],
    },
  );

  return AuditLog;
}
