{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "target": "ES2017", "sourceMap": true, "outDir": "./dist", "baseUrl": ".", "paths": {"src/*": ["./src/*"], "*": ["node_modules/*"]}, "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "esModuleInterop": true}, "include": ["src/**/*", "tests/**/*", "src/types/express.d.ts"]}