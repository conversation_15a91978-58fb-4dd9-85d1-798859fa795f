import { literal, Op, Sequelize } from 'sequelize';
import Decimal from 'decimal.js';
import { BrokingSlip } from '../models/broking-slip.model';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { User } from '../../auth-service/models/user.model';
import { AuditLogEnum, ErrorCode } from '../../../enum/trail-action.enum';
import { ModuleEnum } from '../../../enum/module.enum';
import logger from '../../../utils/logger';
import { Clients } from '../../clients/models/clients.model';
import { Policy } from '../../policy/models/policy.model';
import cacheService from '../../../utils/cache.service';
import { PaginationDto, PaginationMetadataDto, PaginationResultDto } from '../../../utils/pagination';
import { CacheKeys } from '../../../enum/cache-keys.enum';
import { AppError } from '../../../utils/custom-error';
import { IBrokingSlip, SendBrokingSlipToInsurerDTO } from '../validations/broking-slip.schema';
import { rabbitMQService } from '../../workers/rabbitmq/rabbitmq.service';
import { EMAIL_QUEUE } from '../../../constants/role-permission.constant';
import { BrokingSlipStatusEnum } from '../../../enum/broking-slip-status.enum';
import { Insurer } from '../../insurer/models/insurer.model';
import { ContactList } from '../../insurer/models/contact-list.model';
import { InsurerStatus } from '../../../enum/insurer-status.enum';
import { db } from '../../../database/db';
import { Slip } from '../models/slip.model';
import { Category } from '../../policy/models/policy-category.model';
// import { config } from '../../../config/env.config';
import { InsurerBrokingSlips } from '../../insurer/models/insurer-brokingSlips.model';

/**
 * BrokingSlipService handles operations related to broking slips,
 */
export default class BrokingSlipService {
  private readonly messageQueue = rabbitMQService;

  /**
 * Helper to format slip_id from a serial number and user initials
 * Format: BS/XX/001
 */
  formatSlipId(serialNumber: number, firstName: string, lastName: string): string {
    const initials = `${firstName.charAt(0)}${lastName?.charAt(0)}`.toUpperCase();
    return `BS/${initials}/${String(serialNumber).padStart(3, '0')}`;
  }

  /**
 * Creates a new broking slip (with one or more child slips) and logs the audit event.
 *
 * @param initiatorId - ID of the user initiating the request
 * @param ipAddress - IP address of the request origin
 * @param userAgent - User agent string from the request
 * @param data - Payload containing broking slip details (with array of slips inside)
 * @returns The newly created broking slip instance
 * @throws Error if creation or logging fails
 */
  async generateBrokingSlip(
    initiatorId: string,
    ipAddress: string,
    userAgent: string,
    data: IBrokingSlip
  ): Promise<BrokingSlip> {
    const { slips } = data;
    const user = await User.findByPk(initiatorId);
    if (!user) throw new AppError(`Initiator with ID ${initiatorId} not found`, ErrorCode.NOT_FOUND);

    const transaction = await db.transaction();
    const client = await Clients.findByPk(data.clientId, { transaction });
    if (!client) throw new AppError(`Client with ID ${data.clientId} not found`, ErrorCode.NOT_FOUND);

    try {
      await db.query('LOCK TABLE "broking_slip" IN EXCLUSIVE MODE', { transaction });
      let maxSerialNumber: number = (await BrokingSlip.max('serial_number', { transaction })) as number;
      maxSerialNumber = typeof maxSerialNumber === 'number' && !isNaN(maxSerialNumber) ? maxSerialNumber : 0;
      const nextSerialNumber = maxSerialNumber + 1;
      const baseSlipId = this.formatSlipId(nextSerialNumber, user.first_name, user.last_name);
      const preparedSlips: any[] = [];
      let cumulativeSumInsured = new Decimal(0);
      let cumulativeGrossPremium = new Decimal(0);
      const categoryUpdates: Promise<any>[] = [];

      const newBrokingSlip = await BrokingSlip.create(
        {
          clientId: client.id,
          created_by: initiatorId,
          serial_number: nextSerialNumber,
          slip_id: baseSlipId,
          coverage_period: data.coverage_period,
          coverage_time: data.coverage_time,
          cumulative_sum_insured: cumulativeSumInsured.toFixed(2),
          cumulative_gross_premium: cumulativeGrossPremium.toFixed(2),
        },
        { transaction }
      );

      // Prepare slips array for embedding inside one BrokingSlip row
      for (const slipData of slips) {
        const policy = await Policy.findByPk(slipData.policyId, { transaction });
        if (!policy) throw new AppError(`Policy with ID ${slipData.policyId} not found`, ErrorCode.NOT_FOUND);

        const sumInsured = new Decimal(
          (slipData.schedule?.data ?? []).reduce((total, item) => total + Number(item.value ?? 0), 0)
        );
        const baseRate = new Decimal(slipData.base_rate || 0);
        const grossPremium = sumInsured.mul(baseRate.div(100)).toDecimalPlaces(2);
        const totalDiscounts = new Decimal(
          slipData.discounts?.filter(d => d.enabled).reduce((sum, d) => sum + Number(d.value || 0), 0) || 0
        );
        const totalLoadings = new Decimal(
          slipData.loadings?.filter(l => l.enabled).reduce((sum, l) => sum + Number(l.value || 0), 0) || 0
        );
        const discountAmount = grossPremium.mul(totalDiscounts.div(100)).toDecimalPlaces(2);
        const loadingAmount = grossPremium.mul(totalLoadings.div(100)).toDecimalPlaces(2);

        const adjustedPremium = grossPremium.minus(discountAmount).plus(loadingAmount).toDecimalPlaces(2);

        cumulativeSumInsured = cumulativeSumInsured.plus(sumInsured);
        cumulativeGrossPremium = cumulativeGrossPremium.plus(adjustedPremium);
        const brokerCommission = adjustedPremium
          .mul(new Decimal(slipData.broker_commission || 0).div(100))
          .toDecimalPlaces(2);

        const tax = adjustedPremium.mul(0.075).toDecimalPlaces(2);
        const netPremium = adjustedPremium.minus(brokerCommission).minus(tax).toDecimalPlaces(2);

        const safeSections = slipData.sections?.map(section => ({
          name: section.name ?? '',
          data: section.data?.map(item => ({
            name: item.name ?? '',
            label: item.label ?? '',
            value: item.value ?? '',
          })) ?? [],
        })) ?? [];

        const safeSchedule = {
          name: slipData.schedule?.name ?? '',
          data:
            slipData.schedule?.data?.map(item => ({
              ...item,
              name: item.name ?? '',
              value: Number(item.value ?? 0),
            })) ?? [],
        };

        const safeExtensions = slipData.extensions?.map(ext => ({
          name: ext.name ?? '',
          value: Number(ext.value ?? 0),
        }));

        const newSlip = await Slip.create(
          {
            brokingSlipId: newBrokingSlip.id,
            policyId: slipData.policyId,
            summary_of_cover: slipData.summary_of_cover,
            policy_category: slipData.policy_category,
            sections: safeSections,
            schedule: safeSchedule,
            extensions: safeExtensions,
            order: slipData.order || [],
            discounts: (slipData.discounts ?? []).map(d => ({
              name: d.name ?? '',
              value: Number(d.value ?? 0),
              enabled: Boolean(d.enabled),
            })),
            loadings: (slipData.loadings ?? []).map(l => ({
              name: l.name ?? '',
              value: Number(l.value ?? 0),
              enabled: Boolean(l.enabled),
            })),
            base_rate: slipData.base_rate,
            broker_commission: slipData.broker_commission,
            sum_insured: sumInsured.toFixed(2),
            gross_premium: adjustedPremium.toFixed(2),
            net_premium: netPremium.toFixed(2),
            supporting_documents: slipData.supporting_documents ?? [],
          },
          { transaction }
        );
        categoryUpdates.push(
          Category.update(
            {
              summary_of_cover: slipData.summary_of_cover,
              extensions: safeExtensions,
            },
            {
              where: {
                name: slipData.policy_category,
                policy_id: slipData.policyId
              },
              transaction
            }
          ),
        );

        preparedSlips.push(newSlip.id);
      }

      await Promise.all([
        ...categoryUpdates,
        newBrokingSlip.update(
          {
            cumulative_sum_insured: cumulativeSumInsured.toFixed(2),
            cumulative_gross_premium: cumulativeGrossPremium.toFixed(2),
          },
          { transaction }
        ),
      ]);

      await transaction.commit();

      await Promise.all([
        cacheService.invalidateByPattern(`${CacheKeys.BROKING_SLIP_LIST}:*`),
        cacheService.invalidateByPattern(`${CacheKeys.BROKING_SLIP_DASHBOARD}:*`),
      ]);

      const brokingSlipWithRelations = await BrokingSlip.findByPk(newBrokingSlip.id, {
        include: [
          {
            model: Slip,
            as: 'slips',
            include: [
              {
                model: Policy,
                as: 'policy',
              }
            ]
          }
        ]
      });
      AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.BROKING_SLIP,
        action_description: `${user.first_name} ${user.last_name} generated ${preparedSlips.length} slip(s) in BrokingSlip ${newBrokingSlip.id} for client ${client.id}.`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      return brokingSlipWithRelations;
    } catch (error) {
      await transaction.rollback();
      logger.error(`Error generating broking slip: ${error}`);
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.BROKING_SLIP,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        action_description: `Failed to generate broking slip. Error: ${error.message}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
 * List all broking slips with pagination, optional search and status filter
 * @param paginationDto - Pagination configuration (page, limit, order)
 * @param searchQuery - Optional search string to filter by slip_id
 * @param status - Optional filter for slip status
 * @returns {Promise<PaginationResultDto<BrokingSlip>>} Paginated and filtered list of broking slips
 * @throws {Error} If fetching broking slips fails
 */
  async listBrokingSlips(
    paginationDto: PaginationDto,
    searchQuery?: string,
    status?: string
  ): Promise<PaginationResultDto<BrokingSlip>> {
    try {
      const { order, limit, skip } = paginationDto;

      const cacheKey = cacheService.generateKey(CacheKeys.BROKING_SLIP_LIST, {
        pagination: paginationDto,
        searchQuery,
        status,
      });
      const cached = await cacheService.get<PaginationResultDto<BrokingSlip>>(cacheKey);

      if (cached) {
        return new PaginationResultDto(cached.data, cached.meta);
      };

      const whereClause: any = {};

      if (searchQuery) {
        whereClause[Op.or] = [
          { slip_id: { [Op.iLike]: `%${searchQuery}%` } },
          literal(`"client"."first_name" ILIKE '%${searchQuery}%'`),
          literal(`"client"."last_name" ILIKE '%${searchQuery}%'`)
        ];
      }

      if (status) {
        whereClause.status = status;
      }

      const { count, rows } = await BrokingSlip.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Clients,
            as: 'client',
            attributes: ['id', 'first_name', 'last_name']
          },
          {
            model: User,
            as: 'creator',
            attributes: {
              exclude: ['password']
            }
          },
          {
            model: Slip,
            as: 'slips',
            include: [
              {
                model: Policy,
                as: 'policy',
              }
            ]
          }
        ],
        limit,
        offset: skip,
        order: [['createdAt', order]],
      });
      const metadata = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });
      const result = new PaginationResultDto(rows, metadata);

      await cacheService.set(cacheKey, result);

      return result;
    } catch (error) {
      logger.error(`Failed to fetch broking slips: ${error.message}`);
      throw new AppError('Failed to fetch broking slips');
    }
  }


  /**
 * Retrieves a broking slip by its ID, including related client and creator.
 *
 * @param id - ID of the broking slip to retrieve
 * @returns The broking slip instance
 * @throws Error if not found or retrieval fails
 */
  async getBrokingSlip(id: string) {
    try {
      const brokingSlip = await BrokingSlip.findByPk(id, {
        include: [
          { model: Clients, as: 'client' },
          {
            model: User,
            as: 'creator',
            attributes: { exclude: ['password'] }
          },
          {
            model: Slip,
            as: 'slips',
            include: [
              {
                model: Policy,
                as: 'policy'
              }
            ]
          }
        ]
      });

      if (!brokingSlip) {
        logger.error('Broking slip not found');
        throw new AppError('Broking slip not found', ErrorCode.NOT_FOUND);
      }

      return brokingSlip;
    } catch (error) {
      logger.error('Error getting broking slip by ID', error);
      throw new AppError(
        error.message || 'Internal server error',
        error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR
      );
    }
  }


  //   /**
  //  * Updates an existing broking slip by its ID and logs the audit event.
  //  *
  //  * @param initiatorId - ID of the user performing the update
  //  * @param ipAddress - IP address of the request origin
  //  * @param userAgent - User agent string from the request
  //  * @param id - ID of the broking slip to update
  //  * @param updateData - Fields to update (excluding clientId)
  //  * @returns The updated broking slip instance
  //  * @throws Error if update fails or broking slip is not found
  //  */

  // async updateBrokingSlip(
  //   initiatorId: string,
  //   ipAddress: string,
  //   userAgent: string,
  //   id: string,
  //   updateData: Partial<BrokingSlip & { slips?: Partial<Slip>[] }>
  // ) {
  //   const user = await User.findByPk(initiatorId);

  //   try {
  //     const brokingSlip = await BrokingSlip.findByPk(id, { include: [Slip] });

  //     if (!brokingSlip) {
  //       await AuditLogService.logEvent({
  //         userId: initiatorId,
  //         eventType: AuditLogEnum.UPDATE_RESOURCE,
  //         module: ModuleEnum.BROKING_SLIP,
  //         error_code: ErrorCode.NOT_FOUND,
  //         action_description: `Broking slip ${id} not found during update on ${new Date()}`,
  //         ip_address: ipAddress,
  //         device_info: userAgent,
  //       });
  //       throw new AppError('Broking slip not found', ErrorCode.NOT_FOUND);
  //     }

  //     // Log status change if provided
  //     if (updateData.status && updateData.status !== brokingSlip.status) {
  //       await AuditLogService.logEvent({
  //         userId: initiatorId,
  //         eventType: AuditLogEnum.UPDATE_RESOURCE,
  //         module: ModuleEnum.BROKING_SLIP,
  //         action_description: `Status of slip ${brokingSlip.slip_id} changed from '${brokingSlip.status}' to '${updateData.status}' by ${user.first_name} ${user.last_name} on ${new Date()}`,
  //         ip_address: ipAddress,
  //         device_info: userAgent,
  //       });
  //     }

  //     let cumulativeSumInsured = new Decimal(0);
  //     let cumulativeGrossPremium = new Decimal(0);

  //     // Update each Slip individually if provided
  //     if (updateData.slips && updateData.slips.length) {
  //       for (const slipItem of updateData.slips) {
  //         const existingSlip = await Slip.findOne({
  //           where: { id: slipItem.id, brokingSlipId: brokingSlip.id },
  //         });

  //         if (!existingSlip) continue;

  //         // Normalize sections, schedule, extensions, discounts, loadings
  //         const safeSections = (slipItem.sections ?? []).map(section => ({
  //           name: section.name ?? '',
  //           data: (section.data ?? []).map(item => ({
  //             name: item.name ?? '',
  //             label: item.label ?? '',
  //             value: item.value ?? '',
  //           })),
  //         }));

  //         const safeSchedule = slipItem.schedule
  //           ? {
  //             name: slipItem.schedule.name ?? '',
  //             data: (slipItem.schedule.data ?? []).map(item => ({
  //               ...item,
  //               name: item.name ?? '',
  //               value: Number(item.value ?? 0),
  //             })),
  //           }
  //           : slipItem.schedule;

  //         const safeExtensions = (slipItem.extensions ?? []).map(ext => ({
  //           name: ext.name ?? '',
  //           value: Number(ext.value ?? 0),
  //         }));

  //         const safeDiscounts = (slipItem.discounts ?? []).map(d => ({
  //           name: d.name ?? '',
  //           value: Number(d.value ?? 0),
  //           enabled: Boolean(d.enabled),
  //         }));

  //         const safeLoadings = (slipItem.loadings ?? []).map(l => ({
  //           name: l.name ?? '',
  //           value: Number(l.value ?? 0),
  //           enabled: Boolean(l.enabled),
  //         }));

  //         const newSupportingDocuments = slipItem.supporting_documents ?? [];

  //         // Recalculate sum_insured and gross_premium
  //         let newSumInsured = new Decimal(0);
  //         let newGrossPremium = new Decimal(0);

  //         if (safeSchedule?.data?.length) {
  //           newSumInsured = safeSchedule.data.reduce<Decimal>(
  //             (sum, item) => sum.plus(new Decimal(item.value ?? 0)),
  //             new Decimal(0)
  //           );

  //           const rate = new Decimal(slipItem.base_rate ?? 0);
  //           const grossPremium = newSumInsured.times(rate.dividedBy(100));

  //           const totalDiscounts = safeDiscounts
  //             .filter(d => d.enabled)
  //             .reduce<Decimal>((sum, d) => sum.plus(new Decimal(d.value)), new Decimal(0));

  //           const totalLoadings = safeLoadings
  //             .filter(l => l.enabled)
  //             .reduce<Decimal>((sum, l) => sum.plus(new Decimal(l.value)), new Decimal(0));

  //           newGrossPremium = grossPremium.minus(totalDiscounts).plus(totalLoadings);
  //         }

  //         // Update Slip in DB
  //         await existingSlip.update({
  //           ...slipItem,
  //           sections: safeSections,
  //           schedule: safeSchedule,
  //           extensions: safeExtensions,
  //           discounts: safeDiscounts,
  //           loadings: safeLoadings,
  //           supporting_documents: newSupportingDocuments,
  //           sum_insured: newSumInsured.toFixed(2),
  //           gross_premium: newGrossPremium.toFixed(2),
  //         });

  //         cumulativeSumInsured = cumulativeSumInsured.plus(newSumInsured);
  //         cumulativeGrossPremium = cumulativeGrossPremium.plus(newGrossPremium);
  //       }
  //     } else {
  //       // If slips not updated, recalc cumulative totals from DB
  //       const allSlips = await Slip.findAll({ where: { brokingSlipId: brokingSlip.id } });
  //       for (const s of allSlips) {
  //         cumulativeSumInsured = cumulativeSumInsured.plus(new Decimal(s.sum_insured ?? 0));
  //         cumulativeGrossPremium = cumulativeGrossPremium.plus(new Decimal(s.gross_premium ?? 0));
  //       }
  //     }

  //     // Update BrokingSlip
  //     await brokingSlip.update({
  //       status: updateData.status ?? brokingSlip.status,
  //       cumulative_sum_insured: cumulativeSumInsured.toFixed(2),
  //       cumulative_gross_premium: cumulativeGrossPremium.toFixed(2),
  //     });

  //     await cacheService.invalidateByPattern(`${CacheKeys.BROKING_SLIP_LIST}:*`);

  //     return brokingSlip;
  //   } catch (error) {
  //     logger.error(`Error updating broking slip ${id}: ${error.message}`);

  //     await AuditLogService.logEvent({
  //       userId: initiatorId,
  //       eventType: AuditLogEnum.UPDATE_RESOURCE,
  //       module: ModuleEnum.BROKING_SLIP,
  //       error_code: ErrorCode.INTERNAL_SERVER_ERROR,
  //       action_description: `Failed to update broking slip ${id} on ${new Date()}`,
  //       ip_address: ipAddress,
  //       device_info: userAgent,
  //     });

  //     throw new AppError(
  //       error.message || 'Internal server error',
  //       error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR
  //     );
  //   }
  // }
  /**
   * Updates the status of a broking slip and returns the full broking slip structure.
   *
   * This method updates the `status` field of the broking slip. It returns the broking slip
   * with its associated slips, policies, client, and creator.
   *
   * @param initiatorId - The ID of the user initiating the update.
   * @param ipAddress - The IP address of the user making the request.
   * @param userAgent - The device/user-agent string of the client.
   * @param id - The ID of the broking slip to update.
   * @param status - The new status to set for the broking slip.
   *
   * @throws {AppError} Throws a 404 AppError if the broking slip does not exist.
   * @throws {AppError} Throws a 500 AppError if an internal server error occurs.
   *
   * @returns The updated broking slip instance with relations.
   *
   * @example
   * const updatedSlip = await updateBrokingSlip(
   *   'user-123',
   *   '***********',
   *   'Mozilla/5.0',
   *   'slip-456',
   *   BrokingSlipStatusEnum.APPROVED
   * );
   */
  async updateBrokingSlip(
    initiatorId: string,
    ipAddress: string,
    userAgent: string,
    id: string,
    status: BrokingSlipStatusEnum
  ) {
    const user = await User.findByPk(initiatorId);

    try {
      const brokingSlip = await BrokingSlip.findByPk(id);

      if (!brokingSlip) {
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.BROKING_SLIP,
          error_code: ErrorCode.NOT_FOUND,
          action_description: `Broking slip ${id} not found during update on ${new Date()}`,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('Broking slip not found', ErrorCode.NOT_FOUND);
      }

      // Only update and log if status is actually changing
      if (status && status !== brokingSlip.status) {
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.BROKING_SLIP,
          action_description: `Status of slip ${brokingSlip.id} changed from '${brokingSlip.status}' to '${status}' by ${user.first_name} ${user.last_name} on ${new Date()}`,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        await brokingSlip.update({ status });
      }

      // Fetch the updated broking slip with all relations
      const updatedSlipWithRelations = await BrokingSlip.findByPk(id, {
        include: [
          {
            model: Clients,
            as: 'client',
            attributes: ['id', 'first_name', 'last_name'],
          },
          {
            model: User,
            as: 'creator',
            attributes: { exclude: ['password'] },
          },
          {
            model: Slip,
            as: 'slips',
            include: [
              {
                model: Policy,
                as: 'policy',
              },
            ],
          },
        ],
      });

      return updatedSlipWithRelations;
    } catch (error) {
      logger.error(`Error updating broking slip ${id}: ${error.message}`);

      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.BROKING_SLIP,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        action_description: `Failed to update broking slip ${id} on ${new Date()}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      throw new AppError(
        error.message || 'Internal server error',
        error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Broking Slip Dashboard data
   * @returns Dashboard data
   */
  async brokingSlipDashboardData(): Promise<{
    totalSlips: number;
    slipsThisMonth: number;
    draftSlips: number;
    draftSlipsThisMonth: number;
    rejectedSlips: number;
    rejectedSlipsThisMonth: number;
    approvedSlips: number;
    approvedSlipsThisMonth: number;
  }> {
    const cacheKey = cacheService.generateKey(CacheKeys.BROKING_SLIP_DASHBOARD, {});
    const cached = await cacheService.get<{
      totalSlips: number;
      slipsThisMonth: number;
      draftSlips: number;
      draftSlipsThisMonth: number;
      rejectedSlips: number;
      rejectedSlipsThisMonth: number;
      approvedSlips: number;
      approvedSlipsThisMonth: number;
    }>(cacheKey);
    if (cached) return cached;

    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    const result = await BrokingSlip.findAll({
      attributes: [
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalSlips'],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal(`CASE WHEN "createdAt" BETWEEN '${startOfMonth.toISOString()}' AND '${endOfMonth.toISOString()}' THEN 1 END`)
          ),
          'slipsThisMonth',
        ],
        [
          Sequelize.fn('COUNT', Sequelize.literal('CASE WHEN status = \'draft\' THEN 1 END')),
          'draftSlips',
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal(`CASE WHEN status = 'draft' AND "createdAt" BETWEEN '${startOfMonth.toISOString()}' AND '${endOfMonth.toISOString()}' THEN 1 END`)
          ),
          'draftSlipsThisMonth',
        ],
        [
          Sequelize.fn('COUNT', Sequelize.literal('CASE WHEN status = \'rejected\' THEN 1 END')),
          'rejectedSlips',
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal(`CASE WHEN status = 'rejected' AND "createdAt" BETWEEN '${startOfMonth.toISOString()}' AND '${endOfMonth.toISOString()}' THEN 1 END`)
          ),
          'rejectedSlipsThisMonth',
        ],
        [
          Sequelize.fn('COUNT', Sequelize.literal('CASE WHEN status = \'approved\' THEN 1 END')),
          'approvedSlips',
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal(`CASE WHEN status = 'approved' AND "createdAt" BETWEEN '${startOfMonth.toISOString()}' AND '${endOfMonth.toISOString()}' THEN 1 END`)
          ),
          'approvedSlipsThisMonth',
        ],
      ],
      raw: true,
    });

    const parsed = {
      totalSlips: Number(result[0]['totalSlips'] ?? 0),
      slipsThisMonth: Number(result[0]['slipsThisMonth'] ?? 0),
      draftSlips: Number(result[0]['draftSlips'] ?? 0),
      draftSlipsThisMonth: Number(result[0]['draftSlipsThisMonth'] ?? 0),
      rejectedSlips: Number(result[0]['rejectedSlips'] ?? 0),
      rejectedSlipsThisMonth: Number(result[0]['rejectedSlipsThisMonth'] ?? 0),
      approvedSlips: Number(result[0]['approvedSlips'] ?? 0),
      approvedSlipsThisMonth: Number(result[0]['approvedSlipsThisMonth'] ?? 0),
    };

    await cacheService.set(cacheKey, parsed, 2 * 60 * 60);

    return parsed;
  }

  /**
   * Send Broking slip to insurer(s)
   * @param initiatorId - Id of user making request
   * @param ipAddress - IP Address
   * @param userAgent - Device Info
   * @param data - payload.
   * @returns 
   */
  async sendBrokingSlipToInsurer(
    initiatorId: string,
    ipAddress: string,
    userAgent: string,
    data: SendBrokingSlipToInsurerDTO
  ): Promise<BrokingSlip> {
    const [user, brokingSlip] = await Promise.all([
      User.findByPk(initiatorId),
      BrokingSlip.findByPk(data.brokingSlipId)
    ]);

    if (!brokingSlip) {
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.BROKING_SLIP,
        error_code: ErrorCode.NOT_FOUND,
        action_description: `Broking slip ${data.brokingSlipId} not found during send to insurer`,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      throw new AppError('Broking slip not found', ErrorCode.NOT_FOUND);
    }

    const results = await Promise.allSettled(
      data.recipients.map(async (recipient) => {
        const { insurer_id, contact_ids, note } = recipient;

        try {
          const insurer = await Insurer.findByPk(insurer_id);
          if (!insurer || insurer.status !== InsurerStatus.ACTIVE) {
            throw new AppError(`Insurer with ID ${insurer_id} not found or inactive`, ErrorCode.BAD_REQUEST);
          }

          const contacts = await ContactList.findAll({
            where: {
              id: { [Op.in]: contact_ids },
              insurerId: insurer_id,
              status: InsurerStatus.ACTIVE
            }
          });

          const foundContactIds = contacts.map(c => c.id);
          const missingOrMismatched = contact_ids.filter(id => !foundContactIds.includes(id));

          if (missingOrMismatched.length > 0) {
            logger.warn(
              `Some contact IDs for insurer "${insurer.name}" were missing or mismatched: ${missingOrMismatched.join(', ')}`
            );
            await AuditLogService.logEvent({
              userId: initiatorId,
              eventType: AuditLogEnum.UPDATE_RESOURCE,
              module: ModuleEnum.BROKING_SLIP,
              action_description: `Partial contact mismatch while sending broking slip ${brokingSlip.slip_id} to insurer "${insurer.name}". Missing/mismatched contact IDs: ${missingOrMismatched.join(', ')}`,
              ip_address: ipAddress,
              device_info: userAgent,
            });
          }

          if (contacts.length === 0) {
            throw new Error(`No valid contacts found for insurer ${insurer.name}`);
          }

          await Promise.all(
            contacts.map((contact) => {
              InsurerBrokingSlips.upsert({
                broking_slip_id: brokingSlip.id,
                insurer_id,
                contact_id: contact.id,
              });
            })
          );

          const emailPromises = contacts.map((contact) => {
            // const link = `${config.FRONTEND_URL[0]}/login?callbackUrl=/broking-slip/${brokingSlip.id}&contactId=${contact.id}`;
            const link = `http://localhost:3000/broking-slips/${brokingSlip.id}?contactId=${contact.id}`;

            return this.messageQueue.sendToQueue(EMAIL_QUEUE, {
              to: contact.email,
              subject: `Broking Slip: ${brokingSlip.slip_id}`,
              templateName: 'send-broking-slip.html',
              data: {
                insurerName: insurer.name,
                brokingSlipId: brokingSlip.slip_id,
                link,
                note,
                senderName: `${user?.first_name} ${user?.last_name}`,
                date: new Date().toLocaleString(),
              },
            });
          });

          await Promise.all(emailPromises);

          await AuditLogService.logEvent({
            userId: initiatorId,
            eventType: AuditLogEnum.UPDATE_RESOURCE,
            module: ModuleEnum.BROKING_SLIP,
            action_description: `Broking slip ${brokingSlip.slip_id} sent to insurer "${insurer.name}" by ${user?.first_name} ${user?.last_name} note: ${note}`,
            ip_address: ipAddress,
            device_info: userAgent,
          });

          return {
            insurer: insurer.name,
            status: 'queued',
            partial: missingOrMismatched.length > 0,
            missingContacts: missingOrMismatched
          };
        } catch (err) {
          logger.error(`❌ Failed to queue email for insurer ID ${insurer_id}: ${err}`);
          return {
            insurer: insurer_id,
            status: 'failed',
            error: err.message
          };
        }
      })
    );

    const successes = results.filter(r => r.status === 'fulfilled' && r.value?.status === 'queued');

    if (successes.length > 0) {
      brokingSlip.status = BrokingSlipStatusEnum.SENT_TO_INSURER;
      await brokingSlip.save();
    } else {
      logger.info('Failed to queue broking slip to all insurers');
    }

    await cacheService.invalidateByPattern(`${CacheKeys.INSURER_BROKING_SLIPS}:*`);

    return brokingSlip;
  }
}