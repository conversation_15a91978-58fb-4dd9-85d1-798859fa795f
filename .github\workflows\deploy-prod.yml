name: Deploy Backend To Prod

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install dependencies
        run: npm ci

      - name: Lint code
        run: npm run lint

      - name: Run backend tests
        run: npm run test:ci
      
      - name: Upload coverage artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: backen-coverage-report
          path: coverage

      - name: Building TypeScript
        run: npm run build

      # Skip deploy on PRs (CI only)
      - name: Deploy backend to EC2
        if: github.event_name == 'push'
        uses: appleboy/ssh-action@v0.1.10
        with:
          host: ${{ secrets.AWS_PROD_HOST }}
          username: ${{ secrets.AWS_USER }}
          key: ${{ secrets.AWS_PROD_KEY }}
          script: |
            cd ~

            if [ ! -d "kbc-backend" ]; then
              git clone https://${{ secrets.USER }}:${{ secrets.PAT }}@github.com/projectbabura/kbc-backend.git
            fi

            cd kbc-backend
            git fetch origin
            git checkout main
            git reset --hard origin/main

            echo "Installing dependencies..."
            npm install

            echo "Building TypeScript..."
            npm run build

            

            echo "Restarting backend..."
            pm2 delete kbc-backend || true
            pm2 start dist/index.js --name kbc-backend --interpreter node --interpreter-args="-r tsconfig-paths/register"
            pm2 save
            
            echo "Running migrations"
            npm run db:migrate
            npm run seed:permissions
