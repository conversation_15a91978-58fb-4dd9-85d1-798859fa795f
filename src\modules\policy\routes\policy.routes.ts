import { Router } from 'express';
import { ModuleEnum } from '../../../enum/module.enum';
import { ModuleAction } from '../../../enum/module-action';
import PolicyController from '../controllers/policy.controller';
import { checkPermissions, isAuthenticated } from '../../../middlewares/auth.middleware';
import { validateRequest, validateUUIDParam } from '../../../middlewares/zod-validation.middleware';
import { createPolicySchema, updatePolicySchema } from '../validations/policy.schema';

const router = Router();
const policyController = new PolicyController();

router.post(
  '/',
  checkPermissions([{ module: ModuleEnum.POLICIES, action: ModuleAction.CREATE }]),
  validateRequest(createPolicySchema),
  policyController.createPolicy.bind(policyController)
);

router.get(
  '/',
  isAuthenticated,
  // checkPermissions([{ module: ModuleEnum.POLICIES, action: ModuleAction.READ }]),
  policyController.listPolicies.bind(policyController)
);

router.get(
  '/loading-types',
  checkPermissions([{ module: ModuleEnum.POLICIES, action: ModuleAction.READ }]),
  policyController.listLoadingTypes.bind(policyController)
);

router.get(
  '/discount-types',
  checkPermissions([{ module: ModuleEnum.POLICIES, action: ModuleAction.READ }]),
  policyController.listDiscountTypes.bind(policyController)
);

router.get(
  '/categories',
  checkPermissions([{ module: ModuleEnum.POLICIES, action: ModuleAction.READ }]),
  policyController.listCategories.bind(policyController)
);

router.get(
  '/assets',
  checkPermissions([{ module: ModuleEnum.POLICIES, action: ModuleAction.READ }]),
  policyController.listAssets.bind(policyController)
);

router.get(
  '/search',
  checkPermissions([{ module: ModuleEnum.POLICIES, action: ModuleAction.READ }]),
  policyController.getPolicyByName.bind(policyController)
);

router.patch(
  '/:id',
  checkPermissions([{ module: ModuleEnum.POLICIES, action: ModuleAction.UPDATE }]),
  validateRequest(updatePolicySchema),
  policyController.updatePolicy.bind(policyController)
);

router.delete(
  '/:id',
  checkPermissions([{ module: ModuleEnum.POLICIES, action: ModuleAction.DELETE }]),
  policyController.deletePolicy.bind(policyController)
);


router.get(
  '/:id',
  // checkPermissions([{ module: ModuleEnum.POLICIES, action: ModuleAction.READ }]),
  isAuthenticated,
  validateUUIDParam('id'),
  policyController.getPolicyById.bind(policyController)
);


export default router;
