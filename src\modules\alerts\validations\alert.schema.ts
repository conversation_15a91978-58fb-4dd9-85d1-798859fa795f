import { TimeUnit } from '../../../enum/time-unit.enum';
import { z } from 'zod';

export const createAlertSchema = z.object({
  alert_type: z
    .string({ required_error: 'alert_type is required' })
    .nonempty({ message: 'alert_type is required' }),

  active: z.boolean({ message: 'active should be a boolean' }).optional(),

  is_recurring: z.boolean({ message: 'is_recurring should be a boolean' }).optional(),

  trigger_unit: z
    .nativeEnum(TimeUnit, {
      required_error: 'trigger_unit is required',
      message: `trigger_unit must be one of ${Object.values(TimeUnit).join(', ')}`
    }),

  trigger_interval: z
    .number({ message: 'trigger_interval is required', required_error: 'trigger_interval is required' })
    .int({ message: 'trigger_interval must be an integer' }),

  reminders: z
    .array(
      z.object({
        recurrence_unit: z
          .nativeEnum(TimeUnit, {
            message: `recurrence_unit must be one of ${Object.values(TimeUnit).join(', ')}`,
            required_error: 'recurrence_unit is required'
          }),

        recurrence_interval: z
          .number({ message: 'recurrence_interval must be an integer', required_error: 'recurrence_interval is required' })
          .int({ message: 'recurrence_interval must be an integer' })
      }), {
      message: 'reminders should be an array of objects'
    }
    )
    .min(1, 'You must provide at least one reminder object.')
    .optional(),
});

export const updateAlertSchema = createAlertSchema.partial();

export const alertPayloadSchema = z
  .array(updateAlertSchema)
  .nonempty({ message: 'Atleast one alert object must be provided' });