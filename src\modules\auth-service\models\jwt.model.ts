import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';

export class JwtBlacklist extends Model<
  InferAttributes<JwtBlacklist>,
  InferCreationAttributes<JwtBlacklist>
> {
  declare id: CreationOptional<string>;
  declare token: string;
  declare blacklistedAt: CreationOptional<Date>;
}

export function initJwtBlacklistModel(sequelize: Sequelize): typeof JwtBlacklist {
  JwtBlacklist.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      token: {
        type: DataTypes.TEXT,
        allowNull: false,
        unique: true,
      },
      blacklistedAt: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        allowNull: false,
      },
    },
    {
      sequelize,
      modelName: 'JwtBlacklist',
      tableName: 'jwt_blacklist',
      timestamps: false,
      indexes: [
        {
          unique: true,
          fields: ['token'],
        },
      ],
    },
  );

  return JwtBlacklist;
}
