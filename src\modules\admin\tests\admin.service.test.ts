import AdminService from '../services/admin.service';
import { User } from '../../auth-service/models/user.model';
import { Role } from '../../auth-service/models/role.model';
import { Invitation } from '../../auth-service/models/invitation.model';
import { Company } from '../models/company.model';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { rabbitMQService } from '../../workers/rabbitmq/rabbitmq.service';
import { PaginationDto } from '../../../utils/pagination';
import { UserStatus } from '../../../enum/user-status.enum';
import { ModuleEnum } from '../../../enum/module.enum';
import { config } from '../../../config/env.config';
import { OtpHelper } from '../../../utils/otp-helper';
import { PasswordHelper } from '../../../utils/password.utils';
import { JWTService } from '../../../utils/jwt';
import { PhoneNumberHelper } from '../../../utils/phonenumber.util';
import { AppError } from '../../../utils/custom-error';

jest.mock('../../../config/env.config', () => ({
  config: {
    VALID_DOMAINS: ['@company.com'],
    FRONTEND_URL: ['https://frontend.com'],
    DEFAULT_EMAIL_SENDER: '<EMAIL>',
  }
}));

jest.mock('../../auth-service/models/user.model');
jest.mock('../../auth-service/models/role.model');
jest.mock('../../auth-service/models/invitation.model');
jest.mock('../models/company.model');
jest.mock('../../../modules/team/models/team.model');
jest.mock('../../audit-trail-service/services/audit-trail.service');
jest.mock('../../workers/rabbitmq/rabbitmq.service');
jest.mock('../../../utils/password.utils');
jest.mock('../../../utils/otp-helper');
jest.mock('../../../utils/jwt');
jest.mock('../../../utils/logger', () => ({ error: jest.fn(), info: jest.fn() }));
jest.mock('../../../utils/phonenumber.util', () => ({
  PhoneNumberHelper: {
    formatToCountryStandard: jest.fn((num) => num),
  },
}));

const mockUser = {
  id: 'user-id',
  email: '<EMAIL>',
  first_name: 'John',
  last_name: 'Doe',
  phone_number: '1234567890',
  department: 'IT',
  roleId: 'role-id',
  user_type: 'ADMIN',
  status: UserStatus.ACTIVE,
  save: jest.fn(),
};
const mockRole = {
  id: 'role-id',
  name: 'ADMIN',
  permissions: [{ module: 'users', action: 'create' }],
};
const mockCompany = {
  id: 'company-id',
  system_preferences: { theme: 'dark' },
  security_policy: { mfa: true },
  general_parameters: { timezone: 'UTC' },
  update: jest.fn(),
};
const mockInvitation = { id: 'invite-id', is_accepted: false };

const mockAuditLog = AuditLogService.logEvent as jest.Mock;
const mockSendToQueue = rabbitMQService.sendToQueue as jest.Mock;
const realGetCompany = AdminService.prototype.getCompany;

describe('AdminService', () => {
  const service = new AdminService();

  beforeEach(() => {
    jest.clearAllMocks();
    config.VALID_DOMAINS = ['@company.com'];
    config.FRONTEND_URL = ['https://frontend.com'];
    config.DEFAULT_EMAIL_SENDER = '<EMAIL>';
    (PhoneNumberHelper.formatToCountryStandard as jest.Mock).mockImplementation((num) => num);
  });

  describe('InviteUser', () => {
    it('should invite a new user', async () => {
      (User.findOne as jest.Mock).mockResolvedValue(null);
      (Role.findByPk as jest.Mock).mockResolvedValue(mockRole);
      (OtpHelper.generatePassword as jest.Mock).mockReturnValue('password');
      (PasswordHelper.hashPassword as jest.Mock).mockResolvedValue('hashed');
      (User.create as jest.Mock).mockResolvedValue({ ...mockUser, id: 'new-user-id', save: jest.fn() });
      (JWTService.generate as jest.Mock).mockReturnValue({ token: 'token', tokenExpiresIn: 123456 });
      (Invitation.create as jest.Mock).mockResolvedValue(mockInvitation);
      (Invitation.update as jest.Mock).mockResolvedValue(mockInvitation);
      (User.findByPk as jest.Mock).mockResolvedValueOnce({ first_name: 'Admin', last_name: 'User' });

      const result = await service.InviteUser({
        email: '<EMAIL>',
        first_name: 'Jane',
        last_name: 'Smith',
        phone_number: '1234567890',
        department: 'IT',
        roleId: 'role-id',
        userId: 'admin-id',
        ipAddress: '127.0.0.1',
        userAgent: 'agent',
      });

      expect(result).toHaveProperty('id');
      expect(mockSendToQueue).toHaveBeenCalled();
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw for invalid email domain', async () => {
      await expect(service.InviteUser({
        email: '<EMAIL>',
        first_name: 'Jane',
        last_name: 'Smith',
        phone_number: '1234567890',
        department: 'IT',
        roleId: 'role-id',
        userId: 'admin-id',
        ipAddress: '127.0.0.1',
        userAgent: 'agent',
      })).rejects.toThrow('Invalid email domain. Please use an official company email');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    // it('should throw if email already exists', async () => {
    //   (User.findOne as jest.Mock).mockResolvedValue({ ...mockUser, email: '<EMAIL>', save: jest.fn() });
    //   await expect(service.InviteUser({
    //     email: '<EMAIL>',
    //     first_name: 'Jane',
    //     last_name: 'Smith',
    //     phone_number: '1234567890',
    //     department: 'IT',
    //     roleId: 'role-id',
    //     userId: 'admin-id',
    //     ipAddress: '127.0.0.1',
    //     userAgent: 'agent',
    //   })).rejects.toThrow('Email already exists');
    //   expect(mockAuditLog).toHaveBeenCalled();
    // });

    it('should throw if phone number already exists', async () => {
      (User.findOne as jest.Mock).mockResolvedValue({ ...mockUser, phone_number: '1234567890', save: jest.fn() });
      await expect(service.InviteUser({
        email: '<EMAIL>',
        first_name: 'Jane',
        last_name: 'Smith',
        phone_number: '1234567890',
        department: 'IT',
        roleId: 'role-id',
        userId: 'admin-id',
        ipAddress: '127.0.0.1',
        userAgent: 'agent',
      })).rejects.toThrow('Phone number already exists');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw if role not found', async () => {
      (User.findOne as jest.Mock).mockResolvedValue(null);
      (Role.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.InviteUser({
        email: '<EMAIL>',
        first_name: 'Jane',
        last_name: 'Smith',
        phone_number: '1234567890',
        department: 'IT',
        roleId: 'role-id',
        userId: 'admin-id',
        ipAddress: '127.0.0.1',
        userAgent: 'agent',
      })).rejects.toThrow('Role not found');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should handle errors and log them', async () => {
      (User.findOne as jest.Mock).mockRejectedValue(new Error('DB error'));
      await expect(service.InviteUser({
        email: '<EMAIL>',
        first_name: 'Jane',
        last_name: 'Smith',
        phone_number: '1234567890',
        department: 'IT',
        roleId: 'role-id',
        userId: 'admin-id',
        ipAddress: '127.0.0.1',
        userAgent: 'agent',
      })).rejects.toThrow('DB error');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  describe('listAllUsers', () => {
    it('should return paginated users', async () => {
      (User.findAndCountAll as jest.Mock).mockResolvedValue({ count: 1, rows: [mockUser] });
      const result = await service.listAllUsers(new PaginationDto({}), { searchQuery: '' });
      expect(result.data.length).toBe(1);
      expect(result.meta.itemCount).toBe(1);
    });
  });

  describe('activateOrDeactivateUser', () => {
    it('should activate a user', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue({ ...mockUser, save: jest.fn() });
      const result = await service.activateOrDeactivateUser({
        userId: 'user-id',
        action: 'activate',
        ipAddress: '127.0.0.1',
        userAgent: 'agent',
      });
      expect(result.status).toBe(UserStatus.ACTIVE);
      expect(mockSendToQueue).toHaveBeenCalled();
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should deactivate a user', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue({ ...mockUser, save: jest.fn() });
      const result = await service.activateOrDeactivateUser({
        userId: 'user-id',
        action: 'deactivate',
        ipAddress: '127.0.0.1',
        userAgent: 'agent',
      });
      expect(result.status).toBe(UserStatus.DEACTIVATED);
      expect(mockSendToQueue).toHaveBeenCalled();
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw if user not found', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.activateOrDeactivateUser({
        userId: 'user-id',
        action: 'activate',
        ipAddress: '127.0.0.1',
        userAgent: 'agent',
      })).rejects.toThrow('User with ID user-id not found.');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should handle errors and log them', async () => {
      (User.findByPk as jest.Mock).mockRejectedValue(new Error('DB error'));
      await expect(service.activateOrDeactivateUser({
        userId: 'user-id',
        action: 'activate',
        ipAddress: '127.0.0.1',
        userAgent: 'agent',
      })).rejects.toThrow('DB error');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  describe('assignRoleToUser', () => {
    it('should assign a role to a user', async () => {
      (User.findByPk as jest.Mock).mockResolvedValueOnce({ ...mockUser, save: jest.fn() });
      (Role.findByPk as jest.Mock).mockResolvedValue(mockRole);
      const result = await service.assignRoleToUser('user-id', 'role-id', 'admin-id');
      expect(result).toContain('assigned to user');
    });

    it('should throw if user not found', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.assignRoleToUser('user-id', 'role-id', 'admin-id')).rejects.toThrow('User with ID user-id not found.');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw if self-assignment', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue({ ...mockUser, id: 'admin-id', save: jest.fn() });
      await expect(service.assignRoleToUser('admin-id', 'role-id', 'admin-id')).rejects.toThrow('You cannot assign a role to yourself.');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw if role not found', async () => {
      (User.findByPk as jest.Mock).mockResolvedValueOnce({ ...mockUser, save: jest.fn() });
      (Role.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.assignRoleToUser('user-id', 'role-id', 'admin-id')).rejects.toThrow('Role with ID role-id not found.');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should handle errors and log them', async () => {
      (User.findByPk as jest.Mock).mockRejectedValue(new Error('DB error'));
      await expect(service.assignRoleToUser('user-id', 'role-id', 'admin-id')).rejects.toThrow('DB error');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  describe('listAllModules', () => {
    it('should return all modules', async () => {
      const result = await service.listAllModules();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toContain(ModuleEnum.USERS);
    });

    it('should handle errors', async () => {
      const orig = Object.values;
      Object.values = () => { throw new AppError('fail'); };
      await expect(service.listAllModules()).rejects.toThrow('fail');
      Object.values = orig;
    });
  });

  describe('updateSystemPreferences', () => {
    it('should update company preferences if company exists', async () => {
      (service.getCompany as any) = jest.fn().mockResolvedValue({ ...mockCompany, update: jest.fn() });
      (Company.findByPk as jest.Mock).mockResolvedValue(mockCompany);

      const result = await service.updateSystemPreferences('user-id', 'ip', 'agent', {
        system_preferences: { theme: 'light' },
        security_policy: { mfa: false },
        general_parameters: { timezone: 'CET' },
      });
      expect(result).toEqual(mockCompany);
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should create company if not exists', async () => {
      (service.getCompany as any) = jest.fn().mockResolvedValue(null);
      (Company.create as jest.Mock).mockResolvedValue(mockCompany);

      const result = await service.updateSystemPreferences('user-id', 'ip', 'agent', {
        system_preferences: { theme: 'light' },
        security_policy: { mfa: false },
        general_parameters: { timezone: 'CET' },
      });
      expect(result).toEqual(mockCompany);
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  describe('getCompany', () => {
    beforeAll(() => {
      service.getCompany = realGetCompany.bind(service);
    });
    it('should return company if found', async () => {
      (Company.findOne as jest.Mock).mockResolvedValue(mockCompany);
      const result = await service.getCompany();
      expect(result).toEqual(mockCompany);
    });

    it('should return null if not found', async () => {
      (Company.findOne as jest.Mock).mockResolvedValue(null);
      const result = await service.getCompany();
      expect(result).toBeNull();
    });
  });
});