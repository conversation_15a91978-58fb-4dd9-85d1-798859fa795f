import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';

export class Alerts extends Model<
  InferAttributes<Alerts>,
  InferCreationAttributes<Alerts>
> {
  declare id: CreationOptional<string>;
  declare alert_type: string;
  declare active: boolean;
  declare trigger_interval: number;
  declare trigger_unit: string;
  declare reminders: CreationOptional<{
    recurrence_unit?: string;
    recurrence_interval?: number
  }[]>;
  // declare notification_channel: string[];
  declare is_recurring: boolean;
}

export function initAlertsModel(sequelize: Sequelize): typeof Alerts {
  Alerts.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      alert_type: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true
      },
      active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      // notification_channel: {
      //   type: DataTypes.ARRAY(DataTypes.STRING),
      //   allowNull: false,
      // },
      trigger_unit: {
        type: DataTypes.STRING,
        allowNull: true
      },
      trigger_interval: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'Unit of time for the recurring interval',
      },
      is_recurring: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      reminders: {
        type: DataTypes.JSONB,
        allowNull: true,
      }
    },
    {
      sequelize,
      modelName: 'Alerts',
      tableName: 'alerts',
      timestamps: true,
      indexes: [
        {
          unique: false,
          fields: ['active']
        }
      ],
    }
  );

  return Alerts;
}
