import { Readable } from 'stream';

jest.mock('ioredis', () => {
  const mockPipeline = () => ({
    del: jest.fn().mockReturnThis(),
    exec: jest.fn().mockResolvedValue([]),
  });

  const createMockScanStream = () => {
    const stream = new Readable({
      objectMode: true,
      read() {
        // Emit an empty array once and end the stream
        this.push([]);
        this.push(null);
      }
    });
    return stream;
  };

  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      on: jest.fn(),
      quit: jest.fn(),
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      scanStream: jest.fn(createMockScanStream),
      pipeline: jest.fn(mockPipeline),
    })),
  };
});


import ClientService from '../services/clients.service';
import { Clients } from '../models/clients.model';
import { User } from '../../auth-service/models/user.model';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { rabbitMQService } from '../../workers/rabbitmq/rabbitmq.service';
import { EMAIL_QUEUE } from '../../../constants/role-permission.constant';
import { PaginationDto } from '../../../utils/pagination';
import { NotificationChannelEnum } from '../../../enum/notification-channel.eum';

jest.mock('../models/clients.model');
jest.mock('../../auth-service/models/user.model');
jest.mock('../../auth-service/models/role.model');
jest.mock('../../auth-service/models/permission.model');
jest.mock('../../audit-trail-service/services/audit-trail.service');
jest.mock('../../workers/rabbitmq/rabbitmq.service');
jest.mock('../../../utils/logger', () => ({ error: jest.fn(), info: jest.fn() }));

jest.mock('../../../config/env.config', () => ({
  config: {
    VALID_DOMAINS: ['@company.com'],
    FRONTEND_URL: ['https://frontend.com'],
    DEFAULT_EMAIL_SENDER: '<EMAIL>',
  }
}));

const mockClientData = {
  id: 'client-id',
  email: '<EMAIL>',
  phone_number: '1234567890',
  first_name: 'John',
  last_name: 'Doe',
  client_type: 'individual' as const,
  update: jest.fn(),
  save: jest.fn(),
  toJSON: function () { return this; },
};

const mockUser = { first_name: 'Admin', last_name: 'User' };

describe('ClientService', () => {
  const service = new ClientService();
  const mockAuditLog = AuditLogService.logEvent as jest.Mock;
  const mockSendToQueue = rabbitMQService.sendToQueue as jest.Mock;

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createClient', () => {
    it('should create a new client', async () => {
      (Clients.findOne as jest.Mock).mockResolvedValue(null);
      (Clients.create as jest.Mock).mockResolvedValue(mockClientData);

      const result = await service.createClient('127.0.0.1', 'agent', 'user-id', mockClientData);

      expect(Clients.findOne).toHaveBeenCalled();
      expect(Clients.create).toHaveBeenCalledWith(mockClientData);
      expect(mockAuditLog).toHaveBeenCalled();
      expect(mockSendToQueue).toHaveBeenCalledWith(EMAIL_QUEUE, expect.any(Object));
      expect(result).toEqual(mockClientData);
    });

    it('should throw error if email already exists', async () => {
      (Clients.findOne as jest.Mock).mockResolvedValue({ email: mockClientData.email });

      await expect(service.createClient('127.0.0.1', 'agent', 'user-id', mockClientData))
        .rejects.toThrow('Client with this email address already exists');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw error if phone number already exists', async () => {
      (Clients.findOne as jest.Mock).mockResolvedValue({ phone_number: mockClientData.phone_number });

      await expect(service.createClient('127.0.0.1', 'agent', 'user-id', mockClientData))
        .rejects.toThrow('Client with this phone number already exists');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw error if business_name is missing for corporate', async () => {
      await expect(service.createClient('127.0.0.1', 'agent', 'user-id', {
        ...mockClientData,
        client_type: 'corporate',
        business_name: undefined,
      })).rejects.toThrow('Please provide a business name for corporate clients.');
    });
  });

  describe('listAllClients', () => {
    it('should return paginated clients', async () => {
      const mockResponse = { count: 1, rows: [mockClientData] };
      (Clients.findAndCountAll as jest.Mock).mockResolvedValue(mockResponse);

      const result = await service.listAllClients(new PaginationDto({}));

      expect(result.data.length).toBe(1);
      expect(result.meta.itemCount).toBe(1);
    });
  });

  describe('getSingleClient', () => {
    it('should return client by ID', async () => {
      (Clients.findByPk as jest.Mock).mockResolvedValue({
        ...mockClientData,
        toJSON: () => mockClientData,
      });

      const result = await service.getSingleClient('client-id');
      expect(result).toMatchObject({
        ...mockClientData,
        activePolicies: expect.any(Number),
        policiesExpiringSoon: expect.any(Number),
        totalPremiumDue: expect.any(Number),
      });
    });

    it('should throw error if not found', async () => {
      (Clients.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.getSingleClient('client-id')).rejects.toThrow('Client not found');
    });
  });

  describe('updateClient', () => {
    it('should update an existing client', async () => {
      const clientMock = { ...mockClientData, update: jest.fn() };
      (Clients.findByPk as jest.Mock).mockResolvedValueOnce(clientMock);
      (Clients.findOne as jest.Mock).mockResolvedValue(null);
      (User.findByPk as jest.Mock).mockResolvedValue(mockUser);

      const result = await service.updateClient('client-id', '127.0.0.1', 'user-id', 'agent', {
        email: '<EMAIL>',
      });

      expect(clientMock.update).toHaveBeenCalled();
      expect(result).toEqual(clientMock);
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw if email exists for another client', async () => {
      (Clients.findByPk as jest.Mock).mockResolvedValue(mockClientData);
      (Clients.findOne as jest.Mock).mockResolvedValue({});

      await expect(
        service.updateClient('client-id', '127.0.0.1', 'user-id', 'agent', { email: '<EMAIL>' })
      ).rejects.toThrow('Email already exists');
    });

    it('should throw an error if trying to update with an existing phone number', async () => {
      const updateData = { phone_number: '1234567890' };
      // Mock findOne to return a different client with the same phone number
      (Clients.findOne as jest.Mock).mockImplementation(async (options) => {
        if (options.where.phone_number === updateData.phone_number) {
          return { id: 'other-client-id', ...updateData };
        }
        return null;
      });

      await expect(service.updateClient('client-id-123', '127.0.0.1', 'user-id-456', 'agent', updateData))
        .rejects.toThrow('Phone number already exists');
    });

    it('should throw if client does not exist', async () => {
      (Clients.findByPk as jest.Mock).mockResolvedValue(null);

      await expect(
        service.updateClient('client-id', '127.0.0.1', 'user-id', 'agent', { email: '<EMAIL>' })
      ).rejects.toThrow('Client not found');
    });
  });

  describe('createBulkClients', () => {
    const payload = {
      account_manager_id: '39a39ef0-8e8d-4a51-9dad-92413bb1222c',
      clients: [
        mockClientData,
        { ...mockClientData, email: '<EMAIL>' },
      ]
    };

    it('should create multiple clients and collect errors', async () => {
      (Clients.findOne as jest.Mock).mockResolvedValue(null);
      (Clients.create as jest.Mock).mockResolvedValue(mockClientData);

      const result = await service.createBulkClients('127.0.0.1', 'agent', 'user-id', payload);

      expect(result.success.length).toBe(2);
      expect(result.failed.length).toBe(0);
    });

    it('should collect errors for failed client creations', async () => {
      (Clients.findOne as jest.Mock)
        .mockResolvedValueOnce(null) // for first client
        .mockResolvedValueOnce({ email: '<EMAIL>' }); // conflict on second

      (Clients.create as jest.Mock).mockResolvedValue(mockClientData);

      const result = await service.createBulkClients('127.0.0.1', 'agent', 'user-id', payload);

      expect(result.success.length).toBe(1);
      expect(result.failed.length).toBe(1);
      expect(result.failed[0].data.email).toBe('<EMAIL>');
    });
  });

  describe('listAccountManagers', () => {
    it('should return paginated account managers', async () => {
      (User.findAndCountAll as jest.Mock).mockResolvedValue({ count: 1, rows: [mockUser] });
      const result = await service.listAccountManagers(new PaginationDto({}));
      expect(result.data.length).toBe(1);
      expect(result.meta.itemCount).toBe(1);
    });
  });

  describe('clientDashboardData', () => {
    it('should return dashboard stats', async () => {
      (Clients.findAll as jest.Mock).mockResolvedValue([{
        totalClients: 10,
        activeClients: 5,
        clientThisMonth: 2,
        totalPolicies: 7,
      }]);
      const result = await service.clientDashboardData();
      expect(result.totalClients).toBe(10);
      expect(result.activeClients).toBe(5);
      expect(result.clientThisMonth).toBe(2);
      expect(result.totalPolicies).toBe(7);
    });
  });

  describe('notifyClients', () => {
    it('should notify clients via email', async () => {
      (service.findById as any) = jest.fn().mockResolvedValue(mockClientData);
      mockSendToQueue.mockResolvedValue(undefined);

      const result = await service.notifyClients({
        clientIds: ['client-id'],
        notifications: ['welcome'],
        channel: NotificationChannelEnum.EMAIL,
        ipAddress: '127.0.0.1',
        userId: 'user-id',
        userAgent: 'agent',
      });

      expect(result.success).toContain('client-id');
      expect(result.failed.length).toBe(0);
      expect(mockSendToQueue).toHaveBeenCalledWith(EMAIL_QUEUE, expect.any(Object));
    });
  });

  describe('assignAccountManagerToClients', () => {
    it('should assign account manager to clients', async () => {
      const clientMock = { ...mockClientData, save: jest.fn() };
      (Clients.findByPk as jest.Mock).mockResolvedValue(clientMock);

      const result = await service.assignAccountManagerToClients(
        'manager-id',
        ['client-id'],
        { ipAddress: '127.0.0.1', userAgent: 'agent', userId: 'user-id' }
      );
      expect(result.success).toContain('client-id');
      expect(result.failed.length).toBe(0);
    });

    it('should collect failures for missing clients', async () => {
      (Clients.findByPk as jest.Mock).mockResolvedValue(null);

      const result = await service.assignAccountManagerToClients(
        'manager-id',
        ['client-id'],
        { ipAddress: '127.0.0.1', userAgent: 'agent', userId: 'user-id' }
      );
      expect(result.success.length).toBe(0);
      expect(result.failed.length).toBe(1);
    });
  });

  describe('markClientAsLost', () => {
    it('should mark a client as lost', async () => {
      const clientMock = { ...mockClientData, save: jest.fn() };
      (Clients.findByPk as jest.Mock).mockResolvedValue(clientMock);
      (User.findByPk as jest.Mock).mockResolvedValue(mockUser);

      const result = await service.markClientAsLost(
        'client-id',
        'reason',
        '127.0.0.1',
        'agent',
        'user-id'
      );
      expect(clientMock.save).toHaveBeenCalled();
      expect(result).toEqual(clientMock);
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw if client not found', async () => {
      (Clients.findByPk as jest.Mock).mockResolvedValue(null);

      await expect(service.markClientAsLost(
        'client-id',
        'reason',
        '127.0.0.1',
        'agent',
        'user-id'
      )).rejects.toThrow('Client not found');
    });
  });

  describe('findById', () => {
    it('should return client by id', async () => {
      (Clients.findByPk as jest.Mock).mockResolvedValue(mockClientData);
      const result = await service.findById('client-id');
      expect(result).toEqual(mockClientData);
    });
  });

  describe('listNotificationTypes', () => {
    it('should return notification types', async () => {
      const result = await service.listNotificationTypes();
      expect(Array.isArray(result)).toBe(true);
    });
  });
});