import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';
import { ContactList } from './contact-list.model';
import { InsurerStatus } from '../../../enum/insurer-status.enum';


export class Insurer extends Model<
  InferAttributes<Insurer>,
  InferCreationAttributes<Insurer>
> {
  declare id: CreationOptional<string>;
  declare name: string;
  declare website_url: string;
  declare logo: string;
  declare status: CreationOptional<string>;
  declare updatedAt?: Date;
  declare createdAt?: Date;
}

export function initInsurerModel(sequelize: Sequelize): typeof Insurer {
  Insurer.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false
      },
      website_url: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      logo: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: InsurerStatus.ACTIVE
      },
    },
    {
      sequelize,
      modelName: 'Insurer',
      tableName: 'insurer',
      timestamps: true,
      paranoid: true,
      defaultScope: {
        include: [{ model: ContactList, as: 'contacts' }],
      },
      indexes: [
        {
          fields: ['status'],
        },
      ]
    },
  );

  return Insurer;
}
