import { z } from 'zod';

export const UpdateUserSchema = z.object({
  email: z
    .string({
      message: 'Email cannot be empty',
      invalid_type_error: 'Email must be a string',
    })
    .email({ message: 'invalid email format' })
    .optional(),

  first_name: z.string({
    message: 'First name cannot be empty',
    invalid_type_error: 'First name must be a string',
  }).optional(),

  last_name: z.string({
    invalid_type_error: 'Last name must be a string',
    message: 'Last name cannot be empty',
  }).optional(),

  phone_number: z.string({
    invalid_type_error: 'Phone number must be a string',
    message: 'Phone number cannot be empty',
  }).optional(),

  roleId: z.string({
    invalid_type_error: 'Role ID must be a string',
    message: 'Role ID cannot be empty',
  }).optional(),
  
  profile_picture: z.string({
    invalid_type_error: 'Profile picture must be a string',
    message: 'Profile picture URL cannot be empty',
  }).optional(),
}).refine(
  (data) => Object.values(data).some((value) => value !== undefined),
  {
    message: 'At least one field must be provided for update',
  }
);