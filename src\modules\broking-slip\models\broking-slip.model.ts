import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  ForeignKey,
} from 'sequelize';
import { Clients } from '../../clients/models/clients.model';
import { User } from '../../auth-service/models/user.model';
import { BrokingSlipStatusEnum } from '../../../enum/broking-slip-status.enum';
import { TimeUnit } from '../../../enum/time-unit.enum';

export class BrokingSlip extends Model<
  InferAttributes<BrokingSlip>,
  InferCreationAttributes<BrokingSlip>
> {
  declare id: CreationOptional<string>;
  declare slip_id: CreationOptional<string>;
  declare clientId: ForeignKey<string>;
  declare created_by: ForeignKey<string>;
  declare serial_number: CreationOptional<number>;
  declare cumulative_sum_insured: CreationOptional<string>;
  declare cumulative_gross_premium: CreationOptional<string>;
  declare status: string;
  declare coverage_time: CreationOptional<number>;
  declare coverage_period: CreationOptional<TimeUnit>;
  declare createdAt?: CreationOptional<Date>;
  declare updatedAt?: CreationOptional<Date>;
}

export function initBrokingSlipModel(sequelize: Sequelize): typeof BrokingSlip {
  BrokingSlip.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      slip_id: {
        type: DataTypes.STRING,
        unique: true,
      },
      serial_number: {
        type: DataTypes.INTEGER,
        unique: true,
        allowNull: false,
      },

      clientId: {
        type: DataTypes.UUID,
        references: {
          model: Clients,
          key: 'id'
        }
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: false,
        references: { model: User, key: 'id' },
      },
      cumulative_sum_insured: {
        type: DataTypes.DECIMAL(18, 2),
        allowNull: false,
        defaultValue: 0,
      },
      cumulative_gross_premium: {
        type: DataTypes.DECIMAL(18, 2),
        allowNull: false,
        defaultValue: 0,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: BrokingSlipStatusEnum.DRAFT,
      },
      coverage_time: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      coverage_period: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: TimeUnit.YEAR,
      },
    },
    {
      sequelize,
      modelName: 'BrokingSlip',
      tableName: 'broking_slip',
      timestamps: true,
      paranoid: true,
      defaultScope: {
        attributes: { exclude: ['serial_number'] }
      },
      indexes: [
        {
          fields: ['status', 'created_by',],
        },
        { unique: true, fields: ['serial_number'] },
      ],
    }
  );

  return BrokingSlip;
}
