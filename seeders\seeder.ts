import { Sequelize, Op } from 'sequelize';
import dotenv from 'dotenv';
import { config } from '../src/config/env.config';
import { initRoleModel, Role } from '../src/modules/auth-service/models/role.model';
import { initPermissionModel, Permission } from '../src/modules/auth-service/models/permission.model';
import { initRolePermissionModel, RolePermission } from '../src/modules/auth-service/models/role-permission.model';

console.log('▶️ Starting the roles and permissions seeder...');
dotenv.config();

console.log(`[INFO] Loading configuration for database: ${config.DB_NAME} on host: ${config.DB_HOST}`);

const sequelize = new Sequelize({
  database: config.DB_NAME,
  username: config.DB_USER,
  password: config.DB_PASS,
  host: config.DB_HOST,
  port: parseInt(config.DB_PORT || '5432'),
  dialect: 'postgres',
  dialectOptions: {
    ssl: {
      require: true,
      rejectUnauthorized: false,
    },
  },
  logging: false,
});

class RolesAndPermissionsSeeder {
  constructor(private sequelize: Sequelize) { }

  async run() {
    const rolesToSeed = [
      { name: 'system_admin', description: 'System Administrator', is_system_role: true },
      { name: 'sales_rep', description: 'Sales Representative' },
      { name: 'claims_handler', description: 'Claims Handler' },
      { name: 'underwriter', description: 'Underwriter' },
      { name: 'finance_officer', description: 'Finance Officer' },
      { name: 'admin', description: 'Admin' },
    ];

    const permissionsByRole = {
      system_admin: {
        clients: ['create', 'read', 'update', 'delete', 'approve'],
        quotes: ['create', 'read', 'update', 'delete', 'approve'],
        claims: ['create', 'read', 'update', 'delete', 'approve'],
        policies: ['create', 'read', 'update', 'delete', 'approve'],
        payments: ['create', 'read', 'update', 'delete', 'approve'],
        auth: ['create', 'read', 'update', 'delete', 'approve'],
        roles: ['create', 'read', 'update', 'delete', 'approve'],
        system: ['create', 'read', 'update', 'delete', 'approve'],
        users: ['create', 'read', 'update', 'delete', 'approve'],
        prospects: ['create', 'read', 'update', 'delete', 'approve'],
        documents: ['create', 'read', 'update', 'delete', 'approve'],
        setup: ['create', 'read', 'update', 'delete', 'approve'],
        compliance: ['create', 'read', 'update', 'delete', 'approve'],
        integration: ['create', 'read', 'update', 'delete', 'approve'],
        account_management: ['create', 'read', 'update', 'delete', 'approve'],
        broking_slip: ['create', 'read', 'update', 'delete', 'approve'],
        insurer: ['create', 'read', 'update', 'delete', 'approve'],
      },
      sales_rep: {
        clients: ['create', 'read', 'update'],
        quotes: ['create', 'read', 'update'],
        users: ['update'],
        auth: ['read'],
      },
      claims_handler: {
        claims: ['read', 'update'],
        users: ['update'],
        auth: ['read'],
      },
      underwriter: {
        quotes: ['read', 'update', 'approve'],
        policies: ['read', 'update', 'approve'],
        users: ['update'],
        auth: ['read'],
      },
      finance_officer: {
        payments: ['read', 'update'],
        users: ['update'],
        auth: ['read'],
      },
      admin: {
        clients: ['create', 'read', 'update', 'delete', 'approve'],
        quotes: ['create', 'read', 'update', 'delete', 'approve'],
        claims: ['create', 'read', 'update', 'delete', 'approve'],
        policies: ['create', 'read', 'update', 'delete', 'approve'],
        payments: ['create', 'read', 'update', 'delete', 'approve'],
        auth: ['create', 'read', 'update', 'delete', 'approve'],
        roles: ['create', 'read', 'update', 'delete', 'approve'],
        users: ['create', 'read', 'update', 'delete', 'approve'],
        prospects: ['create', 'read', 'update', 'delete', 'approve'],
        documents: ['create', 'read', 'update', 'delete', 'approve'],
        setup: ['create', 'read', 'update', 'delete', 'approve'],
        compliance: ['create', 'read', 'update', 'delete', 'approve'],
        integration: ['create', 'read', 'update', 'delete', 'approve'],
        account_management: ['create', 'read', 'update', 'delete', 'approve'],
        broking_slip: ['create', 'read', 'update', 'delete', 'approve'],
        insurer: ['create', 'read', 'update', 'delete', 'approve'],
      },
    };

    // Seed roles
    console.log('[INFO] Seeding roles...');
    const createdRoles: Role[] = [];
    for (const role of rolesToSeed) {
      const [instance] = await Role.upsert(role, { returning: true });
      createdRoles.push(instance);
    }
    console.log(`[SUCCESS] Roles seeded (${createdRoles.length})`);

    // Seed permissions
    console.log('[INFO] Seeding permissions...');
    const allPermissions = new Set<string>();
    Object.values(permissionsByRole).forEach((modules) => {
      Object.entries(modules).forEach(([module, actions]) => {
        (actions as string[]).forEach((action) => allPermissions.add(`${module}:${action}`));
      });
    });

    const allowedActions = ['create', 'read', 'update', 'delete', 'approve'] as const;
    type AllowedAction = typeof allowedActions[number];

    const permissionArray = Array.from(allPermissions).map((perm) => {
      const [module, action] = perm.split(':');
      if (!allowedActions.includes(action as AllowedAction)) {
        throw new Error(`Invalid action: ${action}`);
      }
      return { module, action: action as AllowedAction, description: `Allows ${action} on ${module}` };
    });

    const existingPermissions = await Permission.findAll({
      where: {
        [Op.or]: permissionArray.map((p) => ({ module: p.module, action: p.action })),
      },
    });

    const existingMap = new Map(existingPermissions.map((p) => [`${p.module}:${p.action}`, p]));
    const newPermissions = permissionArray.filter((p) => !existingMap.has(`${p.module}:${p.action}`));

    const createdPermissions = await Permission.bulkCreate(newPermissions, { returning: true });
    createdPermissions.forEach((p) => existingMap.set(`${p.module}:${p.action}`, p));
    console.log(`[SUCCESS] Permissions seeded (new: ${createdPermissions.length}, total: ${existingMap.size})`);

    // Assign permissions to roles
    console.log('[INFO] Assigning permissions to roles...');
    const rolePermissionTuples: { roleId: number | string; permissionId: number | string }[] = [];

    for (const [roleName, modules] of Object.entries(permissionsByRole)) {
      const role = createdRoles.find((r) => r.name === roleName);
      if (!role) continue;

      for (const [module, actions] of Object.entries(modules)) {
        for (const action of actions as string[]) {
          const key = `${module}:${action}`;
          const permission = existingMap.get(key);
          if (permission) {
            rolePermissionTuples.push({ roleId: String(role.id), permissionId: String(permission.id) });
          }
        }
      }
    }

    const normalizedRolePermissionTuples = rolePermissionTuples.map(tuple => ({
      roleId: String(tuple.roleId),
      permissionId: String(tuple.permissionId),
    }));

    await RolePermission.bulkCreate(normalizedRolePermissionTuples, { ignoreDuplicates: true });
    console.log(`[SUCCESS] Permissions assigned to roles (${rolePermissionTuples.length} entries).`);
  }
}

const run = async () => {
  try {
    console.log('[INFO] Authenticating database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connection successful.');

    console.log('[INFO] Initializing models...');
    initRoleModel(sequelize);
    initPermissionModel(sequelize);
    initRolePermissionModel(sequelize);

    Role.belongsToMany(Permission, { through: RolePermission, foreignKey: 'roleId', otherKey: 'permissionId' });
    Permission.belongsToMany(Role, { through: RolePermission, foreignKey: 'permissionId', otherKey: 'roleId' });
    console.log('✅ Models initialized.');

    console.log('[INFO] Synchronizing database schema...');
    await sequelize.sync({ force: false });
    console.log('✅ Database schema synchronized.');

    const seeder = new RolesAndPermissionsSeeder(sequelize);
    await seeder.run();

    console.log('🎉 Seeding completed successfully!');
  } catch (error) {
    console.error('❌ An error occurred during the seeding process:', error);
  } finally {
    console.log('[INFO] Closing database connection.');
    await sequelize.close();
  }
};

run();
