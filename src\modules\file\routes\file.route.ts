import { Router } from 'express';
import FileController from '../controllers/file.controller';
import { isAuthenticated } from '../../../middlewares/auth.middleware';
import multer from 'multer';
import { validateRequest } from '../../../middlewares/zod-validation.middleware';
import { uploadSchema } from '../validations/file.schema';

const router = Router();
const fileController = new FileController();
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
  },
  fileFilter: (_req, file, cb) => {
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'application/pdf',
      'application/msword', // .doc
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/vnd.ms-excel', // .xls
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'text/csv', // .csv
    ];
    if (!allowedTypes.includes(file.mimetype)) {
      return cb(new Error('Invalid file type. Only JPEG, PNG, and PDF are allowed.'));
    }
    cb(null, true);
  }
});

router.post(
  '/upload',
  isAuthenticated,
  upload.single('file'),
  validateRequest(uploadSchema),
  fileController.upload.bind(fileController)
);

router.get(
  '/download',
  // isAuthenticated,
  fileController.download.bind(fileController)
);

export default router;