import { Router } from 'express';
import InsurerController from '../controllers/insurer.controller';
import { checkPermissions } from '../../../middlewares/auth.middleware';
import { ModuleEnum } from '../../../enum/module.enum';
import { ModuleAction } from '../../../enum/module-action';
import { validateRequest, validateUUIDParam } from '../../../middlewares/zod-validation.middleware';
import { contactPersonSchema, createInsurerSchema, updateInsurerSchema } from '../validations/insurer.validation';

const router = Router();
const insurerController = new InsurerController();


// Create a new insurer
router.post(
  '/',
  checkPermissions([{ module: ModuleEnum.INSURER, action: ModuleAction.CREATE }]),
  validateRequest(createInsurerSchema),
  insurerController.createInsurer.bind(insurerController)
);

// List insurers with optional filtering and pagination
router.get(
  '/',
  checkPermissions([
    {
      module: ModuleEnum.INSURER,
      action: ModuleAction.READ
    },
    {
      module: ModuleEnum.BROKING_SLIP, action: ModuleAction.READ
    }]),
  insurerController.listInsurers.bind(insurerController)
);

// Get insurer by ID
router.get(
  '/:id',
  checkPermissions([{ module: ModuleEnum.INSURER, action: ModuleAction.READ }]),
  validateUUIDParam('id'),
  insurerController.getInsurerById.bind(insurerController)
);

// Update an insurer by ID
router.patch(
  '/:id',
  checkPermissions([{ module: ModuleEnum.INSURER, action: ModuleAction.UPDATE }]),
  validateUUIDParam('id'),
  validateRequest(updateInsurerSchema),
  insurerController.updateInsurer.bind(insurerController)
);

// Delete an insurer by ID
router.delete(
  '/:id',
  checkPermissions([{ module: ModuleEnum.INSURER, action: ModuleAction.DELETE }]),
  validateUUIDParam('id'),
  insurerController.deleteInsurer.bind(insurerController)
);

// Add a contact to an insurer
router.post(
  '/:insurerId/contacts',
  checkPermissions([{ module: ModuleEnum.INSURER, action: ModuleAction.CREATE }]),
  validateUUIDParam('insurerId'),
  validateRequest(contactPersonSchema),
  insurerController.addContactToInsurer.bind(insurerController)
);

// List contacts for an insurer
router.get(
  '/:insurerId/contacts',
  checkPermissions([{ module: ModuleEnum.INSURER, action: ModuleAction.READ }]),
  validateUUIDParam('insurerId'),
  insurerController.getContactsByInsurer.bind(insurerController)
);

// Update an insurer contact
router.patch(
  '/:insurerId/contacts/:id',
  checkPermissions([{ module: ModuleEnum.INSURER, action: ModuleAction.UPDATE }]),
  validateUUIDParam('id'),
  validateUUIDParam('insurerId'),
  validateRequest(contactPersonSchema.partial()),
  insurerController.updateContact.bind(insurerController)
);

// delete a contact for an insurer
router.delete(
  '/contacts/:id',
  validateUUIDParam('id'),
  checkPermissions([{ module: ModuleEnum.INSURER, action: ModuleAction.DELETE }]),
  insurerController.deleteContact.bind(insurerController)
);

export default router;
