import { Sequelize } from 'sequelize';
import { initUser<PERSON>odel, User } from '../modules/auth-service/models/user.model';
import { initRoleModel, Role } from '../modules/auth-service/models/role.model';
import { initPermissionModel, Permission } from '../modules/auth-service/models/permission.model';
import { AuditLog, initAuditLogModel } from '../modules/audit-trail-service/models/audit.model';
import { initJwtBlacklistModel, JwtBlacklist } from '../modules/auth-service/models/jwt.model';
import { initSessionModel, Session } from '../modules/auth-service/models/session.model';
import { initInvitationModel, Invitation } from '../modules/auth-service/models/invitation.model';
import { initTeamModel, Team } from '../modules/team/models/team.model';
import { initCompanyModel } from '../modules/admin/models/company.model';
import { initAlertsModel } from '../modules/alerts/models/alerts.model';
import { initRolePermissionModel, RolePermission } from '../modules/auth-service/models/role-permission.model';
import { Clients, initClientsModel } from '../modules/clients/models/clients.model';
import { Files, initFilesModel } from '../modules/file/models/files.model';
import { initPolicyModel, Policy } from '../modules/policy/models/policy.model';
import { initCategoryModel } from '../modules/policy/models/policy-category.model';
import { initDiscountTypeModel } from '../modules/policy/models/discount-type.model';
import { initLoadingTypeModel } from '../modules/policy/models/loading-type.model';
import { BrokingSlip, initBrokingSlipModel } from '../modules/broking-slip/models/broking-slip.model';
import { initAssetsModel } from '../modules/policy/models/assets.model';
import { initInsurerModel, Insurer } from '../modules/insurer/models/insurer.model';
import { ContactList, initContactListModel } from '../modules/insurer/models/contact-list.model';
import { initSlipModel, Slip } from '../modules/broking-slip/models/slip.model';
import { initInsurerBrokingSlipsModel, InsurerBrokingSlips } from '../modules/insurer/models/insurer-brokingSlips.model';
import { initQuotationModel, Quotation } from '../modules/quotation/models/quotation.model';
import { initQuotationHistoryModel, QuotationHistory } from '../modules/quotation/models/quotation-history.model';

export function initModels(sequelize: Sequelize) {
  // Initialize models
  initUserModel(sequelize);
  initRoleModel(sequelize);
  initPermissionModel(sequelize);
  initAuditLogModel(sequelize);
  initJwtBlacklistModel(sequelize);
  initSessionModel(sequelize);
  initInvitationModel(sequelize);
  initTeamModel(sequelize);
  initCompanyModel(sequelize);
  initAlertsModel(sequelize);
  initRolePermissionModel(sequelize);
  initClientsModel(sequelize);
  initFilesModel(sequelize);

  // Policy related models
  initPolicyModel(sequelize);
  initCategoryModel(sequelize);
  initAssetsModel(sequelize);
  initDiscountTypeModel(sequelize);
  initLoadingTypeModel(sequelize);
  initBrokingSlipModel(sequelize);

  // Insurer related models
  initInsurerModel(sequelize);
  initContactListModel(sequelize);

  // Slip
  initSlipModel(sequelize);

  // InsurerBrokingSlips
  initInsurerBrokingSlipsModel(sequelize);

  //Quotation
  initQuotationModel(sequelize);
  initQuotationHistoryModel(sequelize);


  // Define associations
  /**
   * User and Role association
   * A user belongs to a role
   * A role has many users
   */
  Role.hasMany(User, {
    foreignKey: 'roleId',
    as: 'users',
  });

  User.belongsTo(Role, {
    foreignKey: 'roleId',
    as: 'role',
  });

  /**
   * Role and Permission association
   * A role has many permissions
   * A Permission has many roles
   */
  Role.belongsToMany(Permission, {
    through: RolePermission,
    foreignKey: 'roleId',
    otherKey: 'permissionId',
    as: 'permissions'
  });
  Permission.belongsToMany(Role, {
    through: RolePermission,
    foreignKey: 'permissionId',
    otherKey: 'roleId',
    as: 'roles'
  });


  /**
   * User and Session association
   * A user has many sessions
   * A session belongs to a user
   */

  User.hasMany(Session, {
    foreignKey: 'user_id',
    as: 'sessions',
    onDelete: 'CASCADE',
  });

  Session.belongsTo(User, {
    foreignKey: 'user_id',
    as: 'user',
    onDelete: 'CASCADE',
  });

  /**
   * Invitation and User association
   * An invitation belongs to a user
   * A user has many invitations
   */

  User.hasMany(Invitation, {
    foreignKey: 'invited_by',
    as: 'sent_invitations',
    onDelete: 'SET NULL',
  });

  Invitation.belongsTo(User, {
    foreignKey: 'invited_by',
    as: 'inviter',
    onDelete: 'SET NULL',
  });

  /**
   * Role and Invitation association
   * An invitation belongs to a role
   * A role has many invitations
   */
  Role.hasMany(Invitation, {
    foreignKey: 'assigned_role_id',
    as: 'invitations',
    onDelete: 'SET NULL',
  });

  Invitation.belongsTo(Role, {
    foreignKey: 'assigned_role_id',
    as: 'assigned_role',
    onDelete: 'SET NULL',
  });

  /**
   * AuditLog and User association
   * An audit log belongs to a user
   * A user has many audit logs
   */
  User.hasMany(AuditLog, {
    foreignKey: 'user_id',
    as: 'audit_logs',
    onDelete: 'SET NULL',
  });
  AuditLog.belongsTo(User, {
    foreignKey: 'user_id',
    as: 'user',
    onDelete: 'SET NULL',
  });

  /**
   * Teams and User association
   * A team has many users
   * A user belongs to a team
   */

  User.belongsTo(Team, { foreignKey: 'teamId', as: 'team' });
  Team.hasMany(User, { foreignKey: 'teamId', as: 'members' });

  /**
   * Client account manager and user association
   */
  Clients.belongsTo(User, {
    foreignKey: 'account_manager_id',
    as: 'manager'
  });
  User.hasMany(Clients, {
    foreignKey: 'account_manager_id',
    as: 'managedClients'
  });

  /**
   * Files, user, claims, quotes policies association
   */
  Files.belongsTo(User, { foreignKey: 'uploaded_by' });
  Files.belongsTo(Clients, { foreignKey: 'client_id' });

  /**
   * Clients, Broking Slip association
   */
  BrokingSlip.belongsTo(Clients, { foreignKey: 'clientId', as: 'client' });
  Clients.hasMany(BrokingSlip, { foreignKey: 'clientId', as: 'brokingSlips' });

  // BrokingSlip → User
  BrokingSlip.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  User.hasMany(BrokingSlip, { foreignKey: 'created_by', as: 'createdSlips' });

  Slip.belongsTo(BrokingSlip, {
    foreignKey: 'brokingSlipId',
    as: 'brokingSlip',
    onDelete: 'CASCADE'
  });

  Slip.belongsTo(Policy, {
    foreignKey: 'policyId',
    as: 'policy',
  });

  BrokingSlip.hasMany(Slip, {
    foreignKey: 'brokingSlipId',
    as: 'slips',
  });

  // Insurer and ContactList association
  Insurer.hasMany(ContactList, {
    foreignKey: 'insurerId',
    as: 'contacts',
    onDelete: 'CASCADE',
    hooks: true
  });
  ContactList.belongsTo(Insurer, {
    foreignKey: 'insurerId',
    as: 'insurer',
    onDelete: 'CASCADE',
    hooks: true
  });

  // Insurer and BrokingSlip many-to-many association through InsurerBrokingSlips
  InsurerBrokingSlips.belongsTo(BrokingSlip, {
    foreignKey: 'broking_slip_id',
    as: 'brokingSlip',
    onDelete: 'CASCADE'
  });
  InsurerBrokingSlips.belongsTo(Insurer, {
    foreignKey: 'insurer_id',
    as: 'insurer',
    onDelete: 'CASCADE'
  });
  InsurerBrokingSlips.belongsTo(ContactList, {
    foreignKey: 'contact_id',
    as: 'contact',
    onDelete: 'CASCADE'
  });
  // BrokingSlip ↔ InsurerBrokingSlips association
  BrokingSlip.hasMany(InsurerBrokingSlips, {
    foreignKey: 'broking_slip_id',
    as: 'insurerBrokingSlips',
    onDelete: 'CASCADE',
  });

  // Quotation Broking Slip Association
  Quotation.belongsTo(BrokingSlip, {
    foreignKey: 'broking_slip_id',
    as: 'brokingSlip',
    onDelete: 'CASCADE'
  });
  BrokingSlip.hasMany(Quotation, {
    foreignKey: 'broking_slip_id',
    as: 'quotations',
    onDelete: 'CASCADE',
  });
  // Quotation Insurer Association
  Quotation.belongsTo(Insurer, {
    foreignKey: 'insurer_id',
    as: 'insurer',
    onDelete: 'CASCADE'
  });
  Insurer.hasMany(Quotation, {
    foreignKey: 'insurer_id',
    as: 'quotations',
    onDelete: 'CASCADE',
  });

  // Quotation Contact Association
  Quotation.belongsTo(ContactList, {
    foreignKey: 'contact_id',
    as: 'contact',
    onDelete: 'CASCADE'
  });
  ContactList.hasMany(Quotation, {
    foreignKey: 'contact_id',
    as: 'quotations',
    onDelete: 'CASCADE',
  });


  // Quotation History Association
  Quotation.hasMany(QuotationHistory, {
    foreignKey: 'quotation_id',
    as: 'history',
    onDelete: 'CASCADE',
    // hooks: true'
  });

  QuotationHistory.belongsTo(Quotation, {
    foreignKey: 'quotation_id',
    as: 'quotation',
    onDelete: 'CASCADE',
    // hooks: true'
  });



  return {
    User,
    Role,
    Permission,
    AuditLog,
    JwtBlacklist,
  };
}