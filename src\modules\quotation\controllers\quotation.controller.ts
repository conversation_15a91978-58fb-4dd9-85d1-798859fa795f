import { Response } from 'express';
import { RequestWithInsurer } from '../../../interfaces/custom-request.interface';
import QuotationService from '../services/quotation.service';
import { PaginationDto } from '../../../utils/pagination';

export default class QuotationController {
  private readonly quotationService: QuotationService;

  constructor() {
    this.quotationService = new QuotationService();
  }

  /**
   * List All Quotations
   */
  async listAllQuotations(req: RequestWithInsurer, res: Response): Promise<Response> {
    const paginationDto = new PaginationDto(req.query);
    const response = await this.quotationService.listAllQuotations(paginationDto);
    return res.status(200).json({ data: response, message: 'Quotations retrieved successfully' });
  }

  /**
   * List Insurer Quotations
   */
  async listInsurerQuotations(req: RequestWithInsurer, res: Response): Promise<Response> {
    const { insurerId } = req.insurer;
    const paginationDto = new PaginationDto(req.query);

    const response = await this.quotationService.listInsurerQuotations(insurerId, paginationDto);
    return res.status(200).json({ data: response, message: 'Quotations retrieved successfully' });
  }
}