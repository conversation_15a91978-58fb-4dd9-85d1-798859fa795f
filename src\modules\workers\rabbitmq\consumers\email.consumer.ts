import logger from '../../../../utils/logger';
import { rabbitMQService } from '../rabbitmq.service';
import { MailService } from '../../../../shared/mail.service';
import { EMAIL_QUEUE } from '../../../../constants/role-permission.constant';


const mailService = new MailService();

export const consumeEmailQueue = async () => {
  logger.info('📥 Email worker is consuming messages');
  await rabbitMQService.consume(
    EMAIL_QUEUE,
    async (data) => {
      try {
        await mailService.sendMail(data);
      } catch (error) {
        logger.error(`❌ Failed to send email: ${error}`);
      }
    }
  );
};
