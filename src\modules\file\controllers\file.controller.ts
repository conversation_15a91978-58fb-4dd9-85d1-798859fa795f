import { Response } from 'express';
import FileService from '../services/file.service';
import { RequestWithAuditMetadata } from '../../../interfaces/custom-request.interface';


/**
 * File controller class
 */
export default class FileController {
  private fileService: FileService;

  constructor() {
    this.fileService = new FileService();
  }

  /**
   * Uploads a file
   * @param req - Express request object
   * @param res - Express response object
   * @returns JSON response with the result of the upload
   */
  async upload(req: RequestWithAuditMetadata, res: Response) {
    if (!req.file) {
      return res.status(400).json({ message: 'file is required' });
    }

    const maxSize = 5 * 1024 * 1024; // 5MB
    if (req.file.size > maxSize) {
      return res.status(400).json({ message: 'File is too large maximum file size is 5MB' });
    }

    const { type, category, clientId } = req.body;

    const file = req.file as Express.Multer.File;
    const { userId } = req.user;
    const { ipAddress, userAgent } = req.auditMetadata;

    const result = await this.fileService.uploadFile({
      file,
      userId,
      clientId,
      type,
      ipAddress,
      userAgent,
      category,
    });

    return res.status(200).json({
      data: result,
      message: 'File uploaded successfully',
    });
  }
  /**
     * Download a file
     * @param req - Express request object
     * @param res - Express response object
     * @returns JSON response with the result of the upload
     */
  async download(req: RequestWithAuditMetadata, res: Response) {
    const { key } = req.query;

    if (!key || typeof key !== 'string') {
      return res.status(400).json({ message: 'File key is required' });
    }

    const data = await this.fileService.getS3SignedDownloadUrl(key);

    return res.status(200).json({
      data: {
        url: data,
        key,
      },
      message: 'File download URL generated successfully',
    });
  }
}