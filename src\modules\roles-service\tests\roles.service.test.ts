import RolesService from '../services/roles.service';
import { Role } from '../../auth-service/models/role.model';
import { Permission } from '../../auth-service/models/permission.model';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { db } from '../../../database/db';
import { PaginationDto } from '../../../utils/pagination';

jest.mock('../../auth-service/models/role.model');
jest.mock('../../auth-service/models/permission.model');
jest.mock('../../auth-service/models/role-permission.model');
jest.mock('../../audit-trail-service/services/audit-trail.service');
jest.mock('../../../database/db');
jest.mock('../../../utils/logger', () => ({ error: jest.fn(), info: jest.fn() }));


jest.mock('../../../config/env.config', () => ({
  config: {
    VALID_DOMAINS: ['@company.com'],
    FRONTEND_URL: ['https://frontend.com'],
    DEFAULT_EMAIL_SENDER: '<EMAIL>',
  }
}));

// Add mockRole, mockPermission, and RolePermission mocks
const mockRole = {
  id: 'role-id',
  name: 'TestRole',
  description: 'desc',
  permissions: [],
  setPermissions: jest.fn(),
  addPermissions: jest.fn(),
  update: jest.fn(),
  destroy: jest.fn(),
};

const mockPermission = {
  id: 'perm-id',
  module: 'users',
  action: 'create',
  description: 'desc',
  save: jest.fn().mockResolvedValue(this),
};

const RolePermission = {
  findOrCreate: jest.fn(),
  destroy: jest.fn(),
};


describe('RoleService', () => {
  const service = new RolesService();
  const mockAuditLog = AuditLogService.logEvent as jest.Mock;

  afterEach(() => {
    jest.clearAllMocks();
  });


  describe('listAllRoles', () => {
    it('should return paginated roles', async () => {
      (Role.findAndCountAll as jest.Mock).mockResolvedValue({ count: 1, rows: [mockRole] });
      const result = await service.listAllRoles(new PaginationDto({}), { searchQuery: '' });
      expect(result.data.length).toBe(1);
      expect(result.meta.itemCount).toBe(1);
    });

    it('should handle errors', async () => {
      (Role.findAndCountAll as jest.Mock).mockRejectedValue(new Error('DB error'));
      await expect(service.listAllRoles(new PaginationDto({}), { searchQuery: '' })).rejects.toThrow('DB error');
    });
  });

  describe('createRole', () => {
    it('should create a new role and assign permissions', async () => {
      (Role.findOrCreate as jest.Mock).mockResolvedValueOnce([mockRole, true]);
      (Permission.findOrCreate as jest.Mock).mockResolvedValue([mockPermission]);
      (Role.findByPk as jest.Mock).mockResolvedValue({ ...mockRole, permissions: [mockPermission] });

      const result = await service.createRole({
        name: 'TestRole',
        permissions: [{ module: 'users', action: 'create', description: 'desc' }],
        description: 'desc',
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
      });
      expect(result).toHaveProperty('permissions');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw if role already exists', async () => {
      (Role.findOrCreate as jest.Mock).mockResolvedValueOnce([mockRole, false]);
      await expect(service.createRole({
        name: 'TestRole',
        permissions: [],
        description: 'desc',
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
      })).rejects.toThrow('Role already exists');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      (Role.findOrCreate as jest.Mock).mockRejectedValue(new Error('DB error'));
      await expect(service.createRole({
        name: 'TestRole',
        permissions: [],
        description: 'desc',
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
      })).rejects.toThrow('DB error');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  describe('updateRolePermissions', () => {
    it('should update role permissions and name', async () => {
      (Role.findByPk as jest.Mock).mockResolvedValue({ ...mockRole, setPermissions: jest.fn(), update: jest.fn() });
      (Role.findOne as jest.Mock).mockResolvedValue(null);
      (Permission.findOrCreate as jest.Mock).mockResolvedValue([mockPermission, true]);
      (Role.findByPk as jest.Mock).mockResolvedValueOnce({ ...mockRole, permissions: [mockPermission] });

      const result = await service.updateRolePermissions({
        roleId: 'role-id',
        roleDescription: 'desc',
        permissions: [{ module: 'users', action: 'create', description: 'desc' }],
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
        roleName: 'NewRole',
      });
      expect(result).toHaveProperty('permissions');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw if role not found', async () => {
      (Role.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.updateRolePermissions({
        roleId: 'role-id',
        roleDescription: 'desc',
        permissions: [],
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
      })).rejects.toThrow('Role not found');
    });

    it('should throw if role name already exists', async () => {
      (Role.findByPk as jest.Mock).mockResolvedValue(mockRole);
      (Role.findOne as jest.Mock).mockResolvedValue(mockRole);
      await expect(service.updateRolePermissions({
        roleId: 'role-id',
        roleDescription: 'desc',
        permissions: [],
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
        roleName: 'NewRole',
      })).rejects.toThrow('Role name already exists');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      (Role.findByPk as jest.Mock).mockRejectedValue(new Error('DB error'));
      await expect(service.updateRolePermissions({
        roleId: 'role-id',
        roleDescription: 'desc',
        permissions: [],
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
      })).rejects.toThrow('DB error');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  describe('getRoleById', () => {
    it('should return role by id', async () => {
      (Role.findByPk as jest.Mock).mockResolvedValue(mockRole);
      const result = await service.getRoleById('role-id');
      expect(result).toEqual(mockRole);
    });

    it('should throw if not found', async () => {
      (Role.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.getRoleById('role-id')).rejects.toThrow('Role not found');
    });

    it('should handle errors', async () => {
      (Role.findByPk as jest.Mock).mockRejectedValue(new Error('DB error'));
      await expect(service.getRoleById('role-id')).rejects.toThrow('DB error');
    });
  });

  describe('fetchRolePermissions', () => {
    it('should return paginated permissions for a role', async () => {
      (Role.findByPk as jest.Mock).mockResolvedValue(mockRole);
      (Permission.findAndCountAll as jest.Mock).mockResolvedValue({ count: 1, rows: [mockPermission] });
      const result = await service.fetchRolePermissions('role-id', new PaginationDto({}));
      expect(result.data.length).toBe(1);
      expect(result.meta.itemCount).toBe(1);
    });

    it('should throw if role not found', async () => {
      (Role.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.fetchRolePermissions('role-id', new PaginationDto({}))).rejects.toThrow('Role not found');
    });

    it('should handle errors', async () => {
      (Role.findByPk as jest.Mock).mockRejectedValue(new Error('DB error'));
      await expect(service.fetchRolePermissions('role-id', new PaginationDto({}))).rejects.toThrow('DB error');
    });
  });

  describe('listAvailablePermissions', () => {
    it('should return paginated permissions', async () => {
      (Permission.findAndCountAll as jest.Mock).mockResolvedValue({ count: 1, rows: [mockPermission] });
      const result = await service.listAvailablePermissions(new PaginationDto({}), { searchQuery: '' });
      expect(result.data.length).toBe(1);
      expect(result.meta.itemCount).toBe(1);
    });

    it('should handle errors', async () => {
      (Permission.findAndCountAll as jest.Mock).mockRejectedValue(new Error('DB error'));
      await expect(service.listAvailablePermissions(new PaginationDto({}), { searchQuery: '' })).rejects.toThrow('DB error');
    });
  });

  describe('assignPermissionToRole', () => {
    it('should assign permission to role', async () => {
      (Role.findByPk as jest.Mock).mockResolvedValue(mockRole);
      (Permission.findByPk as jest.Mock).mockResolvedValue(mockPermission);
      (RolePermission.findOrCreate as jest.Mock).mockResolvedValue([{}]);
      (mockPermission.save as jest.Mock).mockResolvedValue(mockPermission);

      const payload = {
        roleId: 'role-id',
        permissionId: 'perm-id',
        moduleName: 'users',
        action: 'create' as 'create' | 'read' | 'update' | 'delete' | 'approve',
        description: 'desc',
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
      };
      const result = await service.assignPermissionToRole(payload);
      expect(result).toEqual(mockPermission);
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw if role not found', async () => {
      (Role.findByPk as jest.Mock).mockResolvedValue(null);
      const payload = {
        roleId: 'role-id',
        permissionId: 'perm-id',
        moduleName: 'users',
        action: 'create' as 'create' | 'read' | 'update' | 'delete' | 'approve',
        description: 'desc',
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
      };
      await expect(service.assignPermissionToRole(payload)).rejects.toThrow('Role with ID role-id not found.');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw if permission not found', async () => {
      (Role.findByPk as jest.Mock).mockResolvedValue(mockRole);
      (Permission.findByPk as jest.Mock).mockResolvedValue(null);
      const payload = {
        roleId: 'role-id',
        permissionId: 'perm-id',
        moduleName: 'users',
        action: 'create' as 'create' | 'read' | 'update' | 'delete' | 'approve',
        description: 'desc',
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
      };
      await expect(service.assignPermissionToRole(payload)).rejects.toThrow('Permission with ID perm-id not found.');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      (Role.findByPk as jest.Mock).mockRejectedValue(new Error('DB error'));
      const payload = {
        roleId: 'role-id',
        permissionId: 'perm-id',
        moduleName: 'users',
        action: 'create' as 'create' | 'read' | 'update' | 'delete' | 'approve',
        description: 'desc',
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
      };
      await expect(service.assignPermissionToRole(payload)).rejects.toThrow('DB error');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  describe('deleteRole', () => {
    it('should delete a role', async () => {
      const destroyMock = jest.fn();
      const transactionMock = {
        commit: jest.fn(),
        rollback: jest.fn(),
      };
      (Role.findByPk as jest.Mock).mockResolvedValue({ ...mockRole, destroy: destroyMock });
      (db.transaction as jest.Mock).mockResolvedValue(transactionMock);
      (RolePermission.destroy as jest.Mock).mockResolvedValue(1);

      await expect(service.deleteRole({
        roleId: 'role-id',
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
      })).resolves.toBeUndefined();
      expect(destroyMock).toHaveBeenCalled();
      expect(transactionMock.commit).toHaveBeenCalled();
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should throw if role not found', async () => {
      (Role.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.deleteRole({
        roleId: 'role-id',
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
      })).rejects.toThrow('Role not found');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      (Role.findByPk as jest.Mock).mockRejectedValue(new Error('DB error'));
      await expect(service.deleteRole({
        roleId: 'role-id',
        initiatorId: 'user-id',
        ipAddress: 'ip',
        userAgent: 'agent',
      })).rejects.toThrow('DB error');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });
});