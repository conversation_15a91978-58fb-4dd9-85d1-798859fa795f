import { User } from '../models/user.model';
import { Role } from '../models/role.model';
import { Permission } from '../models/permission.model';
import { JWTService } from '../../../utils/jwt';
import { Op } from 'sequelize';
import { PasswordHelper } from '../../../utils/password.utils';
import { AuditLogEnum, ErrorCode } from '../../../enum/trail-action.enum';
import { LoginPayload, RegisterSystemAdminPayload } from '../../../interfaces/auth.interface';
import { PhoneNumberHelper } from '../../../utils/phonenumber.util';
import { RoleEnum } from '../../..//enum/role.enum';
import { ModuleEnum } from '../../../enum/module.enum';
import { config } from '../../../config/env.config';
import logger from '../../../utils/logger';
import { OtpHelper } from '../../../utils/otp-helper';
import redis from '../../../utils/redis-client';
import { JwtBlacklist } from '../models/jwt.model';
import { Invitation } from '../models/invitation.model';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { UserProfile } from '../../../interfaces/user.interface';
import { UserStatus } from '../../../enum/user-status.enum';
import { EMAIL_QUEUE } from '../../../constants/role-permission.constant';
import { rabbitMQService } from '../../workers/rabbitmq/rabbitmq.service';
import { AppError } from '../../../utils/custom-error';

/**
 * AuthService class
 * Handles authentication-related operations
 */
export default class AuthService {
  private readonly messageQueue = rabbitMQService;


  /**
   * Constructor
   */
  // The constructor is empty, but will add any initialization logic here when needed.
  constructor() { }

  /**
   * Register System Admnin method
   * @param email User email
   * @param password User password
   * @param first_name User first name
   * @param last_name User last name
   * @param phone_number Optional phone number
   * @param roleId Role assigned to the user
   * @returns Promise<{ user: { id: string; email: string; role: string; first_name: string; last_name: string; phone_number?: string; roleId: string; active: boolean; permissions: Record<string, string[]> } }>
   */
  async registerSystemAdmin({
    email,
    password,
    first_name,
    last_name,
    phone_number,
    department,
    roleId,
    ipAddress,
    userAgent,
  }: RegisterSystemAdminPayload): Promise<{
    token: string;
    id: string;
    email: string;
    roleId: string;
    role: string;
    first_name: string;
    last_name: string;
    phone_number?: string;
    status: UserStatus;
    tokenExpiresIn: number;
    department: string;
    teamId: string
    permissions: Record<string, string[]>;
  }> {
    try {
      if (phone_number) {
        phone_number = PhoneNumberHelper.formatToCountryStandard(phone_number);
      }
      const whereClause: any[] = [{ email }];
      if (phone_number) {
        whereClause.push({ phone_number });
      }
      const existingUser = await User.findOne({
        where: {
          [Op.or]: whereClause,
        },
      });
      if (existingUser) {
        if (existingUser.email === email) throw new AppError('Email already exists', ErrorCode.CONFLICT);
        if (existingUser.phone_number === phone_number)
          throw new AppError('Phone number already exists');
      }

      const userPassword = password ? password : OtpHelper.generatePassword(10);

      const hashedPassword = await PasswordHelper.hashPassword(userPassword);

      const role = await Role.findByPk(roleId);
      if (!role) throw new AppError('Role not found', ErrorCode.NOT_FOUND);

      if (role.name !== RoleEnum.SYSTEM_ADMIN) {
        logger.error('Unauthorized registration attempt');
        throw new AppError('Forbidden', ErrorCode.FORBIDDEN);
      }

      const user = await User.create({
        email,
        password: hashedPassword,
        first_name,
        last_name,
        phone_number,
        roleId,
        department,
        user_type: role.name,
        status: UserStatus.ACTIVE,
      });

      const permissions = role.permissions?.reduce(
        (acc, perm) => {
          if (!acc[perm.module]) acc[perm.module] = [];
          acc[perm.module].push(perm.action);
          return acc;
        },
        {} as Record<string, string[]>,
      );

      const { token, tokenExpiresIn } = JWTService.generate(user, 604800); // 1 week expiration

      const loginUrl = `${config.FRONTEND_URL[0]}/login`;

      // send email to user
      await this.messageQueue.sendToQueue(EMAIL_QUEUE, {
        to: user.email,
        subject: 'Welcome to KBC Brokers',
        templateName: 'super-admin-email.html',
        data: {
          first_name: user.first_name,
          email: user.email,
          password: userPassword,
          loginUrl
        },
      });

      return {
        id: user.id,
        email: user.email,
        roleId: user.roleId,
        role: role.name,
        first_name: user.first_name,
        last_name: user.last_name,
        phone_number: user.phone_number,
        status: user.status,
        department: user.department,
        permissions,
        tokenExpiresIn,
        teamId: user.teamId,
        token,
      };
    } catch (error: any) {
      logger.error(`Error during system admin registration ${error}`);
      await AuditLogService.logEvent({
        userId: null,
        eventType: AuditLogEnum.REGISTER_FAILED,
        module: ModuleEnum.AUTH,
        action_description: `SuperAdmin Registration failed for email: ${email}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }


  /**
   * Login method
   * @param email - User email
   * @param password - User password
   * @param ipAddress - IP address
   * @param userAgent - Browser info
   * @returns {Promise<{ token: string; user: { id: string; email: string; role: string; roleId: string; first_name: string; last_name: string; phone_number?: string; active: boolean; permissions: Record<string, string[]> } }>}
   */
  async login({ email, password, ipAddress, userAgent }: LoginPayload): Promise<{
    token: string;
    id: string;
    email: string;
    roleId: string;
    role: string;
    first_name: string;
    last_name: string;
    first_login?: boolean;
    phone_number?: string;
    status: UserStatus;
    department: string;
    tokenExpiresIn: number;
    multifactor_auth: boolean;
    teamId: string;
    profile_picture: string;
    permissions: Record<string, string[]>;
  }> {
    try {
      if (!config.VALID_DOMAINS.some((domain) => email.endsWith(domain))) {
        await AuditLogService.logEvent({
          userId: null,
          eventType: AuditLogEnum.LOGIN_FAILED,
          module: ModuleEnum.AUTH,
          action_description: `Invalid email domain: ${email}`,
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('Invalid email domain. Please use an official company email', ErrorCode.BAD_REQUEST);
      }

      const user = await User.findOne({
        where: { email, status: UserStatus.ACTIVE },
        include: [
          {
            model: Role,
            as: 'role',
            include: [{ model: Permission, as: 'permissions' }],
          },
        ],
      });
      if (!user) {
        await AuditLogService.logEvent({
          userId: null,
          eventType: AuditLogEnum.LOGIN_FAILED,
          module: ModuleEnum.AUTH,
          action_description: `Invalid email or password for email: ${email}`,
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('Invalid email or password, please verify your details and try again.', ErrorCode.BAD_REQUEST);
      }

      if (user.first_login && user.user_type !== RoleEnum.SYSTEM_ADMIN) {
        await AuditLogService.logEvent({
          userId: user.id,
          eventType: AuditLogEnum.LOGIN_FAILED,
          module: ModuleEnum.AUTH,
          action_description: `User ${user.email} must reset password`,
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError(
          'Please reset your password before logging in. Click on "Forgot Password" to reset it.',
          ErrorCode.BAD_REQUEST
        );
      }

      if (user.locked_until && new Date(user.locked_until) > new Date()) {
        const minutesLeft = Math.ceil(
          (new Date(user.locked_until).getTime() - new Date().getTime()) / 60000,
        );
        await AuditLogService.logEvent({
          userId: user.id,
          eventType: AuditLogEnum.LOGIN_FAILED,
          module: ModuleEnum.AUTH,
          action_description: `User ${user.email} account is locked for ${minutesLeft} minutes`,
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError(`Account locked. Try again in ${minutesLeft} minute(s).`, ErrorCode.BAD_REQUEST);
      }

      const isPasswordValid = await PasswordHelper.comparePassword(password, user.password);
      if (!isPasswordValid) {
        user.login_attempts = (user.login_attempts || 0) + 1;
        if (user.login_attempts >= 5) {
          user.locked_until = new Date(Date.now() + 30 * 60 * 1000); // lock for 30 minutes
        }
        await user.save();
        await AuditLogService.logEvent({
          userId: user.id,
          eventType: AuditLogEnum.LOGIN_FAILED,
          module: ModuleEnum.AUTH,
          action_description: `Invalid password for user ${user.email}`,
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        logger.error(`Login failed ${user.email}`);
        throw new AppError('Invalid credentials', ErrorCode.BAD_REQUEST);
      }

      if (user.two_factor_auth) {
        const otp = OtpHelper.generateOTP(6);
        await redis.setex(`otp:${user.id}`, 300, otp); // Store OTP in Redis for 5 minutes

        await this.messageQueue.sendToQueue(EMAIL_QUEUE, {
          to: user.email,
          subject: 'Your OTP Code',
          templateName: 'otp-email.html',
          data: {
            first_name: user.first_name,
            otp: otp,
          },
        });

        await AuditLogService.logEvent({
          userId: user.id,
          eventType: AuditLogEnum.LOGIN_SUCCESS,
          module: ModuleEnum.AUTH,
          action_description: `OTP sent to ${user.email}`,
          ip_address: ipAddress,
          device_info: userAgent,
        });

        return {
          id: user.id,
          email: user.email,
          roleId: user.roleId,
          role: user.role.name,
          first_name: user.first_name,
          last_name: user.last_name,
          phone_number: user.phone_number,
          status: user.status,
          department: user.department,
          multifactor_auth: true,
          teamId: null,
          permissions: {},
          profile_picture: user.profile_picture,
          tokenExpiresIn: null,
          token: null,
        };
      }

      const permissions: Record<string, string[]> = user.role?.permissions?.reduce(
        (acc: Record<string, string[]>, perm: { module: string; action: string }) => {
          if (!acc[perm.module]) acc[perm.module] = [];
          acc[perm.module].push(perm.action);
          return acc;
        },
        {} as Record<string, string[]>,
      );

      user.permissions = permissions;
      user.role = user.role.name;

      const { token, tokenExpiresIn } = JWTService.generate(user);
      const firstLogin = user.first_login;
      user.login_attempts = 0;
      user.locked_until = null;
      user.first_login = false;
      user.last_login = new Date();

      await user.save();

      await AuditLogService.logEvent({
        userId: user.id,
        eventType: AuditLogEnum.LOGIN_SUCCESS,
        module: ModuleEnum.AUTH,
        action_description: `User ${user.email} logged in successfully`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      return {
        id: user.id,
        email: user.email,
        roleId: user.roleId,
        role: user.role,
        first_name: user.first_name,
        last_name: user.last_name,
        phone_number: user.phone_number,
        first_login: firstLogin,
        status: user.status,
        permissions,
        department: user.department,
        teamId: user?.teamId,
        tokenExpiresIn,
        profile_picture: user.profile_picture,
        multifactor_auth: user.two_factor_auth,
        token,
      };
    } catch (error: any) {
      await AuditLogService.logEvent({
        userId: null,
        eventType: AuditLogEnum.LOGIN_FAILED,
        module: ModuleEnum.AUTH,
        action_description: `Error during login: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error during login ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Verify OTP method
   * @param userId - User ID
   * @param otp - OTP code
   * @returns {Promise<{ token: string; user: { id: string; email: string; role: string; roleId: string; first_name: string; last_name: string; phone_number?: string; active: boolean; permissions: Record<string, string[]> } }>}
   */
  async verifyOTP(
    userId: string,
    otp: string,
    ipAddress: string,
    userAgent: string,
  ): Promise<{
    token: string;
    id: string;
    email: string;
    roleId: string;
    role: string;
    first_name: string;
    last_name: string;
    phone_number?: string;
    status: UserStatus;
    department: string;
    multifactor_auth: boolean;
    permissions: Record<string, string[]>;
    tokenExpiresIn: number;
  }> {
    try {
      const storedOtp = await redis.get(`otp:${userId}`);
      if (!storedOtp) throw new AppError('OTP expired', ErrorCode.BAD_REQUEST);

      if (storedOtp !== otp) {
        await AuditLogService.logEvent({
          userId,
          eventType: AuditLogEnum.LOGIN_FAILED,
          module: ModuleEnum.AUTH,
          action_description: `Invalid OTP for user ${userId}`,
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('Invalid OTP', ErrorCode.BAD_REQUEST);
      }

      await redis.del(`otp:${userId}`);

      const user = await User.findByPk(userId, {
        include: [
          {
            model: Role,
            as: 'role',
            include: [{ model: Permission, as: 'permissions' }],
          },
        ],
      });

      if (!user) {
        await AuditLogService.logEvent({
          userId,
          eventType: AuditLogEnum.LOGIN_FAILED,
          module: ModuleEnum.AUTH,
          action_description: `User not found for ID ${userId}`,
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('User not found', ErrorCode.NOT_FOUND);
      }

      const permissions: Record<string, string[]> = user.role?.permissions?.reduce(
        (acc: Record<string, string[]>, perm: { module: string; action: string }) => {
          if (!acc[perm.module]) acc[perm.module] = [];
          acc[perm.module].push(perm.action);
          return acc;
        },
        {} as Record<string, string[]>,
      );

      user.permissions = permissions;
      user.role = user.role.name;

      const { token, tokenExpiresIn } = JWTService.generate(user);

      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.SSO_LOGIN,
        module: ModuleEnum.AUTH,
        action_description: `User ${user.email} logged in successfully`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      return {
        id: user.id,
        email: user.email,
        roleId: user.roleId,
        role: user.role,
        first_name: user.first_name,
        last_name: user.last_name,
        phone_number: user.phone_number,
        status: user.status,
        multifactor_auth: user.two_factor_auth,
        department: user.department,
        permissions,
        tokenExpiresIn,
        token,
      };
    } catch (error) {
      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.API_ERROR,
        module: ModuleEnum.AUTH,
        action_description: `Error verifying OTP for user ${userId}: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error during OTP verification ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Resend Otp method
   * @param userId - User ID
   * @param ipAddress - IP address
   * @param userAgent - Browser info
   */

  async resendOtp(userId: string, ipAddress: string, userAgent: string): Promise<void> {
    try {
      const user = await User.findByPk(userId, {
        attributes: ['email', 'first_name']
      });
      if (!user) throw new AppError('User not found', ErrorCode.NOT_FOUND);

      const otp = OtpHelper.generateOTP(6);
      await redis.setex(`otp:${userId}`, 300, otp);

      await this.messageQueue.sendToQueue(EMAIL_QUEUE, {
        to: user.email,
        subject: 'Your OTP Code',
        templateName: 'otp-email.html',
        data: {
          first_name: user.first_name,
          otp: otp,
        },
      });

      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.OTHER,
        module: ModuleEnum.AUTH,
        action_description: `OTP resent to ${user.email}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });
    } catch (error) {
      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.API_ERROR,
        module: ModuleEnum.AUTH,
        action_description: `Error resending OTP for user ${userId}: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error during OTP resend ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get User Profile
   * @param userId - User ID
   * @param ipAddress - User IP address
   * @param userAgent - User device identifier
   */
  async getUserProfile(userId: string, ipAddress: string, userAgent: string): Promise<UserProfile> {
    try {
      const user = await User.findByPk(userId, {
        include: [
          {
            model: Role,
            as: 'role',
            include: [{ model: Permission, as: 'permissions' }],
          },
        ],
      });
      if (!user) throw new AppError('User not found', ErrorCode.NOT_FOUND);

      const permissions = user.role?.permissions?.reduce(
        (acc: Record<string, string[]>, perm: { module: string; action: string }) => {
          if (!acc[perm.module]) acc[perm.module] = [];
          acc[perm.module].push(perm.action);
          return acc;
        },
        {} as Record<string, string[]>,
      );

      user.permissions = permissions;
      user.role = user.role?.name;

      return {
        id: user.id,
        email: user.email,
        roleId: user.roleId,
        role: user.role,
        first_name: user.first_name,
        last_name: user.last_name,
        phone_number: user.phone_number,
        department: user.department,
        status: user.status,
        multifactor_auth: user.two_factor_auth,
        profile_picture: user.profile_picture,
        permissions,
        last_login: user.last_login,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };
    } catch (error) {
      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.API_ERROR,
        module: ModuleEnum.AUTH,
        action_description: `Error fetching user profile for user ${userId}: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error fetching user profile ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Forgot Password
   * @param email - User email
   * @returns {Promise<void>}
   */

  async forgotPassword(email: string, ipAddress: string, userAgent: string): Promise<void | string> {
    try {
      const user = await User.findOne({
        where: {
          [Op.and]: [{ email }, { status: UserStatus.ACTIVE }],
        },
      });

      if (!user)
        return 'We have sent you an email with a reset link if this email exists in our system';

      const { token } = JWTService.generate(user, 1800); // 30 minutes expiration

      const resetLink = `${config.FRONTEND_URL[0]}/reset-password?token=${token}&type=existingUser`;

      await this.messageQueue.sendToQueue(EMAIL_QUEUE, {
        to: user.email,
        subject: 'Password Reset',
        templateName: 'forgot-password-email.html',
        data: {
          first_name: user.first_name,
          resetLink,
        },
      });
    } catch (error) {
      await AuditLogService.logEvent({
        userId: null,
        eventType: AuditLogEnum.API_ERROR,
        module: ModuleEnum.AUTH,
        action_description: `Error during forgot password for email ${email}: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error during forgot password ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Reset Password
   * @param token - JWT token
   * @param newPassword - New password
   * @param confirmPassword - Confirm password
   * @param type - Type of reset (newUser or existingUser)
   * @returns {Promise<void>}
   */

  async resetPassword(
    token: string,
    newPassword: string,
    confirmPassword: string,
    type: string,
    ipAddress: string,
    userAgent: string,
  ): Promise<void> {
    try {
      if (newPassword !== confirmPassword) throw new AppError('Passwords do not match', ErrorCode.BAD_REQUEST);

      if (!token) throw new AppError('token is required', ErrorCode.BAD_REQUEST);

      const decoded = (await JWTService.verifyToken(token)) as any;
      const user = await User.findByPk(decoded.userId);
      if (!user) throw new AppError('User not found', ErrorCode.NOT_FOUND);

      if (type === 'newUser') {
        const invitation = await Invitation.findOne({
          where: {
            invite_token: token,
            invitee_email: user.email,
            expires_at: {
              [Op.gt]: new Date(),
            },
          },
        });

        if (!invitation) throw new AppError('Invalid or expired invitation token', ErrorCode.BAD_REQUEST);
        await invitation.update({ is_accepted: true });

      }

      const hashedPassword = await PasswordHelper.hashPassword(newPassword);
      user.password = hashedPassword;
      user.locked_until = null;
      user.login_attempts = 0;
      user.first_login = false;
      user.status = UserStatus.ACTIVE;
      await user.save();

      await this.messageQueue.sendToQueue(EMAIL_QUEUE, {
        to: user.email,
        subject: 'Password Reset Successful',
        templateName: 'password-reset-success-email.html',
        data: {
          first_name: user.first_name,
        },
      });
    } catch (error) {
      await AuditLogService.logEvent({
        userId: null,
        eventType: AuditLogEnum.API_ERROR,
        module: ModuleEnum.AUTH,
        action_description: `Error during password reset for token ${token}: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error during password reset ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Enable or disable two factor authentication
   * @param userId - User ID
   * @param enable - Enable or disable 2FA
   * @returns {Promise<void>}
   */

  async disableEnable2FA(userId: string, enable: boolean, ipAddress: string, userAgent: string): Promise<void> {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        logger.error(`User not found ${userId}`);
        throw new AppError('User not found', ErrorCode.NOT_FOUND);
      }

      user.two_factor_auth = enable;
      await user.save();

      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.OTHER,
        module: ModuleEnum.AUTH,
        action_description: `User ${userId} has ${enable ? 'enabled' : 'disabled'} 2FA`,
        ip_address: ipAddress,
        device_info: userAgent,
      });
    } catch (error) {
      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.API_ERROR,
        module: ModuleEnum.AUTH,
        action_description: `Error during 2FA for user ${userId}: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error during 2FA ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Logout method
   * @param data - Object containing userId, token, ipAddress, and userAgent
   * @returns {Promise<void>}
   * @throws Error if the user is not found
   */

  async logout(data: {
    userId: string;
    token: string;
    ipAddress: string;
    userAgent: string;
  }): Promise<void> {
    try {
      await JwtBlacklist.create({
        token: data.token,
        blacklistedAt: new Date(),
      });
      await AuditLogService.logEvent({
        userId: data.userId,
        eventType: AuditLogEnum.LOGOUT,
        module: ModuleEnum.AUTH,
        action_description: `User ${data.userId} logged out successfully`,
        ip_address: data.ipAddress,
        device_info: data.userAgent,
      });
    } catch (error) {
      await AuditLogService.logEvent({
        userId: data.userId,
        eventType: AuditLogEnum.LOGOUT_FAILED,
        module: ModuleEnum.AUTH,
        action_description: `Logout failed for user ${data.userId}: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: data.ipAddress,
        device_info: data.userAgent,
      });
      logger.error(`Error during logout ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Refresh token method
   * @param token - JWT token
   * @param userId - User ID
   * @param ipAddress - IP address
   * @param userAgent - Browser info
   * @returns {Promise<{ token: string; user: { id: string; email: string; role: string; roleId: string; first_name: string; last_name: string; phone_number?: string; active: boolean; permissions: Record<string, string[]> } }>}
   * @throws Error if the token is invalid or expired
   */
  async refreshToken(token: string, ipAddress: string, userAgent: string): Promise<{
    token: string;
    id: string;
    email: string;
    roleId: string;
    role: string;
    first_name: string;
    last_name: string;
    phone_number?: string;
    status: UserStatus;
    department: string;
    first_login?: boolean;
    multifactor_auth: boolean;
    permissions: Record<string, string[]>;
    tokenExpiresIn: number;
  }> {
    try {
      const decoded = JWTService.verifyRefreshToken(token) as any;
      const user = await User.findByPk(decoded.userId, {
        include: [
          {
            model: Role,
            as: 'role',
            include: [{ model: Permission, as: 'permissions' }],
          },
        ],
      });
      if (!user) throw new AppError('User not found', ErrorCode.NOT_FOUND);

      const permissions = user.role?.permissions?.reduce(
        (acc: Record<string, string[]>, perm: { module: string; action: string }) => {
          if (!acc[perm.module]) acc[perm.module] = [];
          acc[perm.module].push(perm.action);
          return acc;
        },
        {} as Record<string, string[]>,
      );

      user.permissions = permissions;
      user.role = user.role.name;

      const { token: newToken, tokenExpiresIn } = JWTService.generate(user);

      return {
        id: user.id,
        email: user.email,
        roleId: user.roleId,
        role: user.role,
        first_name: user.first_name,
        last_name: user.last_name,
        phone_number: user.phone_number,
        status: user.status,
        department: user.department,
        first_login: user.first_login,
        multifactor_auth: user.two_factor_auth,
        permissions,
        tokenExpiresIn,
        token: newToken,
      };
    } catch (error) {
      await AuditLogService.logEvent({
        userId: null,
        eventType: AuditLogEnum.API_ERROR,
        module: ModuleEnum.AUTH,
        action_description: `Token refresh failed: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error during token refresh ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Change password method
   * @param userId - User ID
   * @param oldPassword - Old password
   * @param newPassword - New password
   * @param ipAddress - IpAddress of user
   * @param userAgent - User device identifier
   */

  async changePassword({
    userId,
    oldPassword,
    newPassword,
    ipAddress,
    userAgent,
  }): Promise<void> {
    try {
      const user = await User.findByPk(userId);
      if (!user) throw new AppError('User not found', ErrorCode.NOT_FOUND);

      const isPasswordValid = await PasswordHelper.comparePassword(oldPassword, user.password);
      if (!isPasswordValid) {
        await AuditLogService.logEvent({
          userId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.AUTH,
          action_description: `Invalid old password for user ${user.email}`,
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('password does not match', ErrorCode.BAD_REQUEST);
      }

      const hashedPassword = await PasswordHelper.hashPassword(newPassword);
      user.password = hashedPassword;
      await user.save();

      await this.messageQueue.sendToQueue(EMAIL_QUEUE, {
        to: user.email,
        subject: 'Password Changed Successfully',
        templateName: 'change-password-email.html',
        data: {
          first_name: user.first_name,
        },
      });

      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.AUTH,
        action_description: `User ${user.email} changed password successfully`,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      return;
    } catch (error) {
      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.API_ERROR,
        module: ModuleEnum.AUTH,
        action_description: `Error during password change for user ${userId}: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error during password change ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update User Profile Picture url
   * @param userId - User ID
   * @param profile_picture - New profile picture URL
   * @param ipAddress - User IP address
   * @param userAgent - User device identifier
   * @throws {AppError} if user is not found
   * @returns 
   */
  async updateUserProfile(
    userId: string,
    profile_picture: string,
    ipAddress: string,
    userAgent: string,
  ): Promise<{
    id: string;
    email: string;
    roleId: string;
    role?: string;
    first_name: string;
    last_name: string;
    phone_number?: string;
    status: UserStatus;
    department: string;
    profile_picture: string;
  }> {
    try {
      const user = await User.findByPk(userId);

      if (!user) {
        throw new AppError('User not found', ErrorCode.NOT_FOUND);
      }

      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.AUTH,
        action_description: `User ${user.email} updated profile picture`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      user.profile_picture = profile_picture;
      await user.save();

      // remove password
      return {
        id: user.id,
        email: user.email,
        roleId: user.roleId,
        role: user.role?.name,
        first_name: user.first_name,
        last_name: user.last_name,
        phone_number: user.phone_number,
        status: user.status,
        department: user.department,
        profile_picture: user.profile_picture,
      };
    } catch (error) {
      logger.error(`Error updating user profile: ${error}`);
      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.API_ERROR,
        module: ModuleEnum.AUTH,
        action_description: `Error updating profile for user ${userId}: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }
}
