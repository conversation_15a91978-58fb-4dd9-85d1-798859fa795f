import { z } from 'zod';
import { BrokingSlipStatusEnum } from '../../../enum/broking-slip-status.enum';
import { TimeUnit } from '../../../enum/time-unit.enum';

/**
 * Schedule Item Schema
 */
const scheduleItemSchema = z.object({
  name: z.string({
    required_error: 'Name is required',
    invalid_type_error: 'Name must be a string',
  }).min(1, 'Name cannot be empty'),

  value: z.number({
    required_error: 'Value is required',
    invalid_type_error: 'Value must be a number',
  }).min(0, 'Value cannot be less than 0'),
}).catchall(z.any());

/**
 * Schedule Schema Definition
 */
const scheduleSchema = z.object({
  name: z.string({
    required_error: 'Schedule name is required',
    invalid_type_error: 'Schedule name must be a string',
  }).min(1, 'Schedule name cannot be empty'),

  data: z.array(scheduleItemSchema, {
    required_error: 'Schedule data is required',
    invalid_type_error: 'Schedule data must be an array of schedule items',
  }).min(1, 'Schedule must contain at least one item'),
});

/**
 * Untitled Section Item
 */
const sectionItemSchema = z.object({
  name: z.string({
    required_error: 'Untitled Section name is required',
    invalid_type_error: 'Untitle Section name must be a string',
  }).min(1, 'Untitled Section name cannot be empty'),

  label: z.string({
    required_error: 'Untitled Section label is required',
    invalid_type_error: 'Untitled Section label must be a string',
  }).min(1, 'Untitled Section label cannot be empty').optional(),

  value: z.string({
    required_error: 'Untitled Section value is required',
    invalid_type_error: ' Untitled Section value must be a string',
  }).min(1, 'Untitled Section value cannot be empty').optional(),
});

/**
 * Untitled Section Definition
 */
const sectionSchema = z.object({
  name: z.string({
    required_error: 'Section name is required',
    invalid_type_error: 'Section name must be a string',
  }).min(1, 'Section name cannot be empty'),

  data: z.array(sectionItemSchema, {
    required_error: 'Section data is required',
    invalid_type_error: 'Section data must be an array of items',
  }).min(1, 'Section must contain at least one item'),
});

/**
 * Extension Schema Definition
 */
export const extensionSchema = z.object({
  name: z.string({
    required_error: 'Extension name is required',
    invalid_type_error: 'Extension name must be a string',
  }),
  value: z.number({
    required_error: 'Extension value is required',
    invalid_type_error: 'Extension value must be a number',
  }),
});

/**
 * Discount Schema Definition
 */
export const discountSchema = z.object({
  name: z.string({
    required_error: 'Discount name is required',
    invalid_type_error: 'Discount name must be a string',
  }),
  value: z.number({
    required_error: 'Discount value is required',
    invalid_type_error: 'Discount value must be a number',
  }),
  enabled: z.boolean({
    required_error: 'Discount enabled flag is required',
    invalid_type_error: 'Discount enabled flag must be a boolean',
  }),
});

/**
 * Loading Schema Definition
 */
export const loadingSchema = z.object({
  name: z.string({
    required_error: 'Loading name is required',
    invalid_type_error: 'Loading name must be a string',
  }),
  value: z.number({
    required_error: 'Loading value is required',
    invalid_type_error: 'Loading value must be a number',
  }),
  enabled: z.boolean({
    required_error: 'Loading enabled flag is required',
    invalid_type_error: 'Loading enabled flag must be a boolean',
  }),
});


/**
 * Broking Slip Item schema definition
 */
export const brokingSlipItemSchema = z.object({
  policyId: z.string({
    required_error: 'policyId is required',
    invalid_type_error: 'Policy ID must be a UUID string'
  }).uuid('Policy ID must be a valid UUID'),

  summary_of_cover: z.string({
    required_error: 'Summary of cover is required',
    invalid_type_error: 'Summary of cover must be a string',
  }),

  policy_category: z.string({
    required_error: 'Policy category is required',
    invalid_type_error: 'Policy category must be a string',
  }),

  schedule: scheduleSchema.optional(),

  order: z.array(z.string({
    invalid_type_error: 'Order must be an array of strings',
  }), {
    required_error: 'Order is required',
    invalid_type_error: 'Order must be an array of strings',
  }).optional(),

  sections: z.array(sectionSchema, {
    required_error: 'Sections are required',
    invalid_type_error: 'Sections must be an array of section objects',
  }).optional(),


  supporting_documents: z.array(
    z.string({
      invalid_type_error: 'Supporting document must be a string',
    })
  ).min(1, {
    message: 'At least one supporting document is required'
  }).optional(),

  extensions: z.array(extensionSchema).optional(),

  // sum_insured: z.number({
  //   required_error: 'Sum insured is required',
  //   invalid_type_error: 'Sum insured must be a number',
  // }),
  base_rate: z.number({
    required_error: 'Base rate is required',
    invalid_type_error: 'Base rate must be a number',
  }).min(0, 'Base rate cannot be less than 0%')
    .max(100, 'Base rate cannot exceed 100%'),
  // .refine((val) => /^\d{1,3}(\.\d)?$/.test(val.toString()), {
  //   message: 'Base rate must have at most one decimal place',
  // }),

  broker_commission: z.number({
    required_error: 'Broker commission is required',
    invalid_type_error: 'Broker commission must be a number',
  }).min(0, 'Broker commission cannot be less than 0%')
    .max(100, 'Broker commission cannot exceed 100%'),
  // .refine((val) => /^\d{1,3}(\.\d)?$/.test(val.toString()), {
  //   message: 'Broker commission must have at most one decimal place',
  // }),

  discounts: z.array(discountSchema).optional(),

  loadings: z.array(loadingSchema).optional(),

  status: z
    .enum([
      BrokingSlipStatusEnum.DRAFT,
      BrokingSlipStatusEnum.SENT_TO_INSURER,
      BrokingSlipStatusEnum.QUOTATION_RECEIVED,
      BrokingSlipStatusEnum.QUOTATION_REVIEWED,
      BrokingSlipStatusEnum.FINAL_QUOTE_SHARED,
      BrokingSlipStatusEnum.ARCHIVED_CLOSED,
      BrokingSlipStatusEnum.APPROVED,
      BrokingSlipStatusEnum.REJECTED,
    ], {
      invalid_type_error: 'Status must be one of: draft, sent_to_insurer, quotation_received, quotation_reviewed, final_quote_shared_with_client, or archived_closed',
    })
    .optional()
    .default(BrokingSlipStatusEnum.DRAFT),

});


/**
 * Generate Broking Slip Schema
 */
export const generateBrokingSlipSchema = z.object({
  slips: z.array(brokingSlipItemSchema, {
    required_error: 'Slips is required',
    invalid_type_error: 'Slips must be an array of broking slips'
  }).min(1, {
    message: 'At least one broking slip is required',
  }),
  clientId: z.string({
    required_error: 'Client ID is required',
    invalid_type_error: 'Client ID must be a UUID string',
  }).uuid('Client ID must be a valid UUID'),
  coverage_time: z
    .number({
      required_error: 'Coverage time is required',
      invalid_type_error: 'Coverage time must be a number',
    }).optional(),

  coverage_period: z
    .nativeEnum(TimeUnit, {
      required_error: 'Coverage period is required',
      invalid_type_error: `Coverage period must be one of ${Object.values(TimeUnit).join(', ')}`,
    }).optional(),
});

export type IBrokingSlip = z.infer<typeof generateBrokingSlipSchema>;

// export const updateBrokingSlipSchema = generateBrokingSlipSchema.partial();
export const updateBrokingSlipSchema = z.object({
  status: z.nativeEnum(BrokingSlipStatusEnum, {
    required_error: 'Status is required',
    invalid_type_error: `Status must be one of: ${Object.values(BrokingSlipStatusEnum).join(', ')}`,
  }),
});

/**
 * Send Broking Slip to Insurer Schema
 */
export const sendBrokingSlipToInsurerSchema = z.object({
  brokingSlipId: z.string({
    required_error: 'Broking slip ID is required',
    invalid_type_error: 'Broking slip ID must be a UUID string',
  }).uuid('Broking slip ID must be a valid UUID'),

  recipients: z.array(
    z.object({
      insurer_id: z.string({
        required_error: 'Insurer ID is required',
        invalid_type_error: 'Insurer ID must be a UUID string',
      }).uuid('Insurer ID must be a valid UUID'),

      contact_ids: z.array(
        z.string({
          required_error: 'Contact ID is required',
          invalid_type_error: 'Contact ID must be a UUID string',
        }).uuid('Each Contact ID must be a valid UUID')
      ).min(1, 'At least one contact must be selected'),

      note: z.string({
        required_error: 'Note is required',
        invalid_type_error: 'Note must be a string',
      }).optional(),
    })
  ).min(1, 'At least one recipient must be specified'),
});


export type SendBrokingSlipToInsurerDTO = z.infer<typeof sendBrokingSlipToInsurerSchema>;
