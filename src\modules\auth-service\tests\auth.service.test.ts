jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    quit: jest.fn(),
    get: jest.fn(),
    setex: jest.fn(),
    del: jest.fn()
  }));
});

import AuthService from '../services/auth.service';
import { User } from '../models/user.model';
import { Role } from '../models/role.model';
import { JWTService } from '../../../utils/jwt';
import { PasswordHelper } from '../../../utils/password.utils';
import { PhoneNumberHelper } from '../../../utils/phonenumber.util';
import { RoleEnum } from '../../../enum/role.enum';
import { config } from '../../../config/env.config';
import logger from '../../../utils/logger';
import { OtpHelper } from '../../../utils/otp-helper';
import redis from '../../../utils/redis-client';
import { JwtBlacklist } from '../models/jwt.model';
import { Invitation } from '../models/invitation.model';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { UserStatus } from '../../../enum/user-status.enum';
import { rabbitMQService } from '../../workers/rabbitmq/rabbitmq.service';
import { AppError } from '../../../utils/custom-error';

jest.mock('../../../config/env.config', () => ({
  config: {
    VALID_DOMAINS: ['@company.com'],
    FRONTEND_URL: ['https://frontend.com'],
  }
}));

jest.mock('../models/user.model');
jest.mock('../models/role.model');
jest.mock('../models/permission.model');
jest.mock('../../../utils/jwt');
jest.mock('../../../utils/password.utils');
jest.mock('../../../utils/phonenumber.util', () => ({
  PhoneNumberHelper: { formatToCountryStandard: jest.fn((num) => num) }
}));
jest.mock('../../../utils/logger', () => ({ error: jest.fn(), info: jest.fn() }));
jest.mock('../../../utils/otp-helper');
jest.mock('../../../utils/redis-client');
jest.mock('../models/jwt.model');
jest.mock('../models/invitation.model');
jest.mock('../../audit-trail-service/services/audit-trail.service');
jest.mock('../../workers/rabbitmq/rabbitmq.service');

const mockUser = {
  id: 'user-id',
  email: '<EMAIL>',
  password: 'hashed',
  first_name: 'John',
  last_name: 'Doe',
  phone_number: '1234567890',
  department: 'IT',
  roleId: 'role-id',
  user_type: RoleEnum.SYSTEM_ADMIN,
  status: UserStatus.ACTIVE,
  save: jest.fn(),
  login_attempts: 0,
  locked_until: null,
  first_login: false,
  two_factor_auth: false,
  teamId: 'team-id',
  profile_picture: 'pic.png',
  last_login: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  permissions: {},
  role: { name: RoleEnum.SYSTEM_ADMIN, permissions: [{ module: 'users', action: 'create' }] }
};

const mockRole = {
  id: 'role-id',
  name: RoleEnum.SYSTEM_ADMIN,
  permissions: [{ module: 'users', action: 'create' }]
};

const mockToken = { token: 'jwt-token', tokenExpiresIn: 123456 };

const mockInvitation = {
  invite_token: 'invite-token',
  invitee_email: '<EMAIL>',
  expires_at: new Date(Date.now() + 10000),
  is_accepted: false,
  save: jest.fn()
};

const mockAuditLog = AuditLogService.logEvent as jest.Mock;
const mockSendToQueue = rabbitMQService.sendToQueue as jest.Mock;

describe('AuthService', () => {
  let service: AuthService;

  beforeEach(() => {
    service = new AuthService();
    jest.clearAllMocks();
    config.VALID_DOMAINS = ['@company.com'];
    config.FRONTEND_URL = ['https://frontend.com'];
    (PhoneNumberHelper.formatToCountryStandard as jest.Mock).mockImplementation((num) => num);
  });

  // registerSystemAdmin
  describe('registerSystemAdmin', () => {
    it('registers a new system admin', async () => {
      (User.findOne as jest.Mock).mockResolvedValue(null);
      (PasswordHelper.hashPassword as jest.Mock).mockResolvedValue('hashed');
      (Role.findByPk as jest.Mock).mockResolvedValue(mockRole);
      (User.create as jest.Mock).mockResolvedValue({ ...mockUser, id: 'new-user-id' });
      (JWTService.generate as jest.Mock).mockReturnValue(mockToken);

      const result = await service.registerSystemAdmin({
        email: '<EMAIL>',
        password: 'password',
        first_name: 'John',
        last_name: 'Doe',
        phone_number: '1234567890',
        department: 'IT',
        roleId: 'role-id',
        ipAddress: 'ip',
        userAgent: 'agent'
      });

      expect(result).toHaveProperty('id');
      expect(result.email).toBe('<EMAIL>');
    });

    it('throws if email exists', async () => {
      (User.findOne as jest.Mock).mockResolvedValue({ ...mockUser, email: '<EMAIL>' });
      await expect(service.registerSystemAdmin({
        email: '<EMAIL>',
        password: 'password',
        first_name: 'John',
        last_name: 'Doe',
        phone_number: '1234567890',
        department: 'IT',
        roleId: 'role-id',
        ipAddress: 'ip',
        userAgent: 'agent'
      })).rejects.toThrow('Email already exists');
    });

    it('throws if phone exists', async () => {
      (User.findOne as jest.Mock).mockResolvedValue({
        ...mockUser,
        email: '<EMAIL>', // Not the same as input email
        phone_number: '1234567890'
      });
      await expect(service.registerSystemAdmin({
        email: '<EMAIL>',
        password: 'password',
        first_name: 'John',
        last_name: 'Doe',
        phone_number: '1234567890',
        department: 'IT',
        roleId: 'role-id',
        ipAddress: 'ip',
        userAgent: 'agent'
      })).rejects.toThrow('Phone number already exists');
    });

    it('throws if role not found', async () => {
      (User.findOne as jest.Mock).mockResolvedValue(null);
      (Role.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.registerSystemAdmin({
        email: '<EMAIL>',
        password: 'password',
        first_name: 'John',
        last_name: 'Doe',
        phone_number: '1234567890',
        department: 'IT',
        roleId: 'role-id',
        ipAddress: 'ip',
        userAgent: 'agent'
      })).rejects.toThrow('Role not found');
    });

    it('throws if not SYSTEM_ADMIN', async () => {
      (User.findOne as jest.Mock).mockResolvedValue(null);
      (Role.findByPk as jest.Mock).mockResolvedValue({ ...mockRole, name: 'USER' });
      await expect(service.registerSystemAdmin({
        email: '<EMAIL>',
        password: 'password',
        first_name: 'John',
        last_name: 'Doe',
        phone_number: '1234567890',
        department: 'IT',
        roleId: 'role-id',
        ipAddress: 'ip',
        userAgent: 'agent'
      })).rejects.toThrow('Forbidden');
    });

    it('logs and throws on error', async () => {
      (User.findOne as jest.Mock).mockRejectedValue(new Error('fail'));
      await expect(service.registerSystemAdmin({
        email: '<EMAIL>',
        password: 'password',
        first_name: 'John',
        last_name: 'Doe',
        phone_number: '1234567890',
        department: 'IT',
        roleId: 'role-id',
        ipAddress: 'ip',
        userAgent: 'agent'
      })).rejects.toThrow('fail');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  // login
  describe('login', () => {
    it('throws for invalid domain', async () => {
      config.VALID_DOMAINS = ['@company.com'];
      await expect(service.login({
        email: '<EMAIL>',
        password: 'pass',
        ipAddress: 'ip',
        userAgent: 'agent'
      })).rejects.toThrow('Invalid email domain');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('throws for user not found', async () => {
      (User.findOne as jest.Mock).mockResolvedValue(null);
      await expect(service.login({
        email: '<EMAIL>',
        password: 'pass',
        ipAddress: 'ip',
        userAgent: 'agent'
      })).rejects.toThrow('Invalid email or password');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('throws for first login and not system admin', async () => {
      (User.findOne as jest.Mock).mockResolvedValue({ ...mockUser, first_login: true, user_type: 'USER' });
      await expect(service.login({
        email: '<EMAIL>',
        password: 'pass',
        ipAddress: 'ip',
        userAgent: 'agent'
      })).rejects.toThrow('Please reset your password before logging in');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('throws for locked account', async () => {
      (User.findOne as jest.Mock).mockResolvedValue({ ...mockUser, locked_until: new Date(Date.now() + 60000) });
      await expect(service.login({
        email: '<EMAIL>',
        password: 'pass',
        ipAddress: 'ip',
        userAgent: 'agent'
      })).rejects.toThrow(/Account locked/);
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('throws for invalid password', async () => {
      (User.findOne as jest.Mock).mockResolvedValue({ ...mockUser });
      (PasswordHelper.comparePassword as jest.Mock).mockResolvedValue(false);
      await expect(service.login({
        email: '<EMAIL>',
        password: 'wrong',
        ipAddress: 'ip',
        userAgent: 'agent'
      })).rejects.toThrow('Invalid credentials');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('returns 2FA response if enabled', async () => {
      (User.findOne as jest.Mock).mockResolvedValue({ ...mockUser, two_factor_auth: true, role: { name: RoleEnum.SYSTEM_ADMIN, permissions: [] } });
      (PasswordHelper.comparePassword as jest.Mock).mockResolvedValue(true);
      (OtpHelper.generateOTP as jest.Mock).mockReturnValue('123456');
      (redis.setex as jest.Mock).mockResolvedValue(true);
      (mockSendToQueue as jest.Mock).mockResolvedValue(true);

      const result = await service.login({
        email: '<EMAIL>',
        password: 'pass',
        ipAddress: 'ip',
        userAgent: 'agent'
      });
      expect(result.multifactor_auth).toBe(true);
      expect(mockSendToQueue).toHaveBeenCalled();
    });

    it('returns token for valid login', async () => {
      (User.findOne as jest.Mock).mockResolvedValue({ ...mockUser, role: { name: RoleEnum.SYSTEM_ADMIN, permissions: [{ module: 'users', action: 'create' }] } });
      (PasswordHelper.comparePassword as jest.Mock).mockResolvedValue(true);
      (JWTService.generate as jest.Mock).mockReturnValue(mockToken);

      const result = await service.login({
        email: '<EMAIL>',
        password: 'pass',
        ipAddress: 'ip',
        userAgent: 'agent'
      });
      expect(result.token).toBe('jwt-token');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('logs and throws on error', async () => {
      (User.findOne as jest.Mock).mockRejectedValue(new Error('fail'));
      await expect(service.login({
        email: '<EMAIL>',
        password: 'pass',
        ipAddress: 'ip',
        userAgent: 'agent'
      })).rejects.toThrow('fail');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  // verifyOTP
  describe('verifyOTP', () => {
    it('throws if OTP expired', async () => {
      (redis.get as jest.Mock).mockResolvedValue(null);
      await expect(service.verifyOTP('user-id', '123456', 'ip', 'agent')).rejects.toThrow('OTP expired');
    });

    it('throws if OTP invalid', async () => {
      (redis.get as jest.Mock).mockResolvedValue('654321');
      await expect(service.verifyOTP('user-id', '123456', 'ip', 'agent')).rejects.toThrow('Invalid OTP');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('throws if user not found', async () => {
      (redis.get as jest.Mock).mockResolvedValue('123456');
      (User.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.verifyOTP('user-id', '123456', 'ip', 'agent')).rejects.toThrow('User not found');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('returns token for valid OTP', async () => {
      (redis.get as jest.Mock).mockResolvedValue('123456');
      (User.findByPk as jest.Mock).mockResolvedValue({ ...mockUser, role: { name: RoleEnum.SYSTEM_ADMIN, permissions: [{ module: 'users', action: 'create' }] } });
      (JWTService.generate as jest.Mock).mockReturnValue(mockToken);

      const result = await service.verifyOTP('user-id', '123456', 'ip', 'agent');
      expect(result.token).toBe('jwt-token');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('logs and throws on error', async () => {
      (redis.get as jest.Mock).mockRejectedValue(new Error('fail'));
      await expect(service.verifyOTP('user-id', '123456', 'ip', 'agent')).rejects.toThrow('fail');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  // resendOtp
  describe('resendOtp', () => {
    it('throws if user not found', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.resendOtp('user-id', 'ip', 'agent')).rejects.toThrow('User not found');
    });

    it('sends OTP if user found', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue({ ...mockUser });
      (OtpHelper.generateOTP as jest.Mock).mockReturnValue('123456');
      (redis.setex as jest.Mock).mockResolvedValue(true);
      (mockSendToQueue as jest.Mock).mockResolvedValue(true);

      await service.resendOtp('user-id', 'ip', 'agent');
      expect(mockSendToQueue).toHaveBeenCalled();
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('logs and throws on error', async () => {
      (User.findByPk as jest.Mock).mockRejectedValue(new Error('fail'));
      await expect(service.resendOtp('user-id', 'ip', 'agent')).rejects.toThrow('fail');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  // getUserProfile
  describe('getUserProfile', () => {
    it('throws if user not found', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.getUserProfile('user-id', 'ip', 'agent')).rejects.toThrow('User not found');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('returns user profile', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue({ ...mockUser, role: { name: RoleEnum.SYSTEM_ADMIN, permissions: [{ module: 'users', action: 'create' }] } });
      const result = await service.getUserProfile('user-id', 'ip', 'agent');
      expect(result.id).toBe('user-id');
      expect(result.email).toBe('<EMAIL>');
    });

    it('logs and throws on error', async () => {
      (User.findByPk as jest.Mock).mockRejectedValue(new Error('fail'));
      await expect(service.getUserProfile('user-id', 'ip', 'agent')).rejects.toThrow('fail');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  // forgotPassword
  describe('forgotPassword', () => {
    it('returns message if user not found', async () => {
      (User.findOne as jest.Mock).mockResolvedValue(null);
      const result = await service.forgotPassword('<EMAIL>', 'ip', 'agent');
      expect(result).toMatch(/We have sent you an email/);
    });

    it('sends reset email if user found', async () => {
      (User.findOne as jest.Mock).mockResolvedValue({ ...mockUser });
      (JWTService.generate as jest.Mock).mockReturnValue({ token: 'reset-token' });
      (mockSendToQueue as jest.Mock).mockResolvedValue(true);

      await service.forgotPassword('<EMAIL>', 'ip', 'agent');
      expect(mockSendToQueue).toHaveBeenCalled();
    });

    it('logs and throws on error', async () => {
      (User.findOne as jest.Mock).mockRejectedValue(new Error('fail'));
      await expect(service.forgotPassword('<EMAIL>', 'ip', 'agent')).rejects.toThrow('fail');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  // resetPassword
  describe('resetPassword', () => {
    it('throws if passwords do not match', async () => {
      await expect(service.resetPassword('token', 'a', 'b', 'type', 'ip', 'agent')).rejects.toThrow('Passwords do not match');
    });

    it('throws if token is missing', async () => {
      await expect(service.resetPassword('', 'a', 'a', 'type', 'ip', 'agent')).rejects.toThrow('token is required');
    });

    it('throws if user not found', async () => {
      (JWTService.verifyToken as jest.Mock).mockResolvedValue({ userId: 'user-id' });
      (User.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.resetPassword('token', 'a', 'a', 'type', 'ip', 'agent')).rejects.toThrow('User not found');
    });

    it('throws if invitation not found for newUser', async () => {
      (JWTService.verifyToken as jest.Mock).mockResolvedValue({ userId: 'user-id' });
      (User.findByPk as jest.Mock).mockResolvedValue({ ...mockUser });
      (Invitation.findOne as jest.Mock).mockResolvedValue(null);
      await expect(service.resetPassword('token', 'a', 'a', 'newUser', 'ip', 'agent')).rejects.toThrow('Invalid or expired invitation token');
    });

    it('resets password for newUser', async () => {
      (JWTService.verifyToken as jest.Mock).mockResolvedValue({ userId: 'user-id' });
      (User.findByPk as jest.Mock).mockResolvedValue({ ...mockUser, save: jest.fn() });
      (Invitation.findOne as jest.Mock).mockResolvedValue({ ...mockInvitation, save: jest.fn(), update: jest.fn().mockResolvedValue(true) });
      (PasswordHelper.hashPassword as jest.Mock).mockResolvedValue('hashed');
      (mockSendToQueue as jest.Mock).mockResolvedValue(true);

      await service.resetPassword('token', 'a', 'a', 'newUser', 'ip', 'agent');
      expect(mockSendToQueue).toHaveBeenCalled();
    });

    it('resets password for existingUser', async () => {
      (JWTService.verifyToken as jest.Mock).mockResolvedValue({ userId: 'user-id' });
      (User.findByPk as jest.Mock).mockResolvedValue({ ...mockUser, save: jest.fn() });
      (PasswordHelper.hashPassword as jest.Mock).mockResolvedValue('hashed');
      (mockSendToQueue as jest.Mock).mockResolvedValue(true);

      await service.resetPassword('token', 'a', 'a', 'existingUser', 'ip', 'agent');
      expect(mockSendToQueue).toHaveBeenCalled();
    });

    it('logs and throws on error', async () => {
      (JWTService.verifyToken as jest.Mock).mockRejectedValue(new Error('fail'));
      await expect(service.resetPassword('token', 'a', 'a', 'type', 'ip', 'agent')).rejects.toThrow('fail');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  // disableEnable2FA
  describe('disableEnable2FA', () => {
    it('throws if user not found', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.disableEnable2FA('user-id', true, 'ip', 'agent')).rejects.toThrow('User not found');
      expect(logger.error).toHaveBeenCalled();
    });

    it('enables/disables 2FA', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue({ ...mockUser, save: jest.fn() });
      await service.disableEnable2FA('user-id', true, 'ip', 'agent');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('logs and throws on error', async () => {
      (User.findByPk as jest.Mock).mockRejectedValue(new Error('fail'));
      await expect(service.disableEnable2FA('user-id', true, 'ip', 'agent')).rejects.toThrow('fail');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  // logout
  describe('logout', () => {
    it('logs out user', async () => {
      (JwtBlacklist.create as jest.Mock).mockResolvedValue({});
      await service.logout({ userId: 'user-id', token: 'token', ipAddress: 'ip', userAgent: 'agent' });
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('logs and throws on error', async () => {
      (JwtBlacklist.create as jest.Mock).mockRejectedValue(new Error('fail'));
      await expect(service.logout({ userId: 'user-id', token: 'token', ipAddress: 'ip', userAgent: 'agent' })).rejects.toThrow('fail');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  // refreshToken
  describe('refreshToken', () => {
    it('throws if user not found', async () => {
      (JWTService.verifyRefreshToken as jest.Mock).mockReturnValue({ userId: 'user-id' });
      (User.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.refreshToken('token', 'ip', 'agent')).rejects.toThrow('User not found');
    });

    it('returns new token', async () => {
      (JWTService.verifyRefreshToken as jest.Mock).mockReturnValue({ userId: 'user-id' });
      (User.findByPk as jest.Mock).mockResolvedValue({ ...mockUser, role: { name: RoleEnum.SYSTEM_ADMIN, permissions: [{ module: 'users', action: 'create' }] } });
      (JWTService.generate as jest.Mock).mockReturnValue({ token: 'new-token', tokenExpiresIn: 123 });

      const result = await service.refreshToken('token', 'ip', 'agent');
      expect(result.token).toBe('new-token');
    });

    it('logs and throws on error', async () => {
      (JWTService.verifyRefreshToken as jest.Mock).mockImplementation(() => { throw new AppError('fail'); });
      await expect(service.refreshToken('token', 'ip', 'agent')).rejects.toThrow('fail');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });

  // changePassword
  describe('changePassword', () => {
    it('throws if user not found', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue(null);
      await expect(service.changePassword({ userId: 'user-id', oldPassword: 'old', newPassword: 'new', ipAddress: 'ip', userAgent: 'agent' })).rejects.toThrow('User not found');
    });

    it('throws if old password does not match', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue({ ...mockUser });
      (PasswordHelper.comparePassword as jest.Mock).mockResolvedValue(false);
      await expect(service.changePassword({ userId: 'user-id', oldPassword: 'old', newPassword: 'new', ipAddress: 'ip', userAgent: 'agent' })).rejects.toThrow('password does not match');
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('changes password', async () => {
      (User.findByPk as jest.Mock).mockResolvedValue({ ...mockUser, save: jest.fn() });
      (PasswordHelper.comparePassword as jest.Mock).mockResolvedValue(true);
      (PasswordHelper.hashPassword as jest.Mock).mockResolvedValue('hashed');
      (mockSendToQueue as jest.Mock).mockResolvedValue(true);

      await service.changePassword({ userId: 'user-id', oldPassword: 'old', newPassword: 'new', ipAddress: 'ip', userAgent: 'agent' });
      expect(mockSendToQueue).toHaveBeenCalled();
      expect(mockAuditLog).toHaveBeenCalled();
    });

    it('logs and throws on error', async () => {
      (User.findByPk as jest.Mock).mockRejectedValue(new Error('fail'));
      await expect(service.changePassword({ userId: 'user-id', oldPassword: 'old', newPassword: 'new', ipAddress: 'ip', userAgent: 'agent' })).rejects.toThrow('fail');
      expect(mockAuditLog).toHaveBeenCalled();
    });
  });
});

afterAll(async () => {
  await redis.quit();
});