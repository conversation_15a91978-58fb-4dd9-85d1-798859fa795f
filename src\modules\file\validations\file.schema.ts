import { z } from 'zod';
import { FileType } from '../../../enum/file-type.enum';

export const uploadSchema = z.object({
  type: z.nativeEnum(FileType, {
    message: `type must be one of ${Object.values(FileType).join(', ')}`,
    required_error: 'type is required',
  }),
  category: z.string({ message: 'category must be a string' }).optional(),
  clientId: z.string({ message: 'clientId must be a string' }).uuid({ message: 'clientId must be a valid UUID' }).optional(),
});
