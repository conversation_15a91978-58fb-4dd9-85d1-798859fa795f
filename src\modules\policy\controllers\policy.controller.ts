import { Request, Response } from 'express';
import PolicyService from '../services/policy.service';
import { PaginationDto } from '../../../utils/pagination';
import { RequestWithAuditMetadata } from '../../../interfaces/custom-request.interface';

export default class PolicyController {
  private policyService: PolicyService;

  constructor() {
    this.policyService = new PolicyService();
  }

  /**
   * Create a new policy
   */
  async createPolicy(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const payload = req.body;
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;

    const policy = await this.policyService.create({
      ...payload,
      initiatorId: userId,
      ipAddress,
      userAgent,
    });
    return res.status(201).json({ data: policy, message: 'Policy created successfully' });
  }

  /**
   * Update a new policy
   */
  async updatePolicy(req: RequestWithAuditMetadata, res: Response) {
    const { id } = req.params;
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;

    const updated = await this.policyService.updatePolicy(id, {
      ...req.body,
      initiatorId: userId,
      ipAddress,
      userAgent,
    });

    res.status(200).json({ message: 'Policy updated successfully', data: updated });
  };


  /**
   * Delete a policy.
   */
  async deletePolicy(req: RequestWithAuditMetadata, res: Response) {
    const { id } = req.params;

    await this.policyService.deletePolicy(id, {
      initiatorId: req.user.userId,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    res.status(200).json({ message: 'Policy deleted successfully', data: '' });
  }


  /**
   * List all policies with pagination and optional search
   */
  async listPolicies(req: Request, res: Response): Promise<Response> {
    const paginationDto = new PaginationDto(req.query);
    const searchQuery = req.query.searchQuery as string;

    const result = await this.policyService.listPolicies(paginationDto, searchQuery);
    return res.status(200).json({ data: result, message: 'Policies retrieved successfully' });
  }

  /**
   * List loading types with optional search
   */
  async listLoadingTypes(req: Request, res: Response): Promise<Response> {
    const paginationDto = new PaginationDto(req.query);
    const searchQuery = req.query.searchQuery as string;

    const result = await this.policyService.listLoadingTypes(paginationDto, { searchQuery });
    return res.status(200).json({ data: result, message: 'Loading types retrieved successfully' });
  }

  /**
   * List discount types with optional search
   */
  async listDiscountTypes(req: Request, res: Response): Promise<Response> {
    const paginationDto = new PaginationDto(req.query);
    const searchQuery = req.query.searchQuery as string;

    const result = await this.policyService.listDiscountTypes(paginationDto, { searchQuery });
    return res.status(200).json({ data: result, message: 'Discount types retrieved successfully' });
  }

  /**
   * Search for policy categories (live suggestions)
   */
  async listCategories(req: Request, res: Response): Promise<Response> {
    const paginationDto = new PaginationDto(req.query);
    const { searchQuery, policy_id } = req.query;

    const results = await this.policyService.listCategories(paginationDto, {
      searchQuery: searchQuery as string,
      policy_id: policy_id as string,
    });
    return res.status(200).json({ data: results, message: 'Categories retrieved successfully' });
  }


  /**
   * Search for policy assets (live suggestions)
   */
  async listAssets(req: Request, res: Response): Promise<Response> {
    const paginationDto = new PaginationDto(req.query);

    const results = await this.policyService.listAssets(paginationDto);
    return res.status(200).json({ data: results, message: 'Assets retrieved successfully' });
  }


  /**
 * Get a policy by ID
 */
  async getPolicyById(req: Request, res: Response): Promise<Response> {
    const { id } = req.params;

    const policy = await this.policyService.findById(id);

    return res.status(200).json({ data: policy, message: 'Policy retrieved successfully' });
  }

  /**
   * Get a policy by name
   */
  async getPolicyByName(req: Request, res: Response): Promise<Response> {
    const { name } = req.query;

    if (!name || typeof name !== 'string') {
      return res.status(400).json({ message: 'Policy name is required as a query parameter' });
    }

    const policy = await this.policyService.findByName(name);

    return res.status(200).json({ data: policy, message: 'Policy retrieved successfully' });
  }
}
