import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  ForeignKey,
} from 'sequelize';
import { Insurer } from './insurer.model';
import { InsurerStatus } from '../../../enum/insurer-status.enum';


export class ContactList extends Model<
  InferAttributes<ContactList>,
  InferCreationAttributes<ContactList>
> {
  declare id: CreationOptional<string>;
  declare full_name: string;
  declare phone_number: string;
  declare email: string;
  declare primary_contact: boolean;
  declare status: CreationOptional<string>;
  declare insurerId: ForeignKey<string>;
  declare updatedAt?: Date;
  declare createdAt?: Date;
}

export function initContactListModel(sequelize: Sequelize): typeof ContactList {
  ContactList.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      full_name: {
        type: DataTypes.STRING,
        allowNull: false
      },
      phone_number: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      primary_contact: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      insurerId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: Insurer,
          key: 'id',
        }
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: InsurerStatus.ACTIVE
      },
    },
    {
      sequelize,
      modelName: 'ContactList',
      tableName: 'contact_list',
      timestamps: true,
      paranoid: true,
      defaultScope: {
        order: [['createdAt', 'DESC']],
      },
      indexes: [
        {
          fields: ['status'],
        },
        {
          fields: ['full_name']
        }
      ]
    },
  );

  return ContactList;
}
