import { Router } from 'express';
import AuthController from '../controllers/auth.controller';
// import { CheckSystemAdmin } from '../../../middlewares/system-admin.middleware';
import { isAuthenticated } from '../../../middlewares/auth.middleware';
import { validateRequest, validateUUIDParam } from '../../../middlewares/zod-validation.middleware';
import {
  changePasswordSchema,
  forgotPasswordSchema,
  LoginSchema,
  multifactorSchema,
  oauthSchema,
  RegisterSchema,
  resetPasswordSchema,
  updateUserProfileSchema,
  verifyOtpSchema,
} from '../validations/auth.schema';

const router = Router();
const authController = new AuthController();

router.post('/login', validateRequest(LoginSchema), authController.login.bind(authController));
router.post('/logout', isAuthenticated, authController.logout.bind(authController));
router.post(
  '/system-admin/register',
  validateRequest(RegisterSchema),
  authController.registerSystemAdmin.bind(authController),
);
router.get('/me', isAuthenticated, authController.getUserProfile.bind(authController));
router.post(
  '/forgot-password',
  validateRequest(forgotPasswordSchema),
  authController.forgotPassword.bind(authController),
);
router.post(
  '/reset-password',
  validateRequest(resetPasswordSchema),
  authController.resetPassword.bind(authController),
);
router.post(
  '/multi-factor',
  isAuthenticated,
  validateRequest(multifactorSchema),
  authController.disableEnable2FA.bind(authController),
);
router.post(
  '/verify-otp/:userId',
  validateUUIDParam('userId'),
  validateRequest(verifyOtpSchema),
  authController.verify2FA.bind(authController),
);
router.post('/refresh-token', authController.refreshToken.bind(authController));
router.post(
  '/oauth/verify',
  validateRequest(oauthSchema),
  authController.oauthLogin.bind(authController),
);

router.post(
  '/change-password',
  isAuthenticated,
  validateRequest(changePasswordSchema),
  authController.changePassword.bind(authController)
);

router.post(
  '/resend-otp/:userId',
  validateUUIDParam('userId'),
  authController.resendOtp.bind(authController)
);

router.patch(
  '/me',
  isAuthenticated,
  validateRequest(updateUserProfileSchema),
  authController.updateUserProfile.bind(authController)
);

export default router;
