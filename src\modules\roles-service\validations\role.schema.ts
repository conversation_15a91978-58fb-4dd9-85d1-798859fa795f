import { z } from 'zod';

export const createRoleSchema = z.object({
  name: z.string({ required_error: 'name is required' }).min(1, { message: 'name is required and cannot be empty' }),
  description: z.string({ required_error: 'description is required' }).min(1, { message: 'description is required and cannot be empty' }),
  permissions: z
    .array(
      z.object({
        module: z.string({ required_error: 'module is required' }).min(1, { message: 'module is required' }),
        action: z.enum(['create', 'read', 'update', 'delete', 'approve'], {
          message: 'action must be one of: create, read, update, delete, approve',
          required_error: 'action is required',
        }),
        description: z.string({ required_error: 'description is required' }).min(1, { message: 'description cannot be empty' }).optional(),
      }),
    )
    .min(1, { message: 'At least one permission is required' }),
});

export const updateRolePermissionsSchema = z.object({
  roleName: z.string({ message: 'roleName must be a valid string' }).optional(),
  roleDescription: z.string({ message: 'roleDescription is must be a valid string' }).optional(),
  permissions: z
    .array(
      z.object({
        module: z.string({ required_error: 'module is required' }).min(1, { message: 'module is required' }),
        action: z.enum(['create', 'read', 'update', 'delete', 'approve'], {
          message: 'action must be one of: create, read, update, delete, approve',
          required_error: 'action is required',
        }),
        description: z.string({ required_error: 'description is required' }).min(1, { message: 'description cannot be empty' }).optional(),
      }),
      {
        required_error: 'permissions is required',
        invalid_type_error: 'permissions must be an array of permission objects',
      }
    )
    .min(1, { message: 'At least one permission is required' }),
});
