import { Request, Response } from 'express';
import ClientService from '../services/clients.service';
import { PaginationDto } from '../../../utils/pagination';
import { ClientFilters } from '../../../interfaces/client.interface';
import { RequestWithAuditMetadata } from '../../../interfaces/custom-request.interface';

export default class ClientController {
  private clientService: ClientService;

  constructor() {
    this.clientService = new ClientService();
  }

  /**
   * Create client
   * @param req - The request object
   * @param res - The response object
   * @returns - Success and the newly created client
   */
  async createClient(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const data = req.body;
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const client = await this.clientService.createClient(
      ipAddress,
      userAgent,
      userId,
      data
    );
    return res.status(201).json({ data: client, message: 'Client created successfully' });
  }

  /**
   * Get a client by ID
   * @param req - Request object
   * @param res - Response object
   * @returns - Client
   */
  async getClientById(req: Request, res: Response): Promise<any> {
    const { id } = req.params;
    const client = await this.clientService.getSingleClient(id);
    return res.status(200).json({ data: client, message: 'Client retrieved successfully' });
  }

  /**
   * Update Client
   * @param req - Request object
   * @param res - Response object
   * @returns - Updated client
   */
  async updateClient(req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const { id } = req.params;
    const data = req.body;
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const response = await this.clientService.updateClient(id, ipAddress, userId, userAgent, data);
    return res.status(200).json({ data: response, message: 'Client updated successfully' });
  }

  /**
   * List All Clients
   * @param req - Request object
   * @param res - Response object
   * @returns - Account manager
   */
  async listAllClients(req: RequestWithAuditMetadata, res: Response) {
    const paginationDto = new PaginationDto(req.query);
    const filters: ClientFilters = {};

    if (req.query.searchQuery) {
      filters.searchQuery = req.query.searchQuery as string;
    }
    const clients = await this.clientService.listAllClients(paginationDto, filters);
    return res.status(200).json({
      message: 'Clients returned successfully',
      data: clients,
    });
  }


  /**
   * List Account managers
   * @param req - Request object
   * @param res - Response object
   * @returns - Account manager
   */
  async listAccountManagers(req: Request, res: Response): Promise<any> {
    const paginationDto = new PaginationDto(req.query);
    const data = await this.clientService.listAccountManagers(paginationDto);
    return res.status(200).json({
      data,
      message: 'Account managers retrieved successfully',
    });
  }

  /**
   * Client dashboard data
   * @param _req - Request object
   * @param res - Response object
   * @returns - CLient dashboard
   */
  async clientDashBoardData(_req: Request, res: Response): Promise<any> {
    const data = await this.clientService.clientDashboardData();
    return res.status(200).json({
      data,
      message: 'Dashboard data retrieved successfully',
    });
  }


  /**
   * Notify clients
   * @param req - Request object
   * @param res - Response object
   * @returns - Account manager
   */
  async notifyClient(req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const payload = {
      userId: req.user.id,
      ipAddress: req.auditMetadata.ipAddress,
      userAgent: req.auditMetadata.userAgent,
      notifications: req.body.notifications,
      channel: req.body.channel,
      clientIds: req.body.clientIds,
    };
    const result = await this.clientService.notifyClients(payload);
    return res.status(207).json({
      data: {
        ...result
      },
      message: 'Notification(s) processed',
    });
  }


  /**
   * List Notification types
   * @param req - Request object
   * @param res - Response object
   * @returns - Account manager
   */
  async listNotificationTypes(_req: Request, res: Response): Promise<any> {
    const data = await this.clientService.listNotificationTypes();
    return res.status(200).json({
      data,
      message: 'Notifications retrieved successfully',
    });
  }


  /**
   * Bulk Create Client
   * @param req - The request object
   * @param res - The response object
   * @returns - Success and the newly created client
   */
  async createBulkClients(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const data = req.body;
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;

    const result = await this.clientService.createBulkClients(
      ipAddress,
      userAgent,
      userId,
      data
    );

    return res.status(207).json({  // 207: Multi-Status
      message: 'Bulk client creation processed',
      data:
      {
        result: result.success,
        failed: result.failed
      },
    });
  }

  /**
   * Bulk Create Client
   * @param req - The request object
   * @param res - The response object
   * @returns - Success and the newly created client
  */
  async assignAccountManager(req: RequestWithAuditMetadata, res: Response) {
    const { accountManagerId, clientIds } = req.body;
    const metadata = {
      userId: req.user.id,
      ipAddress: req.auditMetadata.ipAddress,
      userAgent: req.auditMetadata.userAgent
    };

    const result = await this.clientService.assignAccountManagerToClients(
      accountManagerId,
      clientIds,
      metadata
    );

    return res.status(207).json({
      message: 'Account manager assignment completed',
      ...result
    });
  }
  /**
   * 
   * @param req  - Request object with audit metadata
   * @param res - Response object
   * @param id - Client ID to mark as lost
   * @returns - Response indicating success or failure
   */
  async markClientAsLost(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { id } = req.params;
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const { reason } = req.body;
    const response = await this.clientService.markClientAsLost(
      id,
      reason,
      ipAddress,
      userAgent,
      userId
    );
    return res.status(200).json({ data: response, message: 'Successfully marked client as lost' });
  }
}