import { Currency, PasswordComplexity } from '../../../enum/settings.enum';
import { z } from 'zod';

export const updateCompanyPreferencesSchema = z.object({
  system_preferences: z.object({
    company_name: z.string({
      invalid_type_error: 'Company name must be a string',
    }).optional(),

    language: z.string({
      invalid_type_error: 'Language must be a string',
    }).optional(),

    timezone: z.string({
      invalid_type_error: 'Timezone must be a string',
    }).optional(),

    currency: z.enum(Object.values(Currency) as [string, ...string[]], {
      invalid_type_error: `Currency must be one of: ${Object.values(Currency).join(', ')}`,
    }).optional(),

    company_logo: z.string({
      invalid_type_error: 'Company logo must be a string or null',
    }).optional().nullable(),
  }).optional(),

  security_policy: z.object({
    password_complexity: z.enum(Object.values(PasswordComplexity) as [string, ...string[]], {
      invalid_type_error: `Password complexity must be one of: ${Object.values(PasswordComplexity).join(', ')}`,
    }).optional(),

    session_timeout: z.number({
      invalid_type_error: 'Session timeout must be a number',
    }).optional(),

    failed_login_attempts: z.number({
      invalid_type_error: 'Failed login attempts must be a number',
    }).optional(),

    ip_whitelisting: z.boolean({
      invalid_type_error: 'IP whitelisting must be a boolean value',
    }).optional(),
  }).optional(),

  general_parameters: z.object({
    client_numbering_format: z.string({
      invalid_type_error: 'Client numbering format must be a string',
    }).optional(),

    quotation_numbering_format: z.string({
      invalid_type_error: 'Quotation numbering format must be a string',
    }).optional(),

    policy_numbering_format: z.string({
      invalid_type_error: 'Policy numbering format must be a string',
    }).optional(),

    claim_numbering_format: z.string({
      invalid_type_error: 'Claim numbering format must be a string',
    }).optional(),

    default_tax_rate: z.number({
      invalid_type_error: 'Default tax rate must be a number',
    }).optional(),

    interest_rate: z.number({
      invalid_type_error: 'Interest rate must be a number',
    }).optional(),
  }).optional(),
});