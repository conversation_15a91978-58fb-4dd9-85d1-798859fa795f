import { z } from 'zod';

export const RegisterSchema = z.object({
  email: z
    .string({ required_error: 'email is required' })
    .nonempty({ message: 'email is required' })
    .email({ message: 'invalid email format' }),

  password: z
    .string({ required_error: 'password is required' })
    .nonempty({ message: 'password is required' })
    .min(8, { message: 'password must be at least 8 characters' })
    .optional(),

  first_name: z
    .string({ required_error: 'first_name is required' })
    .nonempty({ message: 'first_name is required' }),

  last_name: z
    .string({ required_error: 'last_name is required' })
    .nonempty({ message: 'last_name is required' }),

  phone_number: z.string().optional(),

  roleId: z
    .string({ required_error: 'roleId is required' })
    .nonempty({ message: 'roleId is required' }),

  department: z
    .string({ required_error: 'department is required' })
    .nonempty({ message: 'department is required' }),
});

export const InviteUserSchema = z.object({
  email: z
    .string({ required_error: 'email is required' })
    .nonempty({ message: 'email is required' })
    .email({ message: 'invalid email format' })
    .toLowerCase(),

  first_name: z
    .string({ required_error: 'first_name is required' })
    .nonempty({ message: 'first_name is required' }),

  last_name: z
    .string({ required_error: 'last_name is required' })
    .nonempty({ message: 'last_name is required' }),

  phone_number: z.string().optional(),

  roleId: z
    .string({ required_error: 'roleId is required' })
    .nonempty({ message: 'roleId is required' }),
});

export const LoginSchema = z.object({
  email: z
    .string({ required_error: 'email is required' })
    .nonempty({ message: 'email is required' })
    .email({ message: 'invalid email format' }),

  password: z
    .string({ required_error: 'password is required' })
    .nonempty({ message: 'password is required' })
    .min(8, { message: 'password must be at least 8 characters' }),

  rememberMe: z.boolean({ required_error: 'rememberMe is required' }).optional().default(false),
});

export const forgotPasswordSchema = z.object({
  email: z
    .string({ required_error: 'email is required' })
    .nonempty({ message: 'email is required' })
    .email({ message: 'invalid email format' }),
});

export const resetPasswordSchema = z.object({
  newPassword: z
    .string({ required_error: 'newPassword is required' })
    .nonempty({ message: 'password is required' })
    .min(8, { message: 'password must be at least 8 characters' }),
  confirmPassword: z
    .string({ required_error: 'confirmPassword is required' })
    .nonempty({ message: 'confirmPassword is required' })
    .min(8, { message: 'password must be at least 8 characters' }),

  type: z.enum(['newUser', 'existingUser'], {
    message: 'type must be either newUser or existingUser',
  }),
});

export const multifactorSchema = z.object({
  enable: z.boolean({
    required_error: 'enable is required',
    invalid_type_error: 'enable must be a boolean',
  }),
});

export const verifyOtpSchema = z.object({
  otp: z
    .string({ required_error: 'otp is required' })
    .nonempty({ message: 'otp is required' })
    .length(6, { message: 'otp must be 6 digits' }),
  rememberMe: z.boolean({ required_error: 'rememberMe is required' }).optional().default(false),
});

export const changePasswordSchema = z.object({
  newPassword: z
    .string({ required_error: 'newPassword is required' })
    .nonempty({ message: 'newPassword is required' }),
  oldPassword: z
    .string({ required_error: 'oldPassword is required' })
    .nonempty({ message: 'oldPassword is required' }),
});

export const oauthSchema = z.object({
  secret: z
    .string({ required_error: 'secret is required' })
    .nonempty({ message: 'secret is required' }),
  email: z
    .string({ required_error: 'email is required' })
    .nonempty({ message: 'email is required' }),
  successful: z.boolean({
    required_error: 'successful is required',
    invalid_type_error: 'successful must be a boolean',
  }),
});

export const updateUserProfileSchema = z.object({
  profile_picture: z
    .string({
      required_error: 'profile_picture is required',
      invalid_type_error: 'profile_picture must be a string',
    })
    .nonempty({ message: 'profile_picture is required' }),
});
