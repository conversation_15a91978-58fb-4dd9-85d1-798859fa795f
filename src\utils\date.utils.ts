import { DateTime } from 'luxon';


/**
 * DateHelper class provides utility methods for date manipulation and validation.
 * @class DateHelper
 * @description This class includes methods to check if a date is expired and to validate date formats.
 */
export class DateHelper {
  /**
   * Checks if a given date is expired.
   * @param {Date} date - The date to check.
   * @returns {boolean} - Returns true if the date is expired, false otherwise.
   */
  static isExpired(date: Date): boolean {
    return new Date() > date;
  }


  /**
   * Checks if a given date is valid based on a specific format.
   * @param {Date} date - The date to validate.
   * @returns {boolean} - Returns true if the date is valid, false otherwise.
   */
  static isValidDate(date: string): boolean {
    // set date format to accept
    const dateFormat = 'yyyy-MM-dd';
    // check if date is valid
    return DateTime.fromFormat(date, dateFormat).isValid;
  }
}
