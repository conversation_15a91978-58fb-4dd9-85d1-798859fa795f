// src/config.ts
import dotenv from 'dotenv';
import { z } from 'zod';
import path from 'path';

// Load .env file
dotenv.config({ path: path.join(__dirname, '../../.env') });

// Define schema for env variables
const envSchema = z.object({
  PORT: z.string().min(1, 'PORT is required'),
  DB_HOST: z.string().min(1, 'DB_HOST is required'),
  DB_PORT: z.string().min(1, 'DB_PORT is required'),
  DB_USER: z.string().min(1, 'DB_USER is required'),
  DB_PASS: z.string().min(1, 'DB_PASS is required'),
  DB_NAME: z.string().min(1, 'DB_NAME is required'),
  DATABASE_URL: z.string().min(1, 'DATABASE_URL must be a valid URL'),
  JWT_SECRET: z.string().min(1, 'JWT_SECRET is required'),
  API_VERSION: z.string().min(1, 'API_VERSION is required'),
  DEFAULT_EMAIL_SENDER: z.string().min(1, 'DEFAULT_EMAIL_SENDER is required'),
  FRONTEND_URL: z
    .string()
    .transform((val) => val.split(',').map((v) => v.trim()))
    .refine((arr) => Array.isArray(arr) && arr.every((domain) => typeof domain === 'string'), {
      message: 'FRONTEND_URL must be a comma-separated string of URLs',
    }),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  REDIS_URL: z.string().url('REDIS_URL must be a valid URL').optional(),
  VALID_DOMAINS: z
    .string()
    .transform((val) => val.split(',').map((v) => v.trim()))
    .refine((arr) => Array.isArray(arr) && arr.every((domain) => typeof domain === 'string'), {
      message: 'VALID_DOMAINS must be a comma-separated string of domains',
    }),
  OAUTH_SECRET: z.string().min(1, 'OAUTH_SECRET is required'),
  MAIL_PROVIDER: z.string().min(1, 'MAIL_PROVIDER is required'),
  RESEND_API_KEY: z.string().min(1, 'RESEND_API_KEY is required'),
  AWS_ACCESS_KEY_ID: z.string().min(1, 'AWS_ACCESS_KEY is required'),
  AWS_SECRET_KEY: z.string().min(1, 'AWS_SECRET_KEY  is required'),
  AWS_REGION: z.string().min(1, 'AWS_REGION is required'),
  AWS_S3_BUCKET_NAME: z.string().min(1, 'AWS_S3_BUCKET_NAME'),
  RABBITMQ_URL: z.string().min(1, 'RABBITMQ_URL is required'),
  CDN_ENDPOINT: z.string().url('CDN_ENDPOINT must be a valid URL').min(1, 'CDN_ENDPOINT is required'),
  DISTRIBUTION_ID: z.string().min(1, 'DISTRIBUTION is required').optional(),
  CLOUDFRONT_PRIVATE_KEY: z.string().min(1, 'CLOUDFRONT_PRIVATE_KEY is required'),
  CLOUDFRONT_KEY_PAIR_ID: z.string().min(1, 'CLOUDFRONT_KEY_PAIR_ID is required'),

});

// Validate environment
const parsed = envSchema.safeParse(process.env);

if (!parsed.success) {
  console.error('❌ Invalid environment variables:', parsed.error.format());
  throw new Error('Environment validation failed. Please check your .env file.');
}

// Export config with uppercase keys
export const config = {
  PORT: parsed.data.PORT,
  DB_HOST: parsed.data.DB_HOST,
  DB_PORT: parsed.data.DB_PORT,
  DB_USER: parsed.data.DB_USER,
  DB_PASS: parsed.data.DB_PASS,
  DB_NAME: parsed.data.DB_NAME,
  DATABASE_URL: parsed.data.DATABASE_URL,
  JWT_SECRET: parsed.data.JWT_SECRET,
  API_VERSION: parsed.data.API_VERSION,
  DEFAULT_EMAIL_SENDER: parsed.data.DEFAULT_EMAIL_SENDER,
  FRONTEND_URL: parsed.data.FRONTEND_URL,
  NODE_ENV: parsed.data.NODE_ENV,
  REDIS_URL: parsed.data.REDIS_URL,
  VALID_DOMAINS: parsed.data.VALID_DOMAINS,
  OAUTH_SECRET: parsed.data.OAUTH_SECRET,
  MAIL_PROVIDER: parsed.data.MAIL_PROVIDER,
  RESEND_API_KEY: parsed.data.RESEND_API_KEY,
  AWS_S3_BUCKET_NAME: parsed.data.AWS_S3_BUCKET_NAME,
  AWS_REGION: parsed.data.AWS_REGION,
  AWS_SECRET_KEY: parsed.data.AWS_SECRET_KEY,
  AWS_ACCESS_KEY_ID: parsed.data.AWS_ACCESS_KEY_ID,
  RABBITMQ_URL: parsed.data.RABBITMQ_URL,
  CDN_ENDPOINT: parsed.data.AWS_S3_BUCKET_NAME,
  DISTRIBUTION_ID: parsed.data.DISTRIBUTION_ID,
  CLOUDFRONT_PRIVATE_KEY: parsed.data.CLOUDFRONT_PRIVATE_KEY,
  CLOUDFRONT_KEY_PAIR_ID: parsed.data.CLOUDFRONT_KEY_PAIR_ID,
};
