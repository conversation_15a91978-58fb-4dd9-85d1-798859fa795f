import { Response } from 'express';
import TeamService from '../services/team.service';
import { RequestWithAuditMetadata } from '../../../interfaces/custom-request.interface';
import { PaginationDto } from '../../../utils/pagination';

/**
 * TeamController class
 * This class is responsible for handling requests related to team management.
 * It includes methods for updating user details, creating teams, and managing team members.
 * It interacts with the TeamService to perform the necessary operations.
 */
export default class TeamController {
  private teamService: TeamService;

  /**
   * Constructor for TeamController
   * Initializes the service.
   */
  constructor() {
    this.teamService = new TeamService();
  }

  /**
   * Handles updating user details.
   * @param req - The request object containing user details.
   * @param res - The response object to send back the result.
   * @returns A response indicating the result of the update operation.
   */

  async updateUserDetails(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const userDetails = req.body;
    const { userId } = req.params;
    const { ipAddress, userAgent } = req.auditMetadata;
    const initiatorId = req.user.userId;

    const data = {
      userId,
      initiatorId,
      ipAddress,
      userAgent,
    };

    await this.teamService.updateUserDetails(data, userDetails);
    return res.status(200).json({ message: 'User details updated successfully', data: '' });
  }


  /**
   * Handles fetching all Team members
   * @param req - The request object containing pagination details.
   * @param res - The response object to send back the result.
   * @returns A response containing the list of team members.
   */

  async getAllTeamMembers(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const paginationDto = new PaginationDto(req.query);
    const { searchQuery } = req.query;
    const { teamId } = req.user;
    const result = await this.teamService.listTeamMembers(teamId, paginationDto, { searchQuery: searchQuery as string });
    return res.status(200).json({
      message: 'Team members fetched successfully',
      data: result,
    });
  }
}