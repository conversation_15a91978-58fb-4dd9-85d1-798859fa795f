import { z } from 'zod';

export const AssignPermissionSchema = z.object({
  roleId: z
    .string({ required_error: 'roleId is required' })
    .nonempty({ message: 'roleId is required' }),

  permissionId: z
    .string({ required_error: 'permissionId is required' })
    .nonempty({ message: 'permissionId is required' }),

  moduleName: z
    .string({ required_error: 'moduleName is required' })
    .nonempty({ message: 'moduleName is required' }),
  
  description: z
    .string({ required_error: 'description is required' })
    .nonempty({ message: 'description is required' }),

  action: z.enum(['create', 'read', 'update', 'delete', 'approve'], {
    required_error: 'action is required',
    invalid_type_error: 'action must be one of create, read, update, delete, approve',
  }),
});

export const AssignRoleToUserSchema = z.object({
  userId: z
    .string({ required_error: 'userId is required' })
    .nonempty({ message: 'userId is required' }),

  roleId: z
    .string({ required_error: 'roleId is required' })
    .nonempty({ message: 'roleId is required' }),
});

export const ActivateOrDeactivateUserSchema = z.object({
  action: z.enum(['activate', 'deactivate'], {
    required_error: 'action is required',
    invalid_type_error: 'action must be either "activate" or "deactivate"',
  }),
});
