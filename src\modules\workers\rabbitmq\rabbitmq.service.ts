import amqp, { Channel, ChannelModel } from 'amqplib';
import { RABBITMQ_CONFIG } from '../../../config/rabbitmq.config';
import logger from '../../../utils/logger';

export class RabbitMQService {
  private connection!: ChannelModel;
  private channel!: Channel;

  async connect() {
    this.connection = await amqp.connect(RABBITMQ_CONFIG.url);
    this.channel = await this.connection.createChannel();

    for (const queue of Object.values(RABBITMQ_CONFIG.queues)) {
      await this.channel.assertQueue(queue, { durable: true });
    }

    logger.info('✅ RabbitMQ connected and queues asserted');
  }

  async sendToQueue(queueName: string, payload: any) {
    if (!this.channel) {
      logger.warn('RabbitMQ channel not initialized');
      return;
    }
    this.channel.sendToQueue(queueName, Buffer.from(JSON.stringify(payload)), {
      persistent: true,
    });
  }

  async consume(queueName: string, handler: (data: any) => Promise<void>) {
    if (!this.channel) throw new Error('RabbitMQ channel not initialized');

    await this.channel.consume(queueName, async (msg) => {
      if (!msg) return;

      try {
        const data = JSON.parse(msg.content.toString());
        await handler(data);
        this.channel.ack(msg);
      } catch (err) {
        logger.error(`❌ Failed to process message from ${queueName}:`, err);
        this.channel.nack(msg, false, false); // Discard message
      }
    });

    logger.info(`📥 Consuming messages from ${queueName}`);
  }
}

export const rabbitMQService = new RabbitMQService();
