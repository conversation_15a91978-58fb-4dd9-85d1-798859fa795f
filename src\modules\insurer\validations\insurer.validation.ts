import { z } from 'zod';
import { discountSchema, loadingSchema } from '../../broking-slip/validations/broking-slip.schema';
import { InsurerStatus } from '../../../enum/insurer-status.enum';
import { BrokingSlipStatusEnum } from '../../../enum/broking-slip-status.enum';

export const contactPersonSchema = z.object({
  full_name: z
    .string({
      required_error: 'Full name is required', invalid_type_error: 'Full name must be a string'
    })
    .min(1, 'Full name is required'),
  phone_number: z
    .string({
      required_error: 'Phone number is required', invalid_type_error: 'Phone number must be a string'
    })
    .min(10, 'Phone number is required'),
  email: z.string({
    required_error: 'email is required'
  }).email('Invalid email'),
  primary_contact: z.boolean({ message: 'Primary contact must be a boolean', required_error: 'Primary contact is required' }),
  status: z
    .nativeEnum(InsurerStatus, {
      required_error: 'Status is required',
      message: `Status must be one of ${Object.values(InsurerStatus).join(', ')}`
    }).optional(),
});

export const createInsurerSchema = z.object({
  name: z.string({
    required_error: 'Name is required', invalid_type_error: 'Name must be a string'
  }).min(1, 'Name is required'),
  website_url: z.
    string({ invalid_type_error: 'Website URL must be a string' }).optional(),
  logo: z.string({ invalid_type_error: 'Logo must be a valid string' }).optional(),
  status: z
    .nativeEnum(InsurerStatus, {
      required_error: 'Status is required',
      message: `Status must be one of ${Object.values(InsurerStatus).join(', ')}`
    }).optional(),
  contact_persons: z.array(contactPersonSchema).min(1, 'At least one contact person is required')
    .refine((contacts) => {
      const seen = new Set();
      for (const c of contacts) {
        const key = `${c.email}-${c.phone_number}`;
        if (seen.has(key)) return false;
        seen.add(key);
      }
      return true;
    }, {
      message: 'Duplicate contacts with same phone and email are not allowed.'
    })
});

export const updateInsurerSchema = createInsurerSchema.partial();
export const updatedContactPersonSchema = contactPersonSchema.partial();

export const verifyAccessCodeSchema = z.object({
  accessCode: z.string({
    required_error: 'Access code is required',
    invalid_type_error: 'Access code must be a string',
  }).min(1, 'Access code cannot be empty'),

  contactId: z.string({
    required_error: 'Contact ID is required',
    invalid_type_error: 'Contact ID must be a UUID string',
  }).uuid('Contact ID must be a valid UUID'),
});

export const generateAccessCodeSchema = z.object({
  contactId: z.string({
    required_error: 'Contact ID is required',
    invalid_type_error: 'Contact ID must be a UUID string',
  }).uuid('Contact ID must be a valid UUID'),
});

/**
 * Provide Quote Schema
 */
export const provideQuotationSchema = z.object({
  brokingSlipId: z.string({
    required_error: 'Broking slip ID is required',
    invalid_type_error: 'Broking slip ID must be a UUID string',
  }).uuid('Broking slip ID must be a valid UUID'),


  discounts: z.array(discountSchema).optional(),
  loadings: z.array(loadingSchema).optional(),
  supporting_documents: z.array(z.string({
    invalid_type_error: 'Supporting document must be a string',
  })).optional(),

  base_rate: z.number({ invalid_type_error: 'Base rate must be a number' }).optional(),
  broker_commission: z.number({ invalid_type_error: 'Broker commission must be a number' }).optional(),
  commission_value: z.number({ invalid_type_error: 'Commission value must be a number' }).optional(),
  total_premium_payable: z.number({ invalid_type_error: 'Total premium payable must be a number' }).optional(),
  premium_due: z.number({
    required_error: 'Premium due is required',
    invalid_type_error: 'Premium due must be a number',
  }),
  add_on_cover_premium: z.string({ invalid_type_error: 'Add-on cover premium must be a string' }).optional(),
  sum_insured: z.number({ invalid_type_error: 'Sum insured must be a number' }).optional(),
  base_premium: z.number({ invalid_type_error: 'Base premium must be a number' }).optional(),
  taxes: z.number({ invalid_type_error: 'Taxes must be a number' }).optional(),
  net_premium: z.number({ invalid_type_error: 'Net premium must be a number' }).optional(),
  status: z.nativeEnum(BrokingSlipStatusEnum, {
    required_error: 'Status is required',
    invalid_type_error: `Status must be one of: ${Object.values(BrokingSlipStatusEnum).join(', ')}`,
  }).optional()
});

export const updateQuotationSchema = provideQuotationSchema.partial();
export type ProvideQuotationDTO = z.infer<typeof provideQuotationSchema>;
export type UpdateQuotationDTO = z.infer<typeof updateQuotationSchema>;


export type ContactPersonDTO = z.infer<typeof contactPersonSchema>;
export type CreateInsurerDTO = z.infer<typeof createInsurerSchema>;
export type UpdateInsurerDTO = z.infer<typeof updateInsurerSchema>;

