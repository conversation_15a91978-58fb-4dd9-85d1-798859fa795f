import { Response } from 'express';
import AuthService from '../services/auth.service';
import { RequestWithAuditMetadata } from '../../../interfaces/custom-request.interface';
import { JWTService } from '../../../utils/jwt';
import { config } from '../../../config/env.config';
import logger from '../../../utils/logger';
import OAuthService from '../services/oauth.service';

export default class AuthController {
  private authService: AuthService;
  private oauthService: OAuthService;

  /**
   * Constructor for AuthController
   * Initializes the services.
   */
  constructor() {
    this.authService = new AuthService();
    this.oauthService = new OAuthService();
  }

  /**
   * Handles system admin registration
   * @param req - The request object containing the email, password, and roleId.
   * @param res - The response object to send back the result.
   */
  async registerSystemAdmin(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { email, first_name, last_name, phone_number, roleId, department } = req.body;
    const { ipAddress, userAgent } = req.auditMetadata;

    const data = {
      email: email,
      password: req.body?.password,
      first_name: first_name,
      last_name: last_name,
      phone_number: phone_number,
      department: department,
      roleId: roleId,
      ipAddress: ipAddress,
      userAgent: userAgent,
    };

    const result = await this.authService.registerSystemAdmin(data);
    return res
      .status(201)
      .json({ message: 'System Admin Registration Successful', data: result });
  }

  /**
   * Handles user login
   * @param req - The request object containing the email, password, and user information.
   * @param res - The response object to send back the result.
   */
  async login(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { email, password, rememberMe } = req.body;
    const { auditMetadata } = req;

    const result = await this.authService.login({
      email,
      password,
      ipAddress: auditMetadata.ipAddress,
      userAgent: auditMetadata.userAgent,
    });

    if (result.multifactor_auth) {
      return res.status(200).json({
        message: 'Verify your account with the OTP sent to your email.',
        data: { userId: result.id },
      });
    }
    const { refreshToken } = JWTService.generateRefreshToken(result.id);

    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: config.NODE_ENV === 'production',
      path: '/',
      sameSite: 'none',
      maxAge: rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000, // 30 days or 1 day
    });

    return res.status(200).json({ message: 'Login successful', data: result });
  }

  /**
   * Get User Profile
   * @param req - The request object containing the user ID.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the user profile.
   */
  async getUserProfile(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { userId } = req.user;
    const { ipAddress, userAgent } = req.auditMetadata;
    const result = await this.authService.getUserProfile(userId, ipAddress, userAgent);
    return res.status(200).json({ message: 'User profile fetched successfully', data: result });

  }

  /**
   * Handles forgot password
   * @param req - The request object containing the email.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the result.
   */
  async forgotPassword(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { email } = req.body;
    const { ipAddress, userAgent } = req.auditMetadata;
    await this.authService.forgotPassword(email, ipAddress, userAgent);
    return res.status(200).json({
      message: 'We have sent you an email with a reset link if this email exists in our system',
      data: '',
    });
  }

  /**
   * Handles password reset
   * @param req - The request object containing the new password and token.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the result.
   */
  async resetPassword(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { newPassword, confirmPassword, type } = req.body;
    const { token } = req.query;
    const { ipAddress, userAgent } = req.auditMetadata;

    await this.authService.resetPassword(
      token as string,
      newPassword,
      confirmPassword,
      type,
      ipAddress,
      userAgent,
    );
    return res.status(200).json({
      message: 'Password changed successfully! You can now log in with your new password.',
      data: '',
    });
  }

  /**
   * Handles disabling and enabling 2FA
   * @param req - The request object containing the user ID and 2FA status.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the result.
   */

  async disableEnable2FA(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { enable } = req.body;
    const { userId } = req.user;
    const { ipAddress, userAgent } = req.auditMetadata;

    const result = await this.authService.disableEnable2FA(userId, enable, ipAddress, userAgent);
    return res.status(200).json({ message: '2FA status updated successfully', data: result });
  }

  /**
   * Handles verifying 2FA otp
   * @param req - The request object containing the user ID and OTP.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the result.
   */

  async verify2FA(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { otp, rememberMe } = req.body;
    const { userId } = req.params;
    const { ipAddress, userAgent } = req.auditMetadata;

    const { refreshToken } = JWTService.generateRefreshToken(userId);

    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: config.NODE_ENV === 'production',
      path: '/',
      sameSite: 'none',
      maxAge: rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000, // 30 days or 1 day
    });

    const result = await this.authService.verifyOTP(userId, otp, ipAddress, userAgent);
    return res.status(200).json({ message: '2FA verification successful', data: result });
  }


  /**
   * Handles Resend 2FA OTP
   * @param req - The request object containing the user ID.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the result.
   */
  async resendOtp(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { userId } = req.params;
    const { ipAddress, userAgent } = req.auditMetadata;

    await this.authService.resendOtp(userId, ipAddress, userAgent);
    return res.status(200).json({ message: 'OTP resent successfully', data: '' });
  }


  /**
   * Handles user logout
   * @param req - The request object containing the user ID and token information.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the result.
   */
  async logout(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { userId } = req.user;
    const token = req.headers.authorization?.split(' ')[1];

    const { ipAddress, userAgent } = req.auditMetadata;

    res.clearCookie('refreshToken', {
      httpOnly: true,
      secure: config.NODE_ENV === 'production',
      sameSite: 'none',
    });
    await this.authService.logout({ userId, token, ipAddress, userAgent });

    return res.status(200).json({ message: 'Logout successful' });
  }

  /**
   * Handles Refresh Token
   * @param req - The request object containing the refresh token.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the result.
   */
  async refreshToken(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const refreshToken = req.cookies?.refreshToken;
    const { ipAddress, userAgent } = req.auditMetadata;
    if (!refreshToken) {
      logger.error('Refresh token not provided');
      return res.status(401).json({ message: 'Refresh token not provided' });
    }
    const result = await this.authService.refreshToken(refreshToken, ipAddress, userAgent);
    return res.status(200).json({ message: 'Token refreshed successfully', data: result });
  }

  /**
   * Handles OAuth login
   * @param req - The request object containing the OAuth data.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the result.
   */
  async oauthLogin(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { email, successful, secret } = req.body;
    const { ipAddress, userAgent } = req.auditMetadata;

    const result = await this.oauthService.verifyOauthSuccess(
      secret,
      email,
      successful,
      ipAddress,
      userAgent,
    );

    if (result.multifactor_auth) {
      return res.status(200).json({
        message: 'Verify your account with the OTP sent to your email.',
        data: { userId: result.id },
      });
    }

    const { refreshToken } = JWTService.generateRefreshToken(result.id);

    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: config.NODE_ENV === 'production',
      path: '/',
      sameSite: 'none',
      maxAge: 24 * 60 * 60 * 1000,
    });
    return res.status(200).json({ message: 'OAuth login successful', data: result });
  }

  /**
   * Handles Change password
   * @param req - The request object containing the OAuth data.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the result.
   */

  async changePassword(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { oldPassword, newPassword } = req.body;
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;

    await this.authService.changePassword({
      userId,
      oldPassword,
      newPassword,
      ipAddress,
      userAgent
    });

    return res.status(200).json({ message: 'Password changed successfully', data: '' });
  }

  /**
   * Handles updating user profile
   * @param req - The request object containing the user ID and profile picture.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the result.
   */
  async updateUserProfile(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { userId } = req.user;
    const { profile_picture } = req.body;
    const { ipAddress, userAgent } = req.auditMetadata;

    const result = await this.authService.updateUserProfile(userId, profile_picture, ipAddress, userAgent);
    return res.status(200).json({ message: 'Profile updated successfully', data: result });
  }
}
