
import { ModuleEnum } from '../../../enum/module.enum';
import { AuditLogEnum, ErrorCode } from '../../../enum/trail-action.enum';
import { Alerts } from '../../alerts/models/alerts.model';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import logger from '../../../utils/logger';
import { PaginationDto, PaginationMetadataDto, PaginationResultDto } from '../../../utils/pagination';
import { AppError } from '../../../utils/custom-error';

/**
 * AlertService class to manage alert and alert configurations
 * @class AlertService
 */
export default class AlertService {
  constructor() { }

  /**
   * Create or Update
   * @param userId - Initiator id
   * @param ipAddress - ipAddress of initiator
   * @param userAgent - device info of initiator
   * @param alertsData - Array of alerts object
   * @returns 
   */
  async createOrUpdateAlerts(
    userId: string,
    ipAddress: string,
    userAgent: string,
    alertsData: any[]
  ): Promise<Alerts[]> {
    try {
      const results: Alerts[] = [];

      for (const data of alertsData) {
        let alert = null;

        if (data.alert_type) {
          alert = await Alerts.findOne({ where: { alert_type: data.alert_type } });
        }

        if (alert) {
          await alert.update(data);
          await AuditLogService.logEvent({
            userId,
            eventType: AuditLogEnum.UPDATE_RESOURCE,
            module: ModuleEnum.SYSTEM,
            action_description: `Updated alert: ${data.alert_type}`,
            ip_address: ipAddress,
            device_info: userAgent,
          });
        } else {
          alert = await Alerts.create(data);
          await AuditLogService.logEvent({
            userId,
            eventType: AuditLogEnum.CREATE_RESOURCE,
            module: ModuleEnum.SYSTEM,
            action_description: `Created alert: ${data.alert_type}`,
            ip_address: ipAddress,
            device_info: userAgent,
          });
        }

        results.push(alert);
      }

      return results;
    } catch (error) {
      logger.error(`Error creating/updating alerts: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Create Alert Configuration
   * @param data - configuration data
   * @param userId - Initiator id
   * @param ipAddress - ipAddress of initiator
   * @param userAgent - device info of initiator
   */
  async createAlert(userId: string, ipAddress: string, userAgent: string, data: any): Promise<Alerts> {
    try {
      const alert = await Alerts.findOne({
        where: {
          alert_type: data.alert_type
        }
      });

      if (alert) {
        await AuditLogService.logEvent({
          userId,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.SYSTEM,
          action_description: 'Alert type already exists',
          ip_address: ipAddress,
          error_code: ErrorCode.BAD_REQUEST,
          device_info: userAgent,
        });
        throw new AppError('Alert type already exists', ErrorCode.BAD_REQUEST);
      }

      const createdAlert = await Alerts.create(data);
      return createdAlert;
    } catch (error) {
      logger.error(`Error creating alert: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
 * Update Alert Configuration
 * @param alertId - ID of the alert to update
 * @param data - updated configuration data
 * @param userId - user ID of user who initiated the action
 * @param ipAddress - IP address of initiator
 * @param userAgent - device info of initiator
 */
  async updateAlert(alertId: string, data: any, userId: string, ipAddress: string, userAgent: string): Promise<Alerts> {
    try {
      const alert = await Alerts.findByPk(alertId);

      if (!alert) {
        await AuditLogService.logEvent({
          userId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.SYSTEM,
          action_description: 'Alert not found for update',
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('Alert not found', ErrorCode.NOT_FOUND);
      }

      await alert.update(data);

      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.SYSTEM,
        action_description: 'Alert updated successfully',
        ip_address: ipAddress,
        device_info: userAgent,
      });

      return alert;
    } catch (error) {
      logger.error(`Error updating alert ${alertId}: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
 * Get all Alerts
 * @returns {Promise<Alerts[]>}
 */
  async getAlerts(paginationDto: PaginationDto): Promise<PaginationResultDto<Alerts>> {
    try {
      const { order, limit, skip } = paginationDto;

      const { count, rows } = await Alerts.findAndCountAll({
        limit,
        offset: skip,
        order: [['createdAt', order]],
      });

      const meta = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count
      });

      return new PaginationResultDto(rows, meta);
    } catch (error) {
      logger.error(`Error fetching alerts: ${error}`);
      throw new AppError('Failed to fetch alerts');
    }
  }

  /**
   * Delete alert configuration
   * @param alertId - Alert id
   * @param userId - Initiator user id
   * @param ipAddress - Initiator ip address
   * @param userAgent - Initiator device information
   */
  async deleteAlert({
    alertId,
    userId,
    ipAddress,
    userAgent
  }): Promise<void> {
    const alert = await Alerts.findByPk(alertId);

    if (!alert) {
      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.DELETE_RESOURCE,
        module: ModuleEnum.SYSTEM,
        action_description: 'Alert not found for delete',
        error_code: ErrorCode.NOT_FOUND,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      throw new AppError('Alert not found', ErrorCode.NOT_FOUND);
    }

    await alert.destroy();
    return;
  }
}