import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  Op,
  ForeignKey,
} from 'sequelize';
import { User } from '../../../modules/auth-service/models/user.model';

export class Clients extends Model<
  InferAttributes<Clients>,
  InferCreationAttributes<Clients>
> {
  declare id: CreationOptional<string>;
  declare first_name: string;
  declare last_name: string;
  declare email: string;
  declare phone_number: string;
  declare client_type: string;
  declare account_manager_id: ForeignKey<User['id']>;
  declare client_id: string;
  declare policies: number;
  // declare renewal_status: string;
  declare business_name: string;
  declare status: string;
  // declare payment_schedule: string;
  // declare inactive_reason: string;
  declare lost_client_reason: string;
  declare createdAt: CreationOptional<Date>;
  declare updatedAt: CreationOptional<Date>;
  declare deletedAt: CreationOptional<Date> | null;
}

export function initClientsModel(sequelize: Sequelize): typeof Clients {
  Clients.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      first_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      last_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      phone_number: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      client_type: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      business_name: {
        type: DataTypes.STRING
      },
      account_manager_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: User,
          key: 'id'
        },
      },
      lost_client_reason: {
        type: DataTypes.STRING
      },
      policies: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      client_id: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING,
        defaultValue: 'active',
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: 'Clients',
      tableName: 'clients',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          unique: true,
          fields: ['email'],
        },
        {
          unique: false,
          fields: ['status'],
        },
      ],
      hooks: {
        beforeCreate: async (client, _options) => {
          const now = new Date();
          const year = now.getFullYear();
          const month = (now.getMonth() + 1).toString().padStart(2, '0');
          const day = now.getDate().toString().padStart(2, '0');

          // Create start and end of the month date range
          const startOfMonth = new Date(year, now.getMonth(), 1);
          const endOfMonth = new Date(year, now.getMonth() + 1, 0, 23, 59, 59, 999);

          // Count clients created within the current month
          const count = await Clients.count({
            where: {
              createdAt: {
                [Op.between]: [startOfMonth, endOfMonth],
              },
            },
          }) as number;

          const indexNumber = (count + 1).toString().padStart(2, '0');
          client.client_id = `CLT-${year}${month}${day}-${indexNumber}`;
        },
      },
    }
  );

  return Clients;
}
