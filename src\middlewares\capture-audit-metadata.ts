import { Request, Response, NextFunction } from 'express';
import { RequestWithAuditMetadata } from '../interfaces/custom-request.interface';

export function captureAuditMetadata(req: Request, _res: Response, next: NextFunction) {
  const ipAddress = req.ip || '';
  const userAgent = req.get('User-Agent') || '';

  (req as RequestWithAuditMetadata).auditMetadata = { ipAddress, userAgent };
  next();
}
