import { Request } from 'express';
import { ContactList } from '../modules/insurer/models/contact-list.model';

export interface AuditMetadata {
  ipAddress: string;
  userAgent: string;
}

export interface RequestWithAuditMetadata extends Request {
  user?: any;
  auditMetadata: AuditMetadata;
}

export interface RequestWithInsurer extends Request {
  insurer?: ContactList;
  auditMetadata: AuditMetadata;
}
