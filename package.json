{"name": "kbc-backend", "version": "1.0.0", "description": "", "directories": {"test": "tests"}, "overrides": {"string-width": "^4.2.0", "is-fullwidth-code-point": "3.0.0"}, "scripts": {"dev": "nodemon --exec ts-node -r tsconfig-paths/register src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --coverage --passWithNoTests", "test:husky": "jest --passWithNoTests", "lint": "eslint . --ext .ts,.tsx --max-warnings=0", "prepare": "husky install", "precommit": "npm run lint && npm run test:husky", "build": "tsc && cpx \"src/shared/templates/**/*\" dist/shared/templates", "start": "node -r tsconfig-paths/register dist/index.js", "lint:fix": "eslint . --ext .ts,.tsx --fix", "db:migrate": "ts-node -r tsconfig-paths/register src/scripts/runMigrations.ts", "seed:permissions": "npx ts-node seeders/seeder.ts"}, "lint-staged": {"*.ts": ["eslint --fix", "git add"]}, "sequelize-cli": {"config": "sequelize.config.ts", "migrations-path": "src/migrations"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.810.0", "@aws-sdk/cloudfront-signer": "^3.813.0", "@aws-sdk/s3-request-presigner": "^3.817.0", "amqplib": "^0.10.8", "bcrypt": "^5.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "decimal.js": "^10.6.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "gauge": "^2.7.3", "handlebars": "^4.7.8", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lodash.chunk": "^4.2.0", "luxon": "^3.6.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "otp-generator": "^4.0.1", "pg": "^8.14.1", "resend": "^4.3.0", "sequelize": "^6.37.7", "sequelize-typescript": "^2.1.6", "tsconfig-paths": "^4.2.0", "winston": "^3.17.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.23.0", "@types/amqplib": "^0.10.7", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/dotenv": "^8.2.3", "@types/express": "^5.0.1", "@types/express-fileupload": "^1.5.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/lodash.chunk": "^4.2.9", "@types/luxon": "^3.6.2", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.18", "@types/uuid": "^10.0.0", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "cpx": "^1.5.0", "eslint": "^9.23.0", "eslint-plugin-import": "^2.31.0", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "jiti": "^2.4.2", "lint-staged": "^15.5.0", "nodemon": "^3.1.9", "prettier": "^3.5.3", "supertest": "^7.1.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.2", "typescript-eslint": "^8.29.0"}}