import { Op } from 'sequelize';
import { validate as isUUID } from 'uuid';
import { Policy } from '../models/policy.model';
import { DiscountType } from '../models/discount-type.model';
import { LoadingType } from '../models/loading-type.model';
import logger from '../../../utils/logger';
import { AuditLogEnum, ErrorCode } from '../../../enum/trail-action.enum'; // Custom error enum if applicable
import { db } from '../../../database/db';
import { Category } from '../models/policy-category.model';
import { PaginationDto, PaginationMetadataDto, PaginationResultDto } from '../../../utils/pagination';
import { SearchQuery } from '../../../interfaces/query.interface';
import { User } from '../../../modules/auth-service/models/user.model';
import AuditLogService from '../../../modules/audit-trail-service/services/audit-trail.service';
import { ModuleEnum } from '../../../enum/module.enum';
import { CreatePolicyDto } from '../../../interfaces/create-policy.interface';
import cacheService from '../../../utils/cache.service';
import { CacheKeys } from '../../../enum/cache-keys.enum';
import { Assets } from '../models/assets.model';
import { AppError } from '../../../utils/custom-error';


/**
 * PolicyService class to manage Policies
 * @class PolicuService
 * @description This class provides methods to manage Policies
 */
export default class PolicyService {
  constructor() { }

  /**
 * @param payload - Object containing all details required to create a policy
 * @returns {Promise<Policy>} - The newly created policy object
 * @description Creates a new policy with categories, discount types, and loading types. Uses hybrid design by storing references and JSON snapshots.
 * @throws {Error} - Throws an error if creation fails
 */
  async create(
    payload: CreatePolicyDto &
    {
      initiatorId: string,
      ipAddress: string,
      userAgent: string
    }
  ): Promise<Policy> {
    const {
      name,
      broker_commission,
      categories: categoryNames,
      discountTypes,
      loadingTypes,
      ipAddress,
      initiatorId,
      userAgent
    } = payload;
    const transaction = await db.transaction();
    const user = await User.findByPk(initiatorId, {
      attributes: ['first_name', 'last_name'],
    });

    try {
      const existingPolicy = await Policy.findOne({
        where: { name },
        attributes: ['name'],
      });
      if (existingPolicy) {
        logger.error(`Policy with ${name} already exist`);
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.POLICIES,
          action_description: `Policy with ${name} already exists`,
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });

        throw new AppError(`Policy with ${name} already exists`, ErrorCode.CONFLICT);
      }

      const discountTypeRecords = await Promise.all(
        discountTypes.map(dt =>
          DiscountType.findOrCreate({
            where: { name: dt.name },
            defaults: dt,
            transaction
          }).then(([record]) => record)
        )
      );
      const discountTypeIds = discountTypeRecords.map(dt => dt.id);
      const loadingTypeRecords = await Promise.all(
        loadingTypes.map(lt =>
          LoadingType.findOrCreate({
            where: { name: lt.name },
            defaults: lt,
            transaction
          }).then(([record]) => record)
        )
      );
      const loadingTypeIds = loadingTypeRecords.map(lt => lt.id);
      const policy = await Policy.create(
        {
          name,
          broker_commission,
          categories: categoryNames,
          discount_type_ids: discountTypeIds,
          loading_type_ids: loadingTypeIds,
          discount_type_details: discountTypes,
          loading_type_details: loadingTypes,
        },
        { transaction }
      );

      try {
        for (const category of categoryNames) {
          await Category.findOrCreate({
            where: { name: category.name, policy_id: policy.id },
            defaults: {
              name: category.name,
              policy_id: policy.id,
              default_rate: category.default_rate
            },
            transaction,
          });
        }
      } catch (error) {
        logger.error(error);
      }

      await transaction.commit();

      const timestamp = new Date().toLocaleString();
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.POLICIES,
        action_description: `${user?.first_name} ${user?.last_name} created a policy '${name}' on ${timestamp}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      await cacheService.invalidateByPattern(`${CacheKeys.POLICY_LIST}:*`);

      return policy;
    } catch (error) {
      await transaction.rollback();
      logger.error(`Failed to create policy: ${error}`);

      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.POLICIES,
        action_description: `Error creating policy '${name}': ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }


  /**
* @param payload - Object containing all details required to update a policy
* @returns {Promise<Policy>} - The newly updated policy object
* @description Updates policy with categories, discount types, and loading types. Uses hybrid design by storing references and JSON snapshots.
* @throws {Error} - Throws an error if update fails
*/
  async updatePolicy(
    id: string,
    payload: Partial<CreatePolicyDto> & {
      initiatorId: string,
      ipAddress: string,
      userAgent: string
    }
  ): Promise<Policy> {
    const transaction = await db.transaction();
    const { initiatorId, ipAddress, userAgent } = payload;

    const user = await User.findByPk(initiatorId, {
      attributes: ['first_name', 'last_name'],
    });

    try {
      const policy = await Policy.findByPk(id);
      if (!policy) throw new AppError('Policy not found', ErrorCode.NOT_FOUND);

      if (payload.name) {
        const existing = await Policy.findOne({ where: { name: payload.name } });
        if (existing && existing.id !== policy.id) {
          throw new AppError('A policy with this name already exists', ErrorCode.CONFLICT);
        }
      }

      // Retain original values for unprovided fields
      const updatedData: Partial<Policy> = {
        name: payload.name ?? policy.name,
        broker_commission: payload.broker_commission ?? policy.broker_commission,
        categories: payload.categories ?? policy.categories,
      };
      if (payload.categories) {
        for (const category of payload.categories) {
          await Category.findOrCreate({
            where: { name: category.name, policy_id: policy.id },
            defaults: {
              name: category.name,
              policy_id: policy.id,
              default_rate: category.default_rate
            },
            transaction,
          });
        }
      }

      // if (payload.assets) {
      //   const assets = await Promise.all(
      //     payload.assets.map(asset =>
      //       Assets.findOrCreate({ where: { name: asset }, defaults: { name: asset }, transaction }).then(([asset]) => asset)
      //     )
      //   );
      //   updatedData.assets = assets.map(c => c.name);
      // }

      if (payload.discountTypes) {
        const discounts = await Promise.all(
          payload.discountTypes.map(d =>
            DiscountType.findOrCreate({ where: { name: d.name }, defaults: d, transaction }).then(([dt]) => dt)
          )
        );
        updatedData.discount_type_ids = discounts.map(d => d.id);
        updatedData.discount_type_details = payload.discountTypes;
      }

      if (payload.loadingTypes) {
        const loadings = await Promise.all(
          payload.loadingTypes.map(l =>
            LoadingType.findOrCreate({ where: { name: l.name }, defaults: l, transaction }).then(([lt]) => lt)
          )
        );
        updatedData.loading_type_ids = loadings.map(l => l.id);
        updatedData.loading_type_details = payload.loadingTypes;
      }

      await policy.update(updatedData, { transaction });
      await transaction.commit();

      const timestamp = new Date().toLocaleString();
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.POLICIES,
        action_description: `${user?.first_name} ${user?.last_name} updated policy '${policy.name}' on ${timestamp}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      await cacheService.invalidateByPattern(`${CacheKeys.POLICY_LIST}:*`);

      return policy;
    } catch (err) {
      await transaction.rollback();
      logger.error(`Failed to update policy: ${err.message}`);

      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.POLICIES,
        action_description: `Failed to update policy '${id}': ${err.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      throw new AppError(err.message || 'Failed to update policy');
    }
  }

  /**
 * List all policies with pagination and optional search query
 * @param paginationDto - Pagination configuration (page, limit, order)
 * @param searchQuery - Optional search string to filter by policy name or product
 * @returns {Promise<PaginationResultDto<Policy>>} Paginated and filtered list of policies
 * @throws {Error} If fetching policies fails
 */
  async listPolicies(
    paginationDto: PaginationDto,
    searchQuery?: string
  ): Promise<PaginationResultDto<Policy>> {
    try {
      const { order, limit, skip } = paginationDto;

      const cacheKey = cacheService.generateKey(CacheKeys.POLICY_LIST, {
        pagination: paginationDto,
        searchQuery,
      });
      const cached = await cacheService.get<PaginationResultDto<Policy>>(cacheKey);

      if (cached) {
        return new PaginationResultDto(cached.data, cached.meta);
      };

      const whereClause = searchQuery
        ? {
          [Op.or]: [
            { name: { [Op.iLike]: `%${searchQuery}%` } },
            { product: { [Op.iLike]: `%${searchQuery}%` } },
          ],
        }
        : {};

      const { count, rows } = await Policy.findAndCountAll({
        where: whereClause,
        limit,
        offset: skip,
        order: [['createdAt', order]],
      });

      const metadata = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });
      const result = new PaginationResultDto(rows, metadata);

      await cacheService.set(cacheKey, result);

      return result;
    } catch (error) {
      logger.error(`Failed to fetch policies: ${error.message}`);
      throw new AppError('Failed to fetch policies');
    }
  }


  /**
   * List all loading types with pagination and optional search
   * @param paginationDto
   * @param searchParam
   * @returns {Promise<PaginationResultDto<LoadingType>>}
   */
  async listLoadingTypes(paginationDto: PaginationDto, searchParam: SearchQuery): Promise<PaginationResultDto<LoadingType>> {
    try {
      const { order, limit, skip } = paginationDto;
      const { searchQuery } = searchParam;

      const whereClause: any = {};

      if (searchQuery) {
        whereClause[Op.or] = [
          { name: { [Op.iLike]: `%${searchQuery}%` } },
        ];
      }

      const { count, rows } = await LoadingType.findAndCountAll({
        where: whereClause,
        limit,
        offset: skip,
        distinct: true,
        order: [['createdAt', order]],
      });

      const meta = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });

      return new PaginationResultDto(rows, meta);
    } catch (error) {
      logger.error(`Error fetching loading types: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * List all discount types with pagination and optional search
   * @param paginationDto
   * @param searchParam
   * @returns {Promise<PaginationResultDto<DiscountType>>}
   */
  async listDiscountTypes(paginationDto: PaginationDto, searchParam: SearchQuery): Promise<PaginationResultDto<DiscountType>> {
    try {
      const { order, limit, skip } = paginationDto;
      const { searchQuery } = searchParam;

      const whereClause: any = {};

      if (searchQuery) {
        whereClause[Op.or] = [
          { name: { [Op.iLike]: `%${searchQuery}%` } },
        ];
      }

      const { count, rows } = await DiscountType.findAndCountAll({
        where: whereClause,
        limit,
        offset: skip,
        distinct: true,
        order: [['createdAt', order]],
      });

      const meta = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });

      return new PaginationResultDto(rows, meta);
    } catch (error) {
      logger.error(`Error fetching discount types: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }


  /**
 * Search categories for live suggestions (e.g., dropdown/autocomplete)
 * @param PaginationDto - The partial search string from client input
 * @returns {Promise<Category[]>} - List of matching categories
 * @throws {Error} - If the search operation fails
 */
  async listCategories(
    paginationDto: PaginationDto,
    searchParams: { searchQuery?: string; policy_id?: string }
  ): Promise<PaginationResultDto<Category>> {
    try {
      const { limit, skip, order } = paginationDto;
      const { searchQuery, policy_id } = searchParams;

      const whereClause: any = {};

      if (searchQuery) {
        whereClause.name = { [Op.iLike]: `%${searchQuery}%` };
      }

      if (policy_id) {
        if (!isUUID(policy_id)) {
          throw new AppError('Invalid policy_id format. Must be a valid UUID', 400);
        }
        whereClause.policy_id = { [Op.eq]: policy_id };
      }


      const { count, rows } = await Category.findAndCountAll({
        where: whereClause,
        order: [['name', order || 'ASC']],
        limit,
        offset: skip,
        attributes: ['id', 'name', 'policy_id', 'summary_of_cover', 'extensions', 'default_rate'],
      });

      const meta = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });

      return new PaginationResultDto(rows, meta);
    } catch (error) {
      logger.error(`Error fetching categories: ${error}`);
      throw new AppError(error.message || 'Failed to fetch categories');
    }
  }


  /**
 * List assets for live suggestions (e.g., dropdown/autocomplete)
 * @param PaginationDto - The partial search string from client input
 * @returns {Promise<Assets[]>} - List of assets
 * @throws {Error} - If the search operation fails
 */
  async listAssets(paginationDto: PaginationDto): Promise<PaginationResultDto<Assets>> {
    try {
      const { limit, skip } = paginationDto;
      const { count, rows } = await Assets.findAndCountAll({
        order: [['name', 'ASC']],
        limit,
        offset: skip,
        attributes: ['name'],
      });

      const meta = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count
      });

      return new PaginationResultDto(rows, meta);
    } catch (error) {
      logger.error(`Error fetching assets ${error}`);
      throw new AppError(error.message || 'Failed to fetch assets');
    }
  }

  /**
 * Find a policy by its unique ID.
 * @param {string} id - The UUID of the policy.
 * @returns {Promise<Policy>} - The found policy.
 * @throws {Error} - If the policy is not found or retrieval fails.
 */
  async findById(id: string): Promise<Policy> {
    try {
      const policy = await Policy.findByPk(id);
      if (!policy) {
        throw new AppError('Policy not found', ErrorCode.NOT_FOUND);
      }
      return policy;
    } catch (error) {
      logger.error(`Error fetching policy by ID ${id}: ${error.message}`);
      throw new AppError(error.message || 'Failed to fetch policy by ID');
    }
  }

  /**
   * Find a policy by its unique name.
   * @param {string} name - The name of the policy.
   * @returns {Promise<Policy>} - The found policy.
   * @throws {Error} - If the policy is not found or retrieval fails.
   */
  async findByName(name: string): Promise<Policy> {
    try {
      const policy = await Policy.findOne({ where: { name } });
      if (!policy) {
        throw new AppError(`Policy with name "${name}" not found`, ErrorCode.NOT_FOUND);
      }
      return policy;
    } catch (error) {
      logger.error(`Error fetching policy by name "${name}": ${error.message}`);
      throw new AppError(error.message || 'Failed to fetch policy by name');
    }
  }


  /**
 * Soft delete a policy
 * @param id - Policy ID to delete
 * @param meta - Metadata for auditing
 * @returns {Promise<void>}
 * @throws {Error} - If deletion fails
 */
  async deletePolicy(
    id: string,
    meta: {
      initiatorId: string;
      ipAddress: string;
      userAgent: string;
    }
  ): Promise<void> {
    const { initiatorId, ipAddress, userAgent } = meta;
    const user = await User.findByPk(initiatorId, {
      attributes: ['first_name', 'last_name'],
    });

    try {
      const policy = await Policy.findByPk(id);
      if (!policy) {
        throw new AppError('Policy not found', ErrorCode.NOT_FOUND);
      }

      await policy.destroy();

      const timestamp = new Date().toLocaleString();
      await Promise.all([
        AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.DELETE_RESOURCE,
          module: ModuleEnum.POLICIES,
          action_description: `${user?.first_name} ${user?.last_name} deleted policy '${policy.name}' on ${timestamp}`,
          ip_address: ipAddress,
          device_info: userAgent,
        }),
        cacheService.invalidateByPattern(`${CacheKeys.POLICY_LIST}:*`)
      ]);
    } catch (error) {
      logger.error(`Failed to delete policy: ${error.message}`);
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.DELETE_RESOURCE,
        module: ModuleEnum.POLICIES,
        action_description: `Error deleting policy: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }
}