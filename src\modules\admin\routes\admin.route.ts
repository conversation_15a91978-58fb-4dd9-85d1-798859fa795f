import { Router } from 'express';
import AdminController from '../controllers/admin.controller';
import { validateRequest, validateUUIDParam } from '../../../middlewares/zod-validation.middleware';
import {
  ActivateOrDeactivateUserSchema,
  AssignRoleToUserSchema,
} from '../validations/admin.schema';
import { checkPermissions, isAuthenticated } from '../../../middlewares/auth.middleware';
import { ModuleEnum } from '../../../enum/module.enum';
import { ModuleAction } from '../../../enum/module-action';
import {
  createRoleSchema,
  updateRolePermissionsSchema,
} from '../../roles-service/validations/role.schema';
import { InviteUserSchema } from '../../auth-service/validations/auth.schema';
// import { CheckSystemAdmin } from '../../../middlewares/system-admin.middleware';
import { updateCompanyPreferencesSchema } from '../validations/company.schema';
import { alertPayloadSchema, createAlertSchema, updateAlertSchema } from '../../alerts/validations/alert.schema';

const router = Router();
const adminController = new AdminController();

router.post(
  '/invite-user',
  checkPermissions([{ module: ModuleEnum.USERS, action: ModuleAction.CREATE }]),
  validateRequest(InviteUserSchema),
  adminController.InviteUserBySystemAdmin.bind(adminController),
);
router.get(
  '/users',
  checkPermissions([{ module: ModuleEnum.SYSTEM, action: ModuleAction.READ }]),
  adminController.listAllUsers.bind(adminController),
);
router.patch(
  '/users/assign-role',
  checkPermissions([{ module: ModuleEnum.ROLES, action: ModuleAction.CREATE }]),
  validateRequest(AssignRoleToUserSchema),
  adminController.assignRoleToUser.bind(adminController),
);
router.patch(
  '/users/:userId/activate',
  checkPermissions([{ module: ModuleEnum.USERS, action: ModuleAction.UPDATE }]),
  validateUUIDParam('userId'),
  validateRequest(ActivateOrDeactivateUserSchema),
  adminController.activateOrDeactivateUser.bind(adminController),
);
router.post(
  '/roles',
  isAuthenticated,
  checkPermissions([{ module: ModuleEnum.ROLES, action: ModuleAction.CREATE }]),
  validateRequest(createRoleSchema),
  adminController.createRole.bind(adminController),
);
router.get(
  '/roles',
  isAuthenticated,
  adminController.getAllRoles.bind(adminController),
);
router.get(
  '/permissions',
  checkPermissions([{ module: ModuleEnum.ROLES, action: ModuleAction.READ }]),
  adminController.listAvailablePermissions.bind(adminController),
);
router.get(
  '/roles/:roleId/permissions',
  checkPermissions([{ module: ModuleEnum.ROLES, action: ModuleAction.READ }]),
  validateUUIDParam('roleId'),
  adminController.fetchRolePermissions.bind(adminController),
);
router.patch(
  '/roles/:roleId',
  isAuthenticated,
  checkPermissions([{ module: ModuleEnum.ROLES, action: ModuleAction.UPDATE }]),
  validateRequest(updateRolePermissionsSchema),
  validateUUIDParam('roleId'),
  adminController.updateRolePermission.bind(adminController),
);
router.get(
  '/roles/:roleId',
  isAuthenticated,
  checkPermissions([{ module: ModuleEnum.ROLES, action: ModuleAction.READ }]),
  validateUUIDParam('roleId'),
  adminController.getRoleById.bind(adminController),
);

router.get(
  '/audit-logs',
  isAuthenticated,
  checkPermissions([{ module: ModuleEnum.SYSTEM, action: ModuleAction.READ }]),
  adminController.getAuditLogs.bind(adminController),
);

router.delete(
  '/roles/:roleId',
  isAuthenticated,
  checkPermissions([{ module: ModuleEnum.ROLES, action: ModuleAction.DELETE }]),
  validateUUIDParam('roleId'),
  adminController.deleteRole.bind(adminController),
);

router.get(
  '/modules',
  isAuthenticated,
  checkPermissions([{ module: ModuleEnum.ROLES, action: ModuleAction.READ }]),
  adminController.listAllModules.bind(adminController),
);

router.get(
  '/system-preferences',
  adminController.getCompanyId.bind(adminController)
);

router.patch(
  '/system-preferences',
  isAuthenticated,
  checkPermissions([{ module: ModuleEnum.SYSTEM, action: ModuleAction.UPDATE }]),
  validateRequest(updateCompanyPreferencesSchema),
  adminController.updateSystemPreference.bind(adminController)
);

router.post(
  '/alerts/action',
  checkPermissions([
    { module: ModuleEnum.SYSTEM, action: ModuleAction.CREATE },
    { module: ModuleEnum.SYSTEM, action: ModuleAction.UPDATE }
  ]),
  validateRequest(alertPayloadSchema),
  adminController.createOrUpdateAlerts.bind(adminController)
);

router.post(
  '/alerts',
  checkPermissions([{ module: ModuleEnum.SYSTEM, action: ModuleAction.CREATE }]),
  validateRequest(createAlertSchema),
  adminController.createAlert.bind(adminController)
);

router.patch(
  '/alerts/:alertId',
  validateUUIDParam('alertId'),
  checkPermissions([{ module: ModuleEnum.SYSTEM, action: ModuleAction.UPDATE }]),
  validateRequest(updateAlertSchema),
  adminController.updateAlert.bind(adminController),
);

router.get(
  '/alerts',
  checkPermissions([{ module: ModuleEnum.SYSTEM, action: ModuleAction.READ }]),
  adminController.getAlerts.bind(adminController)
);

router.delete(
  '/alerts/:alertId',
  validateUUIDParam('alertId'),
  checkPermissions([{ module: ModuleEnum.SYSTEM, action: ModuleAction.DELETE }]),
  adminController.deleteAlert.bind(adminController)
);

export default router;
