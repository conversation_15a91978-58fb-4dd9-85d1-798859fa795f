import { RequestWithInsurer } from '../../../interfaces/custom-request.interface';
import { Response } from 'express';
import InsurerPortalService from '../services/insurer-portal.service';
import { PaginationDto } from '../../../utils/pagination';

export default class InsurerPortalController {

  private insurerPortalService: InsurerPortalService;

  constructor() {
    this.insurerPortalService = new InsurerPortalService();
  }

  /**
   * Generates and sends an access code to the insurer's contact email.
   */
  async generateAndSendAccessCode(req: RequestWithInsurer, res: Response): Promise<Response> {
    const { contactId } = req.body;

    await this.insurerPortalService.generateAndSendAccessCode(contactId);

    return res.status(200).json({
      data: '',
      message: 'Access code sent successfully',
    });
  }

  /**
   * Verifies the access code provided by the insurer's contact.
   */
  async verifyAccessCode(req: RequestWithInsurer, res: Response): Promise<Response> {
    const { ipAddress, userAgent } = req.auditMetadata;
    const { accessCode, contactId, } = req.body;

    const data = await this.insurerPortalService.verifyAccessCode(
      accessCode,
      contactId,
      ipAddress,
      userAgent
    );
    return res.status(200).json({
      data,
      message: 'Access code verified successfully',
    });
  }

  /**
   * Lists the broking slips for a specific insurer's contact.
   */
  async listInsurersBrokingSlips(req: RequestWithInsurer, res: Response): Promise<Response> {
    const { id: contactId, insurerId } = req.insurer;
    const paginationDto = new PaginationDto(req.query);

    const data = await this.insurerPortalService.listInsurersBrokingSlips(
      contactId,
      insurerId,
      paginationDto
    );

    return res.status(200).json({
      data,
      message: 'Broking slips retrieved successfully',
    });
  }

  /**
   * Get BrokingSlip By ID
   */
  async getBrokingSlipById(req: RequestWithInsurer, res: Response): Promise<Response> {
    const { brokingSlipId } = req.params;

    const data = await this.insurerPortalService.getBrokingSlipById(brokingSlipId);

    return res.status(200).json({
      data,
      message: 'Broking slips retrieved successfully',
    });
  }

  /**
   * Provide Quote
   */
  async provideQuote(req: RequestWithInsurer, res: Response): Promise<Response> {
    const { ipAddress, userAgent } = req.auditMetadata;
    const { id: contactId, insurerId } = req.insurer;
    const data = req.body;

    const response = await this.insurerPortalService.provideQuote(
      insurerId,
      ipAddress,
      userAgent,
      contactId,
      data
    );
    return res.status(201).json({ data: response, message: 'Quote provided successfully' });
  }
}