import { literal, Op } from 'sequelize';
import { User } from '../../auth-service/models/user.model';
// import { Role } from '../../auth-service/models/role.model';
// import { Permission } from '../../auth-service/models/permission.model';
import { UpdateUserInterface } from '../../../interfaces/update-user.interface';
import { PhoneNumberHelper } from '../../../utils/phonenumber.util';
import logger from '../../../utils/logger';
import AuditLogService from '../../../modules/audit-trail-service/services/audit-trail.service';
import { AuditLogEnum, ErrorCode } from '../../../enum/trail-action.enum';
import { ModuleEnum } from '../../../enum/module.enum';
import { RoleEnum } from '../../../enum/role.enum';
import { Team } from '../models/team.model';
import { PaginationDto, PaginationMetadataDto, PaginationResultDto } from '../../../utils/pagination';
import { Role } from '../../../modules/auth-service/models/role.model';
import { SearchQuery } from '../../../interfaces/query.interface';
import { AppError } from '../../../utils/custom-error';


/**
 * TeamService class
 * This class is responsible for managing team-related operations.
 * It includes methods for updating user details, roles, and permissions.
 * It also includes methods for managing team members and their roles.
 */
export default class TeamService {
  constructor() { }


  /**
   * Updates the details of a user in the team.
   * @param userId - The ID of the user to update.
   * @param userDetails - The new details of the user.
   * @returns A promise that resolves when the user details are updated.
   */
  async updateUserDetails(data: { userId: string, initiatorId: string, ipAddress: string, userAgent: string }, userDetails: UpdateUserInterface): Promise<void> {
    const { userId, initiatorId, ipAddress, userAgent } = data;
    try {
      const user = await User.findByPk(userId);

      if (!user) {
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.USERS,
          action_description: 'Error during user update: User not found',
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('User not found', ErrorCode.NOT_FOUND);
      }

      if (userDetails.email) {
        const existingUser = await User.findOne({ where: { email: userDetails.email } });
        if (existingUser && existingUser.id !== userId) {
          await AuditLogService.logEvent({
            userId: initiatorId,
            eventType: AuditLogEnum.UPDATE_RESOURCE,
            module: ModuleEnum.USERS,
            action_description: 'Error during user update: Email already exists',
            error_code: ErrorCode.BAD_REQUEST,
            ip_address: ipAddress,
            device_info: userAgent,
          });
          throw new AppError('Email already exists', ErrorCode.CONFLICT);
        }
      }

      let phoneNumber = null;

      if (userDetails.phone_number) {
        phoneNumber = PhoneNumberHelper.formatToCountryStandard(userDetails.phone_number);
        const existingUser = await User.findOne({ where: { phone_number: phoneNumber } });
        if (existingUser && existingUser.id !== userId) {
          await AuditLogService.logEvent({
            userId: initiatorId,
            eventType: AuditLogEnum.UPDATE_RESOURCE,
            module: ModuleEnum.USERS,
            action_description: 'Error during user update: Phone number already exists',
            error_code: ErrorCode.BAD_REQUEST,
            ip_address: ipAddress,
            device_info: userAgent,
          });
          throw new AppError('Phone number already exists', ErrorCode.CONFLICT);
        }
      }

      if (userDetails.roleId) {
        if (user.user_type === RoleEnum.SYSTEM_ADMIN && userDetails.roleId !== user.roleId) {
          await AuditLogService.logEvent({
            userId: initiatorId,
            eventType: AuditLogEnum.UPDATE_RESOURCE,
            module: ModuleEnum.USERS,
            action_description: 'Error during user update: System admin cannot change their role',
            error_code: ErrorCode.BAD_REQUEST,
            ip_address: ipAddress,
            device_info: userAgent,
          });
          throw new AppError('System admin cannot change their role', ErrorCode.BAD_REQUEST);
        }
      }

      await user.update({
        ...userDetails,
        phone_number: phoneNumber || user.phone_number,
      });
    } catch (error) {
      logger.error(`Error updating user details: ${error.message}`);
      await AuditLogService.logEvent({
        userId: null,
        eventType: AuditLogEnum.API_ERROR,
        module: ModuleEnum.USERS,
        action_description: `Error during user update: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      throw new AppError('Failed to update user details');
    }
  }


  /**
   * Add User To Team
   * @param userId - The ID of the user to add.
   * @param teamId - The ID of the team to add the user to.
   * @param initiatorId - The ID of the user initiating the action.
   */

  async addUserToTeam(userId: string, teamId: string, initiatorId: string): Promise<void> {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new AppError('User not found', ErrorCode.NOT_FOUND);
      }

      const team = await Team.findByPk(teamId);
      if (!team) {
        throw new AppError('Team not found', ErrorCode.NOT_FOUND);
      }
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.USERS,
        action_description: `User ${userId} added to team ${teamId}`,
        ip_address: null,
        device_info: null,
      });
      await user.update({ teamId });
    } catch (error) {
      logger.error(`Error adding user to team: ${error.message}`);
      throw new AppError('Failed to add user to team');
    }
  }

  async listTeamMembers(teamId: string, paginationDto: PaginationDto, searchQuery: SearchQuery): Promise<PaginationResultDto<User>> {
    try {
      const { order, limit, skip } = paginationDto;
      const { searchQuery: search } = searchQuery;
      const whereClause: any = { teamId };

      if (search) {
        whereClause[Op.or] = [
          { first_name: { [Op.like]: `%${search}%` } },
          { last_name: { [Op.like]: `%${search}%` } },
          { email: { [Op.like]: `%${search}%` } },
          { phone_number: { [Op.like]: `%${search}%` } },
          { user_type: { [Op.like]: `%${search}%` } },
          { department: { [Op.like]: `%${search}%` } },
          literal(`CAST("status" as TEXT) LIKE '%${search}%'`),
        ];
      }

      const { count, rows } = await User.findAndCountAll({
        where: whereClause,
        order: [['createdAt', order]],
        limit,
        offset: skip,
        attributes: { exclude: ['password'] },
        include: [
          {
            model: Role,
            as: 'role',
          },
        ],
      });
      const meta = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });

      return new PaginationResultDto(rows, meta);
    } catch (error) {
      logger.error(`Error listing team members: ${error.message}`);
      throw new AppError('Failed to list team members');
    }
  }
}