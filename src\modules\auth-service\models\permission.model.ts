// src/models/Permission.ts
import {
  Model,
  DataTypes,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  Sequelize,
} from 'sequelize';

export class Permission
  extends Model<InferAttributes<Permission>, InferCreationAttributes<Permission>>
  implements Model
{
  declare id: CreationOptional<string>;
  declare module: string;
  declare action: 'create' | 'read' | 'update' | 'delete' | 'approve';
  declare description: string;
}

export function initPermissionModel(sequelize: Sequelize): typeof Permission {
  Permission.init(
    {
      id: { type: DataTypes.UUID, defaultValue: DataTypes.UUIDV4, primaryKey: true },
      module: { type: DataTypes.STRING, allowNull: false },
      description: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'No description provided',
      },
      action: {
        type: DataTypes.ENUM('create', 'read', 'update', 'delete', 'approve'),
        allowNull: false,
      },
    },
    {
      sequelize,
      modelName: 'Permission',
      tableName: 'permissions',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          unique: true,
          fields: ['module', 'action']
        }
      ]
    },
  );

  return Permission;
}
