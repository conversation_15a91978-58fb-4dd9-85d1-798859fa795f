import { Sequelize } from 'sequelize';
import dotenv from 'dotenv';
import { config } from '../config/env.config';

dotenv.config();

export const db = new Sequelize(
  config.DB_NAME as string,
  config.DB_USER as string,
  config.DB_PASS as string,
  {
    host: config.DB_HOST,
    port: parseInt(config.DB_PORT || '5432', 10),
    dialect: 'postgres',
    logging: config.NODE_ENV === 'production' ? false : console.log,
    pool: {
      max: 9,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    dialectOptions: {
      ssl: {
        rejectUnauthorized: false,
      },
    },
  },
);
