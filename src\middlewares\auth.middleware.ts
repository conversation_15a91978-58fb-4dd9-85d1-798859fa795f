import { NextFunction, Request, Response } from 'express';
import { JWTService } from '../utils/jwt';
import jwt from 'jsonwebtoken';
import logger from '../utils/logger';
import { User } from '../modules/auth-service/models/user.model';
import { UserStatus } from '../enum/user-status.enum';
import AuditLogService from '../modules/audit-trail-service/services/audit-trail.service';
import { AuditLogEnum, ErrorCode } from '../enum/trail-action.enum';
import { ModuleEnum } from '../enum/module.enum';

// Define interface for user in request
interface RequestWithUser extends Request {
  user: any;
}

export const checkPermissions = (requiredPerms = []) => {
  return async (req: RequestWithUser, res: Response, next: NextFunction): Promise<void> => {
    const token = req.headers.authorization?.split(' ')[1];

    if (!token) {
      res.status(401).json({ message: 'Unauthorized' });
      await AuditLogService.logEvent({
        userId: req.user?.userId,
        eventType: AuditLogEnum.UNAUTHORIZED,
        module: ModuleEnum.AUTH,
        action_description: 'Missing token',
        error_code: ErrorCode.UNAUTHORIZED,
        ip_address: req.ip || '',
        device_info: req.headers['user-agent'] || '',
      });
      return;
    }

    if (requiredPerms.length === 0) {
      await AuditLogService.logEvent({
        userId: req.user?.userId,
        eventType: AuditLogEnum.UNAUTHORIZED,
        module: ModuleEnum.AUTH,
        action_description: 'Unauthorized access attempt',
        error_code: ErrorCode.UNAUTHORIZED,
        ip_address: req.ip || '',
        device_info: req.headers['user-agent'] || '',
      });
      res.status(403).json({ message: 'You don\'t have the required permissions to perform this action.' });
      return;
    }

    try {
      const payload = await JWTService.verifyToken(token);

      if (typeof payload === 'string') {
        await AuditLogService.logEvent({
          userId: req.user?.userId,
          eventType: AuditLogEnum.UNAUTHORIZED,
          module: ModuleEnum.AUTH,
          action_description: 'Unauthorized access attempt',
          error_code: ErrorCode.UNAUTHORIZED,
          ip_address: req.ip || '',
          device_info: req.headers['user-agent'] || '',
        });
        res.status(401).json({ message: 'Invalid token' });
        return;
      }

      const user = await User.findByPk(payload.userId);

      if (user.status !== UserStatus.ACTIVE) {
        res.status(401).json({ message: 'User is inactive' });
        await AuditLogService.logEvent({
          userId: req.user?.userId,
          eventType: AuditLogEnum.UNAUTHORIZED,
          module: ModuleEnum.AUTH,
          action_description: 'Inactive user access attempt',
          error_code: ErrorCode.UNAUTHORIZED,
          ip_address: req.ip || '',
          device_info: req.headers['user-agent'] || '',
        });
        return;
      }

      req.user = payload;

      const hasAllPerms = requiredPerms.every((p) =>
        payload.permissions?.[p.module]?.includes(p.action),
      );

      if (!hasAllPerms) {
        await AuditLogService.logEvent({
          userId: req.user?.userId,
          eventType: AuditLogEnum.UNAUTHORIZED,
          module: ModuleEnum.AUTH,
          action_description: 'Unauthorized access attempt. Insufficient permissions',
          error_code: ErrorCode.UNAUTHORIZED,
          ip_address: req.ip || '',
          device_info: req.headers['user-agent'] || '',
        });
        logger.error(
          `User does not have required permissions: ${JSON.stringify({
            userId: payload.userId,
            requiredPerms,
            user_permissions: payload.permissions,
          })}`,
        );
        res.status(401).json({ message: 'You don\'t have the required permissions to perform this action' });
        return;
      }

      next();
    } catch (err) {
      if (err instanceof jwt.TokenExpiredError) {
        await AuditLogService.logEvent({
          userId: req.user?.userId,
          eventType: AuditLogEnum.UNAUTHORIZED,
          module: ModuleEnum.AUTH,
          action_description: 'Token expired',
          error_code: ErrorCode.UNAUTHORIZED,
          ip_address: req.ip || '',
          device_info: req.headers['user-agent'] || '',
        });
        logger.error('Token expired:', err);
        res.status(401).json({ message: 'Token expired' });
        return;
      }
      await AuditLogService.logEvent({
        userId: req.user?.userId,
        eventType: AuditLogEnum.UNAUTHORIZED,
        module: ModuleEnum.AUTH,
        action_description: 'Token verification error',
        error_code: ErrorCode.UNAUTHORIZED,
        ip_address: req.ip || '',
        device_info: req.headers['user-agent'] || '',
      });
      logger.error(`Token verification error: ${err}`);
      res.status(401).json({ message: 'Invalid token' });
      return;
    }
  };
};

export const isAuthenticated = async (
  req: RequestWithUser,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const token = req.headers.authorization?.split(' ')[1];

  if (!token) {
    await AuditLogService.logEvent({
      userId: req.user?.userId,
      eventType: AuditLogEnum.UNAUTHORIZED,
      module: ModuleEnum.AUTH,
      action_description: 'Missing token',
      error_code: ErrorCode.UNAUTHORIZED,
      ip_address: req.ip || '',
      device_info: req.headers['user-agent'] || '',
    });
    res.status(401).json({ message: 'Unauthorized' });
    return;
  }

  try {
    const payload = await JWTService.verifyToken(token);

    if (typeof payload === 'string') {
      await AuditLogService.logEvent({
        userId: req.user?.userId,
        eventType: AuditLogEnum.UNAUTHORIZED,
        module: ModuleEnum.AUTH,
        action_description: 'Unauthorized access attempt',
        error_code: ErrorCode.UNAUTHORIZED,
        ip_address: req.ip || '',
        device_info: req.headers['user-agent'] || '',
      });
      res.status(401).json({ message: 'Invalid token' });
      return;
    }

    const user = await User.findByPk(payload.userId);

    if (user.status !== UserStatus.ACTIVE) {
      await AuditLogService.logEvent({
        userId: req.user?.userId,
        eventType: AuditLogEnum.UNAUTHORIZED,
        module: ModuleEnum.AUTH,
        action_description: 'Inactive user access attempt',
        error_code: ErrorCode.UNAUTHORIZED,
        ip_address: req.ip || '',
        device_info: req.headers['user-agent'] || '',
      });
      res.status(401).json({ message: 'User is inactive' });
      return;
    }
    req.user = payload;
    next();
  } catch (err) {
    if (err instanceof jwt.TokenExpiredError) {
      await AuditLogService.logEvent({
        userId: req.user?.userId,
        eventType: AuditLogEnum.UNAUTHORIZED,
        module: ModuleEnum.AUTH,
        action_description: 'Token expired',
        error_code: ErrorCode.UNAUTHORIZED,
        ip_address: req.ip || '',
        device_info: req.headers['user-agent'] || '',
      });
      logger.error('Token expired:', err);
      res.status(401).json({ message: 'Token expired' });
      return;
    }
    await AuditLogService.logEvent({
      userId: req.user?.userId,
      eventType: AuditLogEnum.UNAUTHORIZED,
      module: ModuleEnum.AUTH,
      action_description: 'Token verification error',
      error_code: ErrorCode.UNAUTHORIZED,
      ip_address: req.ip || '',
      device_info: req.headers['user-agent'] || '',
    });
    logger.error(`Token verification error: ${err}`);
    res.status(401).json({ message: 'Invalid token' });
    return;
  }
};
