import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  ForeignKey,
} from 'sequelize';
import { User } from '../../auth-service/models/user.model';
import { Clients } from '../../clients/models/clients.model';

export class Files extends Model<
  InferAttributes<Files>,
  InferCreationAttributes<Files>
> {
  declare id: CreationOptional<string>;
  declare key: string;
  declare filename: string;
  declare type: string | null; // e.g., avatar, claim, quote
  declare category: string | null;

  // Relationships
  declare client_id: ForeignKey<string> | null;
  // declare policy_id: ForeignKey<string> | null; // add later
  declare uploaded_by: ForeignKey<string> | null;

  declare is_public: CreationOptional<boolean>;
  declare created_at: CreationOptional<Date>;
}

export function initFilesModel(sequelize: Sequelize): typeof Files {
  Files.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      key: {
        type: DataTypes.TEXT,
        allowNull: false,
        unique: true,
      },
      filename: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      type: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      category: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      client_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: { model: Clients, key: 'id' },
        onDelete: 'SET NULL',
      },
      // policy_id: {
      //   type: DataTypes.UUID,
      //   allowNull: true,
      //   // define FK when Policy model is available
      // },
      uploaded_by: {
        type: DataTypes.UUID,
        allowNull: true,
        references: { model: User, key: 'id' },
        onDelete: 'SET NULL',
      },
      is_public: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      sequelize,
      modelName: 'Files',
      tableName: 'files',
      timestamps: false,
      indexes: [
        {
          fields: ['type'],
        },
        {
          fields: ['uploaded_by'],
        },
      ],
    }
  );

  return Files;
}
