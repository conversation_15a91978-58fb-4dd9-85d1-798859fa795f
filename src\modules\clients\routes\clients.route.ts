import { Router } from 'express';
import { ModuleEnum } from '../../../enum/module.enum';
import { ModuleAction } from '../../../enum/module-action';
import ClientController from '../controller/clients.controller';
import { checkPermissions, isAuthenticated } from '../../../middlewares/auth.middleware';
import { assignAccountManagerSchema, createBulkClientsSchema, createClientSchema, markAsLostSchema, notifyClientsSchema, updateClientSchema } from '../validations/client.schema';
import { validateRequest, validateUUIDParam } from '../../../middlewares/zod-validation.middleware';


const router = Router();
const clientController = new ClientController();


router.post(
  '/',
  checkPermissions([{ module: ModuleEnum.CLIENTS, action: ModuleAction.CREATE }]),
  validateRequest(createClientSchema),
  clientController.createClient.bind(clientController)
);

router.get(
  '/',
  isAuthenticated,
  // checkPermissions([{ module: ModuleEnum.CLIENTS, action: ModuleAction.READ }]),
  clientController.listAllClients.bind(clientController)
);

router.get(
  '/account-managers',
  checkPermissions([{ module: ModuleEnum.CLIENTS, action: ModuleAction.READ }]),
  clientController.listAccountManagers.bind(clientController)
);

router.get(
  '/dashboard',
  checkPermissions([{ module: ModuleEnum.CLIENTS, action: ModuleAction.READ }]),
  clientController.clientDashBoardData.bind(clientController)
);

router.get(
  '/notifications',
  checkPermissions([{ module: ModuleEnum.CLIENTS, action: ModuleAction.READ }]),
  clientController.listNotificationTypes.bind(clientController)
);

router.get(
  '/:id',
  isAuthenticated,
  // checkPermissions([{ module: ModuleEnum.CLIENTS, action: ModuleAction.READ }]),
  validateUUIDParam('id'),
  clientController.getClientById.bind(clientController)
);

router.patch(
  '/assign-manager',
  checkPermissions([{ module: ModuleEnum.CLIENTS, action: ModuleAction.UPDATE }]),
  validateRequest(assignAccountManagerSchema),
  clientController.assignAccountManager.bind(clientController)
);

router.patch(
  '/:id',
  checkPermissions([{ module: ModuleEnum.CLIENTS, action: ModuleAction.UPDATE }]),
  validateUUIDParam('id'),
  validateRequest(updateClientSchema),
  clientController.updateClient.bind(clientController)
);

router.post(
  '/notify',
  checkPermissions([{ module: ModuleEnum.CLIENTS, action: ModuleAction.UPDATE }]),
  validateRequest(notifyClientsSchema),
  clientController.notifyClient.bind(clientController)
);

router.post(
  '/bulk',
  checkPermissions([{ module: ModuleEnum.CLIENTS, action: ModuleAction.CREATE }]),
  validateRequest(createBulkClientsSchema),
  clientController.createBulkClients.bind(clientController)
);

router.patch(
  '/mark-lost/:id',
  checkPermissions([{ module: ModuleEnum.CLIENTS, action: ModuleAction.UPDATE }]),
  validateUUIDParam('id'),
  validateRequest(markAsLostSchema),
  clientController.markClientAsLost.bind(clientController)
);


export default router;