import express, { Request, Response } from 'express';
import bodyParser from 'body-parser';
import { captureAuditMetadata } from './middlewares/capture-audit-metadata';
import dotenv from 'dotenv';
import helmet from 'helmet';
import { config } from './config/env.config';
import httpLogger from './middlewares/http-logger';
import cors from 'cors';
import { generalRateLimiter } from './utils/rate-limiter';
import routes from './shared/routes';
import cookieParser from 'cookie-parser';
import AuditLogService from './modules/audit-trail-service/services/audit-trail.service';
import { AuditLogEnum, ErrorCode } from './enum/trail-action.enum';
import { ModuleEnum } from './enum/module.enum';
import { RequestWithAuditMetadata } from './interfaces/custom-request.interface';
import { errorHandler } from './middlewares/error.middleware';
// import { timezoneMiddleware } from './middlewares/timezone.middleware';

dotenv.config();

export class App {
  static configure(app: express.Application) {
    // Middleware configuration

    const apiVersion = config.API_VERSION || 'v1';

    app.use(bodyParser.json());
    app.use(bodyParser.urlencoded({ extended: true }));
    app.use(captureAuditMetadata);
    app.use(helmet());
    app.use(httpLogger);
    app.use(cookieParser());
    app.use(
      cors({
        origin: (origin, callback) => {
          if (!origin || config.FRONTEND_URL.includes(origin)) {
            return callback(null, true);
          }
          callback(new Error('Not allowed by CORS'));
        },
        credentials: true,
      }),
    );
    // app.use(cors());
    app.set('trust proxy', config.NODE_ENV === 'production');
    app.use(generalRateLimiter);

    // app.use(timezoneMiddleware());
    // Routes configuration
    app.use(`/${apiVersion}`, routes);

    app.get('/', (_req: Request, res: Response) => {
      res.json({ message: 'Hello, World!' });
    });

    // Handling a 404 route not found
    app.use(async (_req: RequestWithAuditMetadata, res: Response) => {
      const { ipAddress, userAgent } = _req.auditMetadata;
      await AuditLogService.logEvent({
        eventType: AuditLogEnum.API_ERROR,
        module: ModuleEnum.SYSTEM,
        userId: null,
        ip_address: ipAddress,
        device_info: userAgent,
        action_description: 'Route not found',
        error_code: ErrorCode.NOT_FOUND,
      });
      res.status(404).json({ message: 'Route not found' });
    });

    // Error handling middleware
    app.use(errorHandler);
  }
}
