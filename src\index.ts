import express from 'express';
import { connectToDB } from './database/dbConnect';
import { App } from './app';
import { config } from './config/env.config';
import logger from './utils/logger';
import { rabbitMQService } from './modules/workers/rabbitmq/rabbitmq.service';
import { registerConsumers } from './modules/workers/rabbitmq/consumers/index.consumer';

// Load environment variables

const app = express();

App.configure(app);

// Start the server — bind to 0.0.0.0 so EC2 can access it externally
const PORT = parseInt(config.PORT || '3000', 10);
app.listen(parseInt(config.PORT, 10), '0.0.0.0', async () => {
  await connectToDB();
  await rabbitMQService.connect();
  await registerConsumers();
  logger.info(`🚀 Server running at http://localhost:${PORT}`);
});
