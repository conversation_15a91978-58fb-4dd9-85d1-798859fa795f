import logger from '../../../utils/logger';
import { config } from '../../../config/env.config';
import { User } from '../models/user.model';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { AuditLogEnum, ErrorCode } from '../../../enum/trail-action.enum';
import { ModuleEnum } from '../../../enum/module.enum';
import { Role } from '../models/role.model';
import { Permission } from '../models/permission.model';
import { OtpHelper } from '../../../utils/otp-helper';
import redis from '../../../utils/redis-client';
import { JWTService } from '../../../utils/jwt';
import { UserStatus } from '../../../enum/user-status.enum';
import { EMAIL_QUEUE } from '../../../constants/role-permission.constant';
import { rabbitMQService } from '../../../modules/workers/rabbitmq/rabbitmq.service';
import { AppError } from '../../../utils/custom-error';

/**
 * Service class for handling OAuth verification and user authentication.
 * This class provides methods to verify OAuth success, handle user login attempts,
 * and manage user permissions.
 * @class OAuthService
 * @constructor
 */
export default class OAuthService {
  private readonly messageQueue = rabbitMQService;

  constructor() { }

  /**
   * Verify OAUTH Success
   * @param {string} secret - The authorization code received from the OAuth provider
   * @param {string} email
   * @paramm {boolean} successful - Flag to indicate if the oatuh process was successful
   * @returns {Promise<any>} - The response from the OAuth provider
   * @throws {Error} - If there is an error during the OAuth verification process
   */
  async verifyOauthSuccess(
    secret: string,
    email: string,
    successful: boolean,
    ipAddress: string,
    userAgent: string,
  ): Promise<any> {
    try {
      if (!successful) {
        throw new AppError('OAuth process was not successful', ErrorCode.BAD_REQUEST);
      }
      if (secret !== config.OAUTH_SECRET) {
        throw new AppError('Invalid OAuth secret', ErrorCode.UNAUTHORIZED);
      }
      if (!config.VALID_DOMAINS.some((domain) => email.endsWith(domain))) {
        await AuditLogService.logEvent({
          userId: null,
          eventType: AuditLogEnum.SSO_LOGIN,
          module: ModuleEnum.AUTH,
          action_description: 'Invalid email domain',
          ip_address: ipAddress,
          device_info: userAgent,
          error_code: ErrorCode.BAD_REQUEST,
        });
        throw new AppError('Invalid email domain', ErrorCode.BAD_REQUEST);
      }
      const user = await User.findOne({
        where: { email, status: UserStatus.ACTIVE },
        include: [
          {
            model: Role,
            as: 'role',
            include: [{ model: Permission, as: 'permissions' }],
          },
        ],
      });
      if (!user) {
        throw new AppError('User not found', ErrorCode.NOT_FOUND);
      }
      if (user.two_factor_auth) {
        const otp = OtpHelper.generateOTP(6);
        await redis.setex(`otp:${user.id}`, 300, otp); // Store OTP in Redis for 5 minutes

        await this.messageQueue.sendToQueue(EMAIL_QUEUE, {
          to: user.email,
          subject: 'Your OTP Code',
          templateName: 'otp-email.html',
          data: {
            first_name: user.first_name,
            otp: otp,
          },
        });

        await AuditLogService.logEvent({
          userId: user.id,
          eventType: AuditLogEnum.SSO_LOGIN,
          module: ModuleEnum.AUTH,
          action_description: 'OTP sent to user',
          ip_address: ipAddress,
          device_info: userAgent,
        });
        return {
          id: user.id,
          email: user.email,
          roleId: user.roleId,
          role: user.role.name,
          first_name: user.first_name,
          last_name: user.last_name,
          phone_number: user.phone_number,
          status: user.status,
          department: user.department,
          multifactor_auth: true,
          permissions: {},
          tokenExpiresIn: null,
          token: null,
        };
      }
      const permissions = user.role?.permissions?.reduce(
        (acc: Record<string, string[]>, perm: Permission): Record<string, string[]> => {
          if (!acc[perm.module]) acc[perm.module] = [];
          acc[perm.module].push(perm.action);
          return acc;
        },
        {} as Record<string, string[]>,
      );

      user.permissions = permissions;
      user.role = user.role.name;

      const { token, tokenExpiresIn } = JWTService.generate(user);
      const firstLogin = user.first_login;
      user.login_attempts = 0;
      user.locked_until = null;
      user.first_login = false;

      await user.save();

      await AuditLogService.logEvent({
        userId: user.id,
        eventType: AuditLogEnum.SSO_LOGIN,
        module: ModuleEnum.AUTH,
        action_description: 'User logged in successfully',
        ip_address: ipAddress,
        device_info: userAgent,
      });

      return {
        id: user.id,
        email: user.email,
        roleId: user.roleId,
        role: user.role,
        first_name: user.first_name,
        last_name: user.last_name,
        phone_number: user.phone_number,
        first_login: firstLogin,
        status: user.status,
        permissions,
        department: user.department,
        tokenExpiresIn,
        multifactor_auth: user.two_factor_auth,
        token,
      };
    } catch (error) {
      await AuditLogService.logEvent({
        userId: null,
        eventType: AuditLogEnum.SSO_LOGIN,
        module: ModuleEnum.AUTH,
        action_description: 'Error during OAuth verification',
        ip_address: ipAddress,
        device_info: userAgent,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
      });
      logger.error(`Error verifying OAuth success: ${error}`);
      throw new AppError('OAuth verification failed');
    }
  }
}
