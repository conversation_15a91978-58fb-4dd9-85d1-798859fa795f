import { S3Client, PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl as getS3SignedUrl } from '@aws-sdk/s3-request-presigner';
import { getSignedUrl as getCloudFrontSignerUrl } from '@aws-sdk/cloudfront-signer';
import { config } from '../../../config/env.config';
import { Files } from '../models/files.model';
import { FileType } from '../../../enum/file-type.enum';
import logger from '../../../utils/logger';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { AuditLogEnum, ErrorCode } from '../../../enum/trail-action.enum';
import { ModuleEnum } from '../../../enum/module.enum';
import { AppError } from '../../../utils/custom-error';

/**
 * Service for handling file uploads and downloads using AWS S3.
 */
export default class FileService {
  private readonly bucket: string;
  private readonly s3: S3Client;
  private readonly cdnBaseUrl: string;

  constructor() {
    this.bucket = config.AWS_S3_BUCKET_NAME;
    this.cdnBaseUrl = config.CDN_ENDPOINT;
    this.s3 = new S3Client({
      region: config.AWS_REGION,
      credentials: {
        accessKeyId: config.AWS_ACCESS_KEY_ID,
        secretAccessKey: config.AWS_SECRET_KEY,
      },
    });
  }

  /**
   * 
   * @param file - The file to be uploaded
   * @param userId - The ID of the user uploading the file
   * @param clientId - Optional client ID for categorization
   * @param type - The type of file being uploaded (e.g., avatar, document, etc.)
   * @param ipAddress - The IP address of the user uploading the file
   * @param userAgent - The user agent of the user uploading the file
   * @param category - Optional category for the file
   * @returns
   */
  async uploadFile({
    file,
    userId,
    clientId,
    type,
    category,
    ipAddress,
    userAgent,
  }: {
    file: Express.Multer.File;
    userId: string;
    clientId?: string;
    type: FileType;
    ipAddress: string;
    userAgent: string;
    category?: string;
  }): Promise<{ key: string; url: string }> {
    if (type === FileType.AVATAR) {
      const { key, url } = await this.uploadPublicAvatar(userId, file, ipAddress, userAgent);
      return { key, url };
    } else {
      return await this.uploadPrivateFile(type, clientId, file, userId, category, ipAddress, userAgent);
    }
  }

  /**
   * 
   * @param userId - The ID of the user uploading the avatar
   * @param file - The avatar file to be uploaded
   * @param ipAddress - The IP address of the user uploading the avatar
   * @param userAgent - The user agent of the user uploading the avatar
   * @returns - An object containing the S3 key and a public URL for the uploaded avatar
   */
  private async uploadPublicAvatar(
    userId: string,
    file: Express.Multer.File,
    ipAddress: string,
    userAgent: string
  ): Promise<{ key: string; url: string; extention: string | undefined; size: number }> {
    try {
      const ext = file.originalname.split('.').pop();
      const timestamp = Date.now();
      const key = `public/avatars/${userId}/${timestamp}.${ext}`;

      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
        CacheControl: 'max-age=86400, public', // Cache for 1 year
      });

      await this.s3.send(command);

      await Files.upsert({
        key,
        filename: file.originalname,
        type: FileType.AVATAR,
        uploaded_by: userId,
        is_public: true,
      });

      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.DOCUMENTS,
        action_description: `Uploaded avatar for user ${userId}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      const signedUrl = await this.getS3SignedDownloadUrl(key, FileType.AVATAR);
      const url = `${signedUrl}?v=${Date.now()}`; // Cache busting
      return {
        key,
        url,
        extention: ext,
        size: file.size,

      };
    } catch (error) {
      logger.error(`Error uploading public avatar: ${error}`);
      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.DOCUMENTS,
        action_description: `Failed to upload avatar: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: '',
        device_info: '',
      });
      throw new AppError('Failed to upload public avatar');
    }
  }

  /**
   * Upload a private file to S3
   * @param type - The type of file being uploaded
   * @param clientId - Optional client ID for categorization
   * @param file - The file to be uploaded
   * @param uploadedBy - The user ID of the uploader
   * @param ipAddress - The IP address of the uploader
   * @param userAgent - The user agent of the uploader
   * @param category - Optional category for the file
   * @returns - An object containing the S3 key and a signed URL for the uploaded file
   */
  private async uploadPrivateFile(
    type: FileType,
    clientId: string | undefined,
    file: Express.Multer.File,
    uploadedBy: string,
    ipAddress: string,
    userAgent: string,
    category?: string,
  ): Promise<{ key: string; url: string; extention: string | undefined; size: number }> {
    try {
      const ext = file.originalname.split('.').pop();
      const key = `private/${type}/${clientId ?? 'misc'}/${Date.now()}-${file.originalname}.${ext}`;

      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
        CacheControl: 'max-age=86400, private', // Cache for 1 day
      });

      await this.s3.send(command);

      await Files.create({
        key,
        filename: file.originalname,
        type,
        category,
        uploaded_by: uploadedBy,
        client_id: clientId ?? null,
        is_public: false,
      });

      await AuditLogService.logEvent({
        userId: uploadedBy,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.DOCUMENTS,
        action_description: `Uploaded ${type} document`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      const url = await this.getS3SignedDownloadUrl(key, type);

      return {
        key,
        url,
        extention: ext,
        size: file.size,

      };
    } catch (error) {
      logger.error(`Error uploading file ${error}`);
      await AuditLogService.logEvent({
        userId: uploadedBy,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.DOCUMENTS,
        action_description: `Failed to upload ${type} document: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      throw new AppError('Failed to upload file');
    }
  }

  async getSignedDownloadUrl(key: string): Promise<string> {
    try {
      return getCloudFrontSignerUrl({
        url: `${config.CDN_ENDPOINT}/${key}`,
        keyPairId: config.CLOUDFRONT_KEY_PAIR_ID,
        privateKey: config.CLOUDFRONT_PRIVATE_KEY,
        dateLessThan: new Date(Date.now() + 5 * 60 * 1000),
      });
    } catch (error) {
      logger.error(`Error generating signed download URL: ${error}`);
      throw new AppError('Failed to generate signed download URL');
    }
  }

  /**
   * Get signed URL
   * @param key - The S3 object key for the file
   * @param type - Optional type of the file (e.g., avatar, document)
   * @returns - A signed URL that allows downloading the file from S3
   */
  async getS3SignedDownloadUrl(key: string, type?: string): Promise<string> {
    try {
      const file = await Files.findOne({ where: { key } });
      if (!file) {
        throw new AppError('File not found', ErrorCode.NOT_FOUND);
      }
      const expiresIn = type === FileType.AVATAR ? 7 * 24 * 60 * 60 : 5 * 60; // 7 days for avatars, 5 minutes for other files
      const command = new GetObjectCommand({ Bucket: this.bucket, Key: key });
      return await getS3SignedUrl(this.s3, command, { expiresIn });
    } catch (error) {
      logger.error(`Error generating S3 signed download URL: ${error}`);
      throw new AppError('Failed to generate S3 signed download URL');
    }
  }
}
