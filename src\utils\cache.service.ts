import redis from './redis-client';
import crypto from 'crypto';

/**
 * CacheService provides methods for caching, retrieving, and invalidating data using Redis.
 */
class CacheService {
  /**
   * Generates a consistent Redis cache key using a prefix and a hash of the provided object.
   * @param {string} prefix - A string prefix to categorize the cache key.
   * @param {object} obj - The object to hash and use for cache key generation.
   * @returns {string} - A namespaced and hashed Redis cache key.
   */
  generateKey(prefix: string, obj: any): string {
    const raw = JSON.stringify(obj);
    const hash = crypto.createHash('md5').update(raw).digest('hex');
    return `${prefix}:${hash}`;
  }

  /**
   * Sets a value in Redis with an optional TTL (time-to-live).
   * @param {string} key - The Redis key under which to store the value.
   * @param {any} value - The value to be cached (will be stringified).
   * @param {number} [ttlInSeconds] - Optional time-to-live in seconds.
   * @returns {Promise<void>} - Resolves when the value is successfully stored.
   */
  async set(key: string, value: any, ttlInSeconds?: number): Promise<void> {
    const serialized = JSON.stringify(value);
    if (ttlInSeconds) {
      await redis.set(key, serialized, 'EX', ttlInSeconds);
    } else {
      await redis.set(key, serialized);
    }
  }

  /**
   * Retrieves and parses a cached value from Redis.
   * @template T
   * @param {string} key - The Redis key to retrieve.
   * @returns {Promise<T | null>} - The parsed value if found, otherwise null.
   */
  async get<T = any>(key: string): Promise<T | null> {
    const result = await redis.get(key);
    return result ? JSON.parse(result) : null;
  }

  /**
   * Invalidates all Redis keys matching the provided pattern using a streaming scan.
   * @param {string} pattern - The key pattern to match (e.g., 'broking-slip:list*').
   * @returns {Promise<void>} - Resolves when matching keys are deleted.
   */
  async invalidateByPattern(pattern: string): Promise<void> {
    const stream = redis.scanStream({
      match: pattern,
      count: 100,
    });

    stream.on('data', (keys: string[]) => {
      if (keys.length) {
        const pipeline = redis.pipeline();
        keys.forEach((key) => pipeline.del(key));
        pipeline.exec();
      }
    });

    return new Promise((resolve, reject) => {
      stream.on('end', resolve);
      stream.on('error', reject);
    });
  }
}

export default new CacheService();
