import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
} from 'sequelize';
import { DiscountTypeDetail } from '../../../interfaces/discount-type.interface';


export class Policy extends Model<
  InferAttributes<Policy>,
  InferCreationAttributes<Policy>
> {
  declare id: CreationOptional<string>;
  declare name: string;
  // declare product: string;
  declare categories: Array<{
    name: string;
    default_rate: number;
  }>;
  declare discount_type_ids: string[];
  declare loading_type_ids: string[];
  // declare assets: string[];
  // declare default_rate: number;
  declare broker_commission: number;
  declare discount_type_details: DiscountTypeDetail[];
  declare loading_type_details: DiscountTypeDetail[];
  declare createdAt?: CreationOptional<Date>;
  declare updatedAt?: CreationOptional<Date>;
}

export function initPolicyModel(sequelize: Sequelize): typeof Policy {
  Policy.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true
      },
      // product: {
      //   type: DataTypes.STRING,
      //   allowNull: false,
      // },
      // default_rate: {
      //   type: DataTypes.DECIMAL(4, 1),
      //   allowNull: false,
      // },
      broker_commission: {
        type: DataTypes.DECIMAL(4, 1),
        allowNull: true,
      },
      categories: {
        type: DataTypes.JSONB,
        allowNull: false,
        defaultValue: [],
      },
      // assets: {
      //   type: DataTypes.ARRAY(DataTypes.STRING),
      //   allowNull: false,
      //   defaultValue: [],
      // },
      discount_type_ids: {
        type: DataTypes.ARRAY(DataTypes.UUID),
        allowNull: false,
        defaultValue: [],
      },
      loading_type_ids: {
        type: DataTypes.ARRAY(DataTypes.UUID),
        allowNull: false,
        defaultValue: [],
      },
      discount_type_details: {
        type: DataTypes.JSONB,
        allowNull: false,
        defaultValue: []
      },
      loading_type_details: {
        type: DataTypes.JSONB,
        allowNull: false,
        defaultValue: []
      },
    },
    {
      sequelize,
      modelName: 'Policy',
      tableName: 'policies',
      timestamps: true,
      paranoid: true,
      // indexes: [
      //   {
      //     unique: false,
      //     fields: ['product'],
      //   },
      // ],
    },
  );

  return Policy;
}
