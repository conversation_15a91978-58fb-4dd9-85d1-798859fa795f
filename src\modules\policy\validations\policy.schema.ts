import { z } from 'zod';

export const createPolicySchema = z.object({
  name: z.string({
    required_error: 'Policy name is required',
    invalid_type_error: 'Policy name must be a string',
  }).min(1, 'Policy name cannot be empty'),

  // default_rate: z.number({
  //   required_error: 'Default rate is required',
  //   invalid_type_error: 'Default rate must be a number',
  // }).min(0, 'Default rate cannot be less than 0%')
  //   .max(100, 'Default rate cannot exceed 100%')
  //   .refine(val => /^\d{1,3}(\.\d)?$/.test(val.toString()), {
  //     message: 'Default rate must have at most one decimal place'
  //   }),

  broker_commission: z.number({
    required_error: 'Broker commission is required',
    invalid_type_error: 'Broker commission must be a number',
  }).min(0, 'Broker commission cannot be less than 0%')
    .max(100, 'Broker commission cannot exceed 100%'),
    // .refine(val => /^\d{1,3}(\.\d)?$/.test(val.toString()), {
    //   message: 'Broker commission must have at most one decimal place'
    // }),

  categories: z.array(
    z.object({
      name: z.string({
        required_error: 'Category name is required',
        invalid_type_error: 'Category name must be a string',
      }).min(1, 'Category name cannot be empty'),

      default_rate: z.number({
        required_error: 'Default rate is required',
        invalid_type_error: 'Default rate must be a number',
      }).min(0, 'Default rate cannot be less than 0')
        .max(100, 'Default rate cannot exceed 100')
        // .refine(val => /^\d{1,3}(\.\d)?$/.test(val.toString()), {
        //   message: 'Default rate must have at most one decimal place'
        // }),
    }),
    {
      required_error: 'Category names are required',
      invalid_type_error: 'Category names must be an array of objects',
    }
  ).min(1, 'At least one category must be provided'),


  // assets: z.array(
  //   z.string({
  //     required_error: 'Asset name is required',
  //     invalid_type_error: 'Each asset name must be a string',
  //   }).min(1, 'Asset name cannot be empty'),
  //   {
  //     required_error: 'Assets names are required',
  //     invalid_type_error: 'Assets must be an array of strings',
  //   }
  // ).min(1, 'At least one Asset must be provided'),

  discountTypes: z.array(
    z.object({
      name: z.string({
        required_error: 'Discount type name is required',
        invalid_type_error: 'Discount type name must be a string',
      }).min(1, 'Discount type name cannot be empty').optional(),

      value: z.number({
        required_error: 'Discount value is required',
        invalid_type_error: 'Discount value must be a number',
      }).min(0, 'Discount value cannot be less than 0%')
        .max(100, 'Discount value cannot exceed 100%').optional(),
        // .refine(val => /^\d{1,3}(\.\d)?$/.test(val.toString()), {
        //   message: 'value must have at most one decimal place'
        // }),

      enabled: z.boolean({
        required_error: 'Discount enabled flag is required',
        invalid_type_error: 'Discount enabled must be a boolean',
      }),
    }),
    {
      required_error: 'Discount types are required',
      invalid_type_error: 'Discount types must be an array of objects',
    }
  ).min(1, 'At least one discount type must be provided').optional(),

  loadingTypes: z.array(
    z.object({
      name: z.string({
        required_error: 'Loading type name is required',
        invalid_type_error: 'Loading type name must be a string',
      }).min(1, 'Loading type name cannot be empty'),

      value: z.number({
        required_error: 'Loading value is required',
        invalid_type_error: 'Loading value must be a number',
      }).min(0, 'Loading value cannot be less than 0%')
        .max(100, 'Loading value cannot exceed 100%'),
        // .refine(val => /^\d{1,3}(\.\d)?$/.test(val.toString()), {
        //   message: 'value must have at most one decimal place'
        // }),

      enabled: z.boolean({
        required_error: 'Loading enabled flag is required',
        invalid_type_error: 'Loading enabled must be a boolean',
      }),
    }),
    {
      required_error: 'Loading types are required',
      invalid_type_error: 'Loading types must be an array of objects',
    }
  ).min(1, 'At least one loading type must be provided').optional(),
});

export type CreatePolicyInput = z.infer<typeof createPolicySchema>;
export const updatePolicySchema = createPolicySchema.partial();

export type UpdatePolicyInput = z.infer<typeof updatePolicySchema>;
