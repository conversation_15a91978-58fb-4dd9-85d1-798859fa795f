import jwt from 'jsonwebtoken';
import { config } from '../config/env.config';
import { JwtBlacklist } from '../modules/auth-service/models/jwt.model';
import { User } from '../modules/auth-service/models/user.model';
import logger from './logger';
import { AppError } from './custom-error';
import { ErrorCode } from '../enum/trail-action.enum';
import { ContactList } from '../modules/insurer/models/contact-list.model';

const SECRET = config.JWT_SECRET;

export class JWTService {
  static generate(user: User, expiresIn: number = 7200) {
    const tokenExpiry = Math.floor(Date.now() / 1000) + expiresIn;
    const token = jwt.sign(
      {
        userId: user.id,
        role: user.role,
        permissions: user.permissions,
        status: user.status,
        teamId: user.teamId,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        phone_number: user.phone_number,
        roleId: user.roleId,
      },
      SECRET,
      { expiresIn },
    );

    return {
      token,
      tokenExpiresIn: tokenExpiry,
    };
  }

  static generateContactToken(contact: ContactList, expiresIn: number = 7200) {
    const tokenExpiry = Math.floor(Date.now() / 1000) + expiresIn;
    const token = jwt.sign(
      {
        id: contact.id,
        full_name: contact.full_name,
        phone_number: contact.phone_number,
        email: contact.email,
        status: contact.status,
        primary_contact: contact.primary_contact,
        insurerId: contact.insurerId,
      },
      SECRET,
      { expiresIn },
    );

    return {
      token,
      tokenExpiresIn: tokenExpiry,
    };
  }

  static async verifyToken(token: string) {
    try {
      const blackList = await JwtBlacklist.findOne({ where: { token } });
      if (blackList) {
        logger.error('Token is blacklisted');
        throw new AppError('Invalid token', ErrorCode.UNAUTHORIZED);
      }

      return jwt.verify(token, SECRET);
    } catch (error: any) {
      if (error.name === 'TokenExpiredError') {
        logger.error(`Token expired: ${error.message}`);
        throw new AppError('Token expired', ErrorCode.UNAUTHORIZED);
      }

      logger.error(`Token verification failed: ${error.message}`);
      throw new AppError('Invalid token', ErrorCode.UNAUTHORIZED);
    }
  }

  static generateRefreshToken(userId: string, expiresIn: number = 30 * 24 * 60 * 60) {
    const tokenExpiry = Math.floor(Date.now() / 1000) + expiresIn;
    const token = jwt.sign(
      {
        userId,
        tokenType: 'refresh',
      },
      SECRET,
      { expiresIn },
    );

    return {
      refreshToken: token,
      refreshTokenExpiresIn: tokenExpiry,
    };
  }

  static verifyRefreshToken(token: string) {
    try {
      const payload = jwt.verify(token, SECRET) as any;
      if (payload.tokenType !== 'refresh') {
        logger.error(`Invalid token type: ${payload.tokenType}`);
        throw new AppError('Invalid token type', ErrorCode.UNAUTHORIZED);
      }
      return payload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        logger.error(`Token expired: ${error}`);
        throw new AppError('Token expired', ErrorCode.UNAUTHORIZED);
      }
      logger.error(`Token verification error: ${error}`);
      throw new AppError('Invalid token');
    }
  }
}
