import { Op } from 'sequelize';
import { Insurer } from '../models/insurer.model';
import { ContactPersonDTO, CreateInsurerDTO, UpdateInsurerDTO } from '../validations/insurer.validation';
import { db } from '../../../database/db';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { AuditLogEnum, ErrorCode } from '../../../enum/trail-action.enum';
import { ModuleEnum } from '../../../enum/module.enum';
import { ContactList } from '../models/contact-list.model';
import logger from '../../../utils/logger';
import { PaginationDto, PaginationMetadataDto, PaginationResultDto } from '../../../utils/pagination';
import { User } from '../../../modules/auth-service/models/user.model';
import { InsurerFilter } from '../../../interfaces/insurer-filter.interface';
import cacheService from '../../../utils/cache.service';
import { CacheKeys } from '../../../enum/cache-keys.enum';
import { InsurerStatus } from '../../../enum/insurer-status.enum';
import { AppError } from '../../../utils/custom-error';


/**
 * Service class for managing insurers.
 */
export default class InsurerService {

  /**
   * 
   * @param initiatorId - ID of the user initiating the request
   * @param ipAddress - IP address of the user
   * @param userAgent - User agent string of the request
   * @param data - Data for creating a new insurer
   * @returns {Promise<Insurer>} - Returns the created insurer object
   * @throws {Error} - Throws an error if the insurer already exists or if there
   */
  async createInsurer(
    initiatorId: string,
    ipAddress: string,
    userAgent: string,
    data: CreateInsurerDTO
  ): Promise<Insurer> {
    const transaction = await db.transaction();
    try {
      const existingInsurer = await Insurer.findOne({ where: { name: data.name }, transaction });

      if (existingInsurer) {
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.INSURER,
          action_description: `Insurer with name ${data.name} already exists`,
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });

        throw new AppError(`Insurer with name ${data.name} already exists`, ErrorCode.CONFLICT);
      }
      const emails = [...new Set(data.contact_persons.map(c => c.email))];
      const phones = [...new Set(data.contact_persons.map(c => c.phone_number))];

      const existingContacts = await ContactList.findAll({
        where: {
          [Op.or]: [
            { email: { [Op.in]: emails } },
            { phone_number: { [Op.in]: phones } }
          ]
        },
        paranoid: false,
        transaction
      });

      if (existingContacts.length > 0) {
        const conflicts = existingContacts.map(c => ({
          email: c.email,
          phone: c.phone_number
        }));
        throw new AppError(`Some Contact details already exists: ${JSON.stringify(conflicts)}`, ErrorCode.CONFLICT);
      }

      const createdInsurer = await Insurer.create({
        name: data.name,
        website_url: data.website_url,
        logo: data.logo
      }, { transaction });

      await ContactList.bulkCreate(
        data.contact_persons.map(contact => ({
          full_name: contact.full_name,
          phone_number: contact.phone_number,
          email: contact.email,
          primary_contact: contact.primary_contact,
          insurerId: createdInsurer.id,
        })),
        { transaction }
      );

      const initiator = await User.findByPk(initiatorId, { transaction });

      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.INSURER,
        action_description: `Insurer "${data.name}" was successfully created by ${initiator.first_name} ${initiator.last_name} on ${new Date().toLocaleString()}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      await transaction.commit();

      await Promise.all([
        cacheService.invalidateByPattern(`${CacheKeys.INSURER_LIST}:*`),
        cacheService.invalidateByPattern(`${CacheKeys.INSURER_CONTACT}:*`)
      ]);

      return createdInsurer;

    } catch (error) {
      await transaction.rollback();
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.INSURER,
        action_description: `Failed to create insurer: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      logger.error(`Error creating insurer: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 
   * @param paginationDto - Pagination options for listing insurers
   * @param searchQuery - Optional search query to filter insurers by name
   * @param filters - Optional filters for status, start date, end date, and name
   * @returns - {Promise<PaginationResultDto<Insurer>>} - Returns a paginated list of insurers
   * @throws {Error} - Throws an error if there is an issue retrieving insurers
   */
  async listInsurers(
    paginationDto: PaginationDto,
    searchQuery?: string,
    filters?: InsurerFilter
  ): Promise<PaginationResultDto<Insurer>> {
    try {
      const { limit, skip, order } = paginationDto;

      const cacheKey = cacheService.generateKey(CacheKeys.INSURER_LIST, {
        pagination: paginationDto,
        searchQuery,
        filters
      });

      const cachedData = await cacheService.get<PaginationResultDto<Insurer>>(cacheKey);
      if (cachedData) {
        return new PaginationResultDto<Insurer>(cachedData.data, cachedData.meta);
      }

      const whereClause: any = {};

      if (searchQuery) {
        whereClause.name = {
          [Op.iLike]: `%${searchQuery}%`
        };
      }
      if (filters?.status) {
        whereClause.status = filters.status;
      }
      if (filters?.startDate || filters?.endDate) {
        whereClause.createdAt = {};

        if (filters.startDate) {
          const start = new Date(filters.startDate);
          if (isNaN(start.getTime())) throw new AppError('Invalid start date', ErrorCode.BAD_REQUEST);
          start.setHours(0, 0, 0, 0);
          whereClause.createdAt[Op.gte] = start;
        }

        if (filters.endDate) {
          const end = new Date(filters.endDate);

          if (isNaN(end.getTime())) throw new AppError('Invalid end date', ErrorCode.BAD_REQUEST);
          end.setHours(23, 59, 59, 999);
          whereClause.createdAt[Op.lte] = end;
        }

        if (filters.startDate && filters.endDate) {
          const start = new Date(filters.startDate);
          const end = new Date(filters.endDate);

          if (start > end) throw new AppError('Start date cannot be after end date', ErrorCode.BAD_REQUEST);
        }
      }
      if (filters.name) {
        whereClause.name = {
          [Op.iLike]: `%${filters.name}%`
        };
      }

      const { count, rows } = await Insurer.findAndCountAll({
        where: whereClause,
        limit,
        include: [{
          model: ContactList,
          as: 'contacts',
          separate: true,
          order: [['createdAt', 'DESC']],
        }],
        offset: skip,
        order: [['createdAt', order]],
      });
      const metadata = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });
      const result = new PaginationResultDto<Insurer>(rows, metadata);

      await cacheService.set(cacheKey, result, 60 * 60); // Cache for 1 hour
      return result;
    } catch (error) {
      logger.error(`Error listing insurers: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 
   * @param id - ID of the insurer to retrieve
   * @returns - {Promise<Insurer>} - Returns the insurer object
   * @throws {Error} - Throws an error if the insurer is not found or if
   */
  async getInsurerById(id: string): Promise<Insurer> {
    try {
      const insurer = await Insurer.findByPk(id, {
        include: [{
          model: ContactList,
          as: 'contacts',
          separate: true,
          order: [['createdAt', 'DESC']],
        }]
      });

      if (!insurer) {
        logger.error(`Insurer with ID ${id} not found`);
        throw new AppError(`Insurer with ID ${id} not found`, ErrorCode.NOT_FOUND);
      }

      return insurer;
    } catch (error) {
      logger.error(`Error retrieving insurer by ID ${id}: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update an insurer and insurer contact list
   * @param id - ID of the insurer to update
   * @param data - Data to update the insurer with
   * @returns - {Promise<Insurer>} - Returns the updated insurer object
   * @throws {Error} - Throws an error if the insurer is not found or if there is an issue updating
   */
  async updateInsurer(
    id: string,
    initiatorId: string,
    ipAddress: string,
    userAgent: string,
    data: UpdateInsurerDTO
  ): Promise<Insurer> {
    const transaction = await db.transaction();
    try {
      const insurer = await Insurer.findByPk(id, { transaction });
      if (!insurer) {
        logger.error(`Insurer with ID ${id} not found`);
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.INSURER,
          action_description: `Failed to update insurer with ID ${id}: Not found`,
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError(`Insurer with ID ${id} not found`, ErrorCode.NOT_FOUND);
      }

      if (data.name) {
        const existingInsurer = await Insurer.findOne({
          where: { name: data.name, id: { [Op.ne]: id } },
          transaction,
        });
        if (existingInsurer) {
          logger.error(`Insurer with name ${data.name} already exists`);
          await AuditLogService.logEvent({
            userId: initiatorId,
            eventType: AuditLogEnum.UPDATE_RESOURCE,
            module: ModuleEnum.INSURER,
            action_description: `Failed to update insurer with ID ${id}: Name conflict`,
            error_code: ErrorCode.BAD_REQUEST,
            ip_address: ipAddress,
            device_info: userAgent,
          });
          throw new AppError(`Insurer with name ${data.name} already exists`, ErrorCode.CONFLICT);
        }
      }

      if (data?.contact_persons?.length > 0) {
        const emails = [...new Set(data.contact_persons.map(c => c.email))];
        const phones = [...new Set(data.contact_persons.map(c => c.phone_number))];

        logger.info(`Checking for existing contacts with emails: ${emails} and phones: ${phones}`);

        const existingContacts = await ContactList.findAll({
          where: {
            [Op.or]: [
              { email: { [Op.in]: emails } },
              { phone_number: { [Op.in]: phones } }
            ]
          },
          paranoid: false,
          transaction
        });

        const emailMap = new Map<string, ContactList>();
        const phoneMap = new Map<string, ContactList>();
        for (const contact of existingContacts) {
          if (contact.email) emailMap.set(contact.email, contact);
          if (contact.phone_number) phoneMap.set(contact.phone_number, contact);
        }

        for (const contact of data.contact_persons) {
          const emailMatch = emailMap.get(contact.email);
          const phoneMatch = phoneMap.get(contact.phone_number);

          if (emailMatch && phoneMatch && emailMatch.id !== phoneMatch.id) {
            throw new AppError(
              `Email and phone number belong to different existing contacts: ${contact.email} and ${contact.phone_number}`,
              ErrorCode.CONFLICT
            );
          }

          const existing = emailMatch || phoneMatch;

          if (existing) {
            await existing.update({
              full_name: contact.full_name,
              email: contact.email,
              phone_number: contact.phone_number,
              primary_contact: contact.primary_contact,
              insurerId: insurer.id,
            }, { transaction });
          } else {
            await ContactList.create({
              full_name: contact.full_name,
              phone_number: contact.phone_number,
              email: contact.email,
              primary_contact: contact.primary_contact,
              insurerId: insurer.id,
            }, { transaction });
          }
        }
      }

      await insurer.update(data);

      if (data.status) {
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.INSURER,
          action_description: `Insurer with ID ${id} status updated to ${data.status} by ${initiatorId} on ${new Date().toLocaleString()}`,
          ip_address: ipAddress,
          device_info: userAgent,
        });
      }

      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.INSURER,
        action_description: `Insurer with ID ${id} was successfully updated by ${initiatorId} on ${new Date().toLocaleString()}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      await transaction.commit();
      await Promise.all([
        cacheService.invalidateByPattern(`${CacheKeys.INSURER_LIST}:*`),
        cacheService.invalidateByPattern(`${CacheKeys.INSURER_CONTACT}:*`)
      ]);
      return this.getInsurerById(id);

    } catch (error) {
      await transaction.rollback();
      logger.error(`Error updating insurer with ID ${id}: ${error}`);
      AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.INSURER,
        action_description: `Failed to update insurer with ID ${id}: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      throw new AppError(
        error.message || 'Internal server error',
        error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 
   * @param id - ID of the insurer to delete
   * @param initiatorId - ID of the user initiating the deletion
   * @param ipAddress - IP address of the user
   * @param userAgent - User agent string of the request
   * @returns - {Promise<void>} - Returns nothing on success
   * @throws {Error} - Throws an error if the insurer is not found or if there is an issue deleting
   */
  async deleteInsurer(
    id: string,
    initiatorId: string,
    ipAddress: string,
    userAgent: string
  ): Promise<void> {
    const transaction = await db.transaction();
    try {
      const insurer = await Insurer.findByPk(id, { transaction });
      if (!insurer) {
        logger.error(`Insurer with ID ${id} not found`);
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.DELETE_RESOURCE,
          module: ModuleEnum.INSURER,
          action_description: `Failed to delete insurer with ID ${id}: Not found`,
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError(`Insurer with ID ${id} not found`, ErrorCode.NOT_FOUND);
      }
      await ContactList.destroy({
        where: { insurerId: id },
        transaction
      });
      await insurer.destroy({ transaction });
      await transaction.commit();
      await Promise.all([
        cacheService.invalidateByPattern(`${CacheKeys.INSURER_LIST}:*`),
        cacheService.invalidateByPattern(`${CacheKeys.INSURER_CONTACT}:*`)
      ]);
    } catch (error) {
      await transaction.rollback();
      logger.error(`Error deleting insurer with ID ${id}: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }


  /**
   * 
   * @param initiatorId - ID of the user initiating the request
   * @param ipAddress - IP address of the user
   * @param userAgent - User agent string of the request
   * @param insurerId - ID of the insurer to add the contact to
   * @param contactData - Data for the contact person to add
   * @returns 
   */
  async addContactToInsurer(
    initiatorId: string,
    ipAddress: string,
    userAgent: string,
    insurerId: string,
    contactData: ContactPersonDTO
  ): Promise<ContactList> {
    const transaction = await db.transaction();
    try {
      const insurer = await Insurer.findByPk(insurerId, { transaction });
      if (!insurer) {
        logger.error(`Insurer with ID ${insurerId} not found`);
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.INSURER,
          action_description: `Failed to add contact to insurer ID ${insurerId}: Insurer not found`,
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError(`Insurer with ID ${insurerId} not found`, ErrorCode.NOT_FOUND);
      }
      const existingContact = await ContactList.findOne({
        where: {
          [Op.or]: [
            { phone_number: contactData.phone_number },
            { email: contactData.email }
          ]
        },
        transaction
      });

      if (existingContact) {
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.INSURER,
          action_description: `Contact with phone number ${contactData.phone_number} or email ${contactData.email} already exists for insurer ID ${insurerId}`,
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });

        throw new AppError(
          `Contact with phone number "${contactData.phone_number}" or email "${contactData.email}" already exists for this insurer.`,
          ErrorCode.CONFLICT
        );
      }

      const contact = await ContactList.create(
        {
          full_name: contactData.full_name,
          phone_number: contactData.phone_number,
          email: contactData.email,
          primary_contact: contactData.primary_contact,
          status: contactData.status || InsurerStatus.ACTIVE,
          insurerId: insurerId,
        },
        { transaction }
      );

      await transaction.commit();

      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.INSURER,
        action_description: `Contact ${contactData.full_name} was successfully added to insurer ID ${insurerId} by ${initiatorId} on ${new Date().toLocaleString()}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      await Promise.all([
        cacheService.invalidateByPattern(`${CacheKeys.INSURER_LIST}:*`),
        cacheService.invalidateByPattern(`${CacheKeys.INSURER_CONTACT}:*`)
      ]);

      return contact;
    } catch (error) {
      await transaction.rollback();

      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.INSURER,
        action_description: `Failed to add contact to insurer ID ${insurerId}: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      logger.error(`Error adding contact to insurer ID ${insurerId}: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }


  /**
   * 
   * @param insurerId - ID of the insurer to list contacts for
   * @param paginationDto - Pagination options for listing contacts
   * @returns - {Promise<PaginationResultDto<ContactList>>} - Returns a paginated list of contacts for the insurer
   * @throws {Error} - Throws an error if there is an issue retrieving contacts
   */
  async listContactsByInsurerId(
    insurerId: string,
    paginationDto: PaginationDto,
  ): Promise<PaginationResultDto<ContactList>> {
    try {
      const { limit, skip, order } = paginationDto;

      const cacheKey = cacheService.generateKey(CacheKeys.INSURER_CONTACT, {
        insurerId,
        pagination: paginationDto,
      });
      const cachedData = await cacheService.get<PaginationResultDto<ContactList>>(cacheKey);
      if (cachedData) {
        return new PaginationResultDto<ContactList>(cachedData.data, cachedData.meta);
      }

      const whereClause: any = { insurerId, status: { [Op.ne]: InsurerStatus.INACTIVE } };
      const { count, rows } = await ContactList.findAndCountAll({
        where: whereClause,
        limit,
        offset: skip,
        order: [['createdAt', order]],
      });

      const metadata = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });

      const result = new PaginationResultDto<ContactList>(rows, metadata);

      await cacheService.set(cacheKey, result, 60 * 60); // Cache for 1 hour
      return result;
    } catch (error) {
      logger.error(`Error listing contacts for insurer ID ${insurerId}: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 
   * @param initiatorId - ID of the user initiating the request
   * @param ipAddress - IP address of the user
   * @param userAgent - User agent string of the request
   * @param contactId - ID of the contact to update
   * @param insurerId - ID of the insurer the contact belongs to
   * @param data - Data to update the contact with
   * @returns - {Promise<ContactList>} - Returns the updated contact object
   */
  async updateContact(
    initiatorId: string,
    ipAddress: string,
    userAgent: string,
    contactId: string,
    insurerId: string,
    data: ContactPersonDTO
  ): Promise<ContactList> {
    const transaction = await db.transaction();
    try {
      const insurer = await Insurer.findByPk(insurerId, { transaction });
      if (!insurer) {
        logger.error(`Insurer with ID ${insurerId} not found`);
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.INSURER,
          action_description: `Failed to add contact to insurer ID ${insurerId}: Insurer not found`,
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError(`Insurer with ID ${insurerId} not found`, ErrorCode.NOT_FOUND);
      }
      const contact = await ContactList.findByPk(contactId, { transaction });
      if (!contact) {
        logger.error(`Contact with ID ${contactId} not found`);
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.INSURER,
          action_description: `Failed to update contact with ID ${contactId}: Not found`,
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError(`Contact with ID ${contactId} not found`, ErrorCode.NOT_FOUND);
      }

      if (data.phone_number) {
        const existingContact = await ContactList.findOne({
          where: {
            phone_number: data.phone_number,
            id: { [Op.ne]: contactId },
            insurerId: contact.insurerId
          },
          transaction
        });
        if (existingContact) {
          logger.error(`Contact with phone number ${data.phone_number} already exists`);
          await AuditLogService.logEvent({
            userId: initiatorId,
            eventType: AuditLogEnum.UPDATE_RESOURCE,
            module: ModuleEnum.INSURER,
            action_description: `Failed to update contact with ID ${contactId}: Phone number conflict`,
            error_code: ErrorCode.CONFLICT,
            ip_address: ipAddress,
            device_info: userAgent,
          });
          throw new AppError(`Contact with phone number ${data.phone_number} already exists`, ErrorCode.CONFLICT);
        }
      }

      if (data.email) {
        const existingContact = await ContactList.findOne({
          where: {
            email: data.email,
            id: { [Op.ne]: contactId },
            insurerId: contact.insurerId
          },
          transaction
        });
        if (existingContact) {
          logger.error(`Contact with email ${data.email} already exists`);
          await AuditLogService.logEvent({
            userId: initiatorId,
            eventType: AuditLogEnum.UPDATE_RESOURCE,
            module: ModuleEnum.INSURER,
            action_description: `Failed to update contact with ID ${contactId}: Email conflict`,
            error_code: ErrorCode.CONFLICT,
            ip_address: ipAddress,
            device_info: userAgent,
          });
          throw new AppError(`Contact with email ${data.email} already exists`, ErrorCode.CONFLICT);
        }

        if (data.status) {
          await AuditLogService.logEvent({
            userId: initiatorId,
            eventType: AuditLogEnum.UPDATE_RESOURCE,
            module: ModuleEnum.INSURER,
            action_description: `Contact with ID ${contactId} status updated to ${data.status} by ${initiatorId} on ${new Date().toLocaleString()}`,
            ip_address: ipAddress,
            device_info: userAgent,
          });
        }
      }

      const updatedContact = await contact.update(data, { transaction });
      await transaction.commit();
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.INSURER,
        action_description: `Contact with ID ${contactId} was successfully updated by ${initiatorId} on ${new Date().toLocaleString()}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      await Promise.all([
        cacheService.invalidateByPattern(`${CacheKeys.INSURER_LIST}:*`),
        cacheService.invalidateByPattern(`${CacheKeys.INSURER_CONTACT}:*`)
      ]);
      return updatedContact;
    } catch (error) {
      await transaction.rollback();
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.INSURER,
        action_description: `Failed to update contact with ID ${contactId}: ${error.message}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error updating contact with ID ${contactId}: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 
   * @param initiatorId - ID of the user initiating the request
   * @param ipAddress - IP address of the user
   * @param userAgent - User agent string of the request
   * @param contactId - ID of the contact to delete
   * @returns - {Promise<void>} - Returns nothing on success
   * @throws {Error} - Throws an error if the contact is not found or if there is an issue deleting
   */
  async deleteContact(
    initiatorId: string,
    ipAddress: string,
    userAgent: string,
    contactId: string,
  ): Promise<void> {
    try {
      const contact = await ContactList.findByPk(contactId);
      if (!contact) {
        logger.error(`Contact with ID ${contactId} not found`);
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.DELETE_RESOURCE,
          module: ModuleEnum.INSURER,
          action_description: `Failed to delete contact with ID ${contactId}: Not found`,
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError(`Contact with ID ${contactId} not found`, ErrorCode.NOT_FOUND);
      }
      await contact.destroy();
      await Promise.all([
        cacheService.invalidateByPattern(`${CacheKeys.INSURER_LIST}:*`),
        cacheService.invalidateByPattern(`${CacheKeys.INSURER_CONTACT}:*`)
      ]);
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.DELETE_RESOURCE,
        module: ModuleEnum.INSURER,
        action_description: `Contact with ID ${contactId} was successfully deleted by ${initiatorId} on ${new Date().toLocaleString()}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });
    } catch (error) {
      logger.error(`Error deleting contact with ID ${contactId}: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }
}