// src/scripts/runMigrations.ts
import { db } from '../database/db';
import { initModels } from '../database';
import logger from '../utils/logger';

// Initialize models
initModels(db);

const runMigrations = async () => {
  try {
    logger.info('Dropping old unique constraints on categories.name...');

    // Drop all old unique constraints that match 'categories_name_key%'
    await db.query(`
      DO $$
      DECLARE
          r RECORD;
      BEGIN
          FOR r IN
              SELECT conname
              FROM pg_constraint
              WHERE conrelid = 'categories'::regclass
                AND contype = 'u'
                AND conname LIKE 'categories_name_key%'
          LOOP
              EXECUTE 'ALTER TABLE categories DROP CONSTRAINT ' || r.conname || ';';
          END LOOP;
      END $$;
    `);

    logger.info('Old unique constraints removed. Running migrations...');

    await db.sync({ alter: true });

    logger.info('✅ Migrations completed successfully.');
    process.exit(0);
  } catch (error) {
    logger.error('❌ Migration failed:', error);
    process.exit(1);
  }
};

runMigrations();
