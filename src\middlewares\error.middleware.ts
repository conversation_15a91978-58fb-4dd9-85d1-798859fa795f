// middlewares/error.middleware.ts

import { Response, NextFunction } from 'express';
import { AppError } from '../utils/custom-error';
import { RequestWithAuditMetadata } from '../interfaces/custom-request.interface';
import { ModuleEnum } from '../enum/module.enum';
import { AuditLogEnum, ErrorCode } from '../enum/trail-action.enum';
import AuditLogService from '../modules/audit-trail-service/services/audit-trail.service';
import logger from '../utils/logger';

/**
 * Function to handle errors in the application.
 * @param err - The error object
 * @param req - The request object
 * @param res - The response object
 * @param _next - The next function
 * @returns - void
 */
export const errorHandler = async (err: any, req: RequestWithAuditMetadata, res: Response, _next: NextFunction): Promise<void> => {
  if (err instanceof AppError) {
    res.status(err.statusCode).json({
      success: false,
      message: err.message,
    });
    return;
  }
  // Fallback for unhandled errors
  logger.error(err);
  try {
    const { ipAddress, userAgent } = req.auditMetadata ?? {};
    await AuditLogService.logEvent({
      eventType: AuditLogEnum.API_ERROR,
      module: ModuleEnum.SYSTEM,
      userId: null,
      ip_address: ipAddress,
      device_info: userAgent,
      action_description: err?.message || 'Internal server error',
      error_code: ErrorCode.INTERNAL_SERVER_ERROR,
    });
  } catch (logErr) {
    logger.error('Error while logging to audit trail:', logErr);
  }

  res.status(500).json({
    success: false,
    message: 'Internal Server Error',
  });
};
