import { Request, Response } from 'express';
import { RequestWithAuditMetadata } from '../../../interfaces/custom-request.interface';
import BrokingSlipService from '../services/broking-slip.service';
import { PaginationDto } from '../../../utils/pagination';

export default class BrokingSlipController {
  private brokingSlipService: BrokingSlipService;

  constructor() {
    this.brokingSlipService = new BrokingSlipService();
  }

  /**
   * Generates a new broking slip with the provided data.
   *
   * @param {RequestWithAuditMetadata} req - Express request object containing user and audit metadata.
   * @param {Response} res - Express response object.
   * @returns {Promise<Response>} JSON response with created broking slip data or error message.
   */
  async generateBrokingSlip(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const data = req.body;

    const response = await this.brokingSlipService.generateBrokingSlip(userId, ipAddress, userAgent, data);
    return res.status(200).json({ data: response, message: 'Broking slip generated successfully' });
  }

  /**
     * List all policies with pagination and optional search
     */
  async listBrokingSlips(req: Request, res: Response): Promise<Response> {
    const paginationDto = new PaginationDto(req.query);
    const searchQuery = req.query.searchQuery as string;
    const status = req.query.status as string;

    const result = await this.brokingSlipService.listBrokingSlips(paginationDto, searchQuery, status);
    return res.status(200).json({ data: result, message: 'Broking slips retrieved successfully' });
  }


  /**
   * Retrieves a broking slip by its ID.
   *
   * @param {Request} req - Express request object containing the broking slip ID in params.
   * @param {Response} res - Express response object.
   * @returns {Promise<Response>} JSON response with the broking slip data or error message.
   */
  async getBrokingSLip(req: Request, res: Response): Promise<Response> {
    const { id } = req.params;

    const client = await this.brokingSlipService.getBrokingSlip(id);
    return res.status(200).json({ data: client, message: 'Broking slip retrieved successfully' });
  }

  /**
   * Updates an existing broking slip with new data.
   *
   * @param {RequestWithAuditMetadata} req - Express request object containing user and audit metadata.
   * @param {Response} res - Express response object.
   * @returns {Promise<Response>} JSON response with updated broking slip data or error message.
   */
  async updateBrokingSlip(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const data = req.body;
    const { id } = req.params;
    const response = await this.brokingSlipService.updateBrokingSlip(userId, ipAddress, userAgent, id, data.status);
    return res.status(200).json({ data: response, message: 'Broking slip updated successfully' });
  }


  /**
   * Broking slip dashboard data
   * @param _req - Request object
   * @param res - Response object
   * @returns - Broking slip dashboard
   */
  async brokingSlipDashBoardData(_req: Request, res: Response): Promise<Response> {
    const data = await this.brokingSlipService.brokingSlipDashboardData();
    return res.status(200).json({
      data,
      message: 'Dashboard data retrieved successfully',
    });
  }

  async sendBrokingSlipToInsurers(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const data = req.body;

    await this.brokingSlipService.sendBrokingSlipToInsurer(
      userId,
      ipAddress,
      userAgent,
      data
    );

    return res.status(200).json({
      data: '',
      message: 'Notification sent to insurer(s)',
    });
  }
}
