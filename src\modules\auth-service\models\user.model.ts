import {
  Sequelize,
  DataTypes,
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  ForeignKey,
} from 'sequelize';
import { UserStatus } from '../../../enum/user-status.enum';

export class User extends Model<InferAttributes<User>, InferCreationAttributes<User>> {
  declare id: CreationOptional<string>;
  declare email: string;
  declare password: string;
  declare roleId: ForeignKey<string>;
  declare status: CreationOptional<UserStatus>;
  declare first_name: string;
  declare last_name: string;
  declare phone_number?: string;
  declare first_login?: CreationOptional<boolean>;
  declare role?: any;
  declare user_type: string;
  declare permissions?: Record<string, string[]>;
  declare login_attempts?: CreationOptional<number>;
  declare locked_until?: CreationOptional<Date>;
  declare two_factor_auth?: CreationOptional<boolean>;
  declare department?: string;
  declare last_login?: CreationOptional<Date>;
  declare updatedAt?: Date;
  declare createdAt?: Date;
  declare teamId?: string;
  declare profile_picture?: string;
}

export function initUserModel(sequelize: Sequelize): typeof User {
  User.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      first_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      last_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
      },
      phone_number: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: true,
      },
      password: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      roleId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(UserStatus.ACTIVE, UserStatus.DEACTIVATED, UserStatus.PENDING),
        defaultValue: UserStatus.PENDING,
      },
      first_login: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      user_type: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      login_attempts: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      locked_until: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      two_factor_auth: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      department: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      last_login: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      teamId: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      profile_picture: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: 'User',
      tableName: 'users',
      timestamps: true,
      paranoid: true,
      indexes: [
        {
          unique: false,
          fields: ['teamId', 'status'],
        },
      ],
    },
  );

  return User;
}
