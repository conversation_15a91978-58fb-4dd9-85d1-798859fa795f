import { SequelizeOptions } from 'sequelize-typescript';
import dotenv from 'dotenv';
import { config } from './config/env.config';

dotenv.config();

const sequelizeConfig: { [key: string]: SequelizeOptions } = {
  development: {
    username: config.DB_USER!,
    password: config.DB_PASS!,
    database: config.DB_NAME!,
    host: config.DB_HOST!,
    port: parseInt(config.DB_PORT || '5432', 10),
    dialect: 'postgres',
    dialectOptions: {
      ssl: {
        rejectUnauthorized: false,
      },
    },
  },
};

export default sequelizeConfig;
