
import { Op, Sequelize } from 'sequelize';
import logger from '../../../utils/logger';
import { Clients } from '../models/clients.model';
import { ModuleEnum } from '../../../enum/module.enum';
import { ClientFilters } from '../../../interfaces/client.interface';
// import { DateRangeEnum } from '../../../enum/date-range.enum';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { AuditLogEnum, ErrorCode } from '../../../enum/trail-action.enum';
import { PaginationDto, PaginationMetadataDto, PaginationResultDto } from '../../../utils/pagination';
import { User } from '../../../modules/auth-service/models/user.model';
import { Role } from '../../../modules/auth-service/models/role.model';
import { Permission } from '../../../modules/auth-service/models/permission.model';
import { UserStatus } from '../../../enum/user-status.enum';
import { rabbitMQService } from '../../workers/rabbitmq/rabbitmq.service';
import { EMAIL_QUEUE, SMS_QUEUE } from '../../../constants/role-permission.constant';
import { ClientNotificationEnum } from '../../../enum/client-notification.enum';
import { NotificationChannelEnum } from '../../../enum/notification-channel.eum';
import cacheService from '../../../utils/cache.service';
import { CacheKeys } from '../../../enum/cache-keys.enum';
import { AppError } from '../../../utils/custom-error';
import { CreateBulkClientsDTO } from '../validations/client.schema';



export default class ClientService {
  private readonly messageQueue = rabbitMQService;

  constructor() { }

  /**
   * Create a new client record
   * @param data - client data
   * @param ipAddress - IpAddress of the user who is creating the client
   * @param userAgent - Device info of the user who is creating the client
   * @param userId - Id of the user who is creating the client
   */
  async createClient(
    ipAddress: string,
    userAgent: string,
    userId: string,
    data: Partial<Clients>) {
    try {
      if (data.client_type === 'corporate' && !data.business_name) {
        throw new AppError('Please provide a business name for corporate clients.', ErrorCode.BAD_REQUEST);
      }
      const existingClient = await Clients.findOne({
        where: {
          [Op.or]: [
            { email: data.email },
            { phone_number: data.phone_number },
          ]
        }
      });

      if (existingClient?.email === data.email) {
        await AuditLogService.logEvent({
          userId: userId,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.CLIENTS,
          action_description: 'Error creating client: Email already exists',
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        logger.error('Error creating client: Client with email already exists');
        throw new AppError('Client with this email address already exists', ErrorCode.CONFLICT);
      }
      if (existingClient?.phone_number === data.phone_number) {
        await AuditLogService.logEvent({
          userId: userId,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.CLIENTS,
          action_description: 'Error creating client: phone number already exists',
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        logger.error('Error creating client: Client with phone number already exists');
        throw new AppError('Client with this phone number already exists', ErrorCode.CONFLICT);
      }
      const client = await Clients.create(data);
      await AuditLogService.logEvent({
        userId: userId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.CLIENTS,
        action_description: 'client successfully created',
        ip_address: ipAddress,
        device_info: userAgent,
      });

      await this.messageQueue.sendToQueue(EMAIL_QUEUE, {
        to: data.email,
        subject: 'Welcome to KBC Brokers',
        templateName: 'client-welcome-email.html',
        data: {
          first_name: data.first_name,
        },
      });
      await Promise.all([
        cacheService.invalidateByPattern(`${CacheKeys.CLIENT_LIST}:*`),
        cacheService.invalidateByPattern(`${CacheKeys.CLIENT_DASHBOARD}:*`)
      ]);

      return client;
    } catch (error) {
      await AuditLogService.logEvent({
        userId: userId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.CLIENTS,
        action_description: `Error creating client ${error}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error('Create client error:', error);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }


  /**
   * 
   * @param paginationDto - pagination optionas
   * @param filters - Filter parameters
   * @returns - Promise<PaginationResultDto<Clients>>
   */
  async listAllClients(
    paginationDto: PaginationDto,
    filters: ClientFilters = {}
  ): Promise<PaginationResultDto<Clients>> {
    try {
      const { limit, skip, order } = paginationDto;
      const whereClause: any = {};

      // Filter by status
      // if (filters.status) {
      //   whereClause.status = filters.status;
      // }

      // if (filters.renewal_status) {
      //   whereClause.renewal_status = filters.renewal_status;
      // }

      // // if (filters.payment_schedule) {
      // // whereClause.payment_schedule = filters.payment_schedule;
      // // }

      // if (filters.inactive_reason) {
      //   whereClause.inactive_reason = filters.inactive_reason;
      // }

      const cacheKey = cacheService.generateKey(CacheKeys.CLIENT_LIST, {
        pagination: paginationDto,
        filters,
      });

      const cached = await cacheService.get<PaginationResultDto<Clients>>(cacheKey);
      if (cached) {
        return new PaginationResultDto(cached.data, cached.meta);
      };

      if (filters.lost_client_reason) {
        whereClause.lost_client_reason = filters.lost_client_reason;
      }

      // // Handle startDate and endDate filtering on createdAt
      // if (filters.startDate || filters.endDate) {
      //   whereClause.createdAt = {
      //     ...(filters.startDate && { [Op.gte]: filters.startDate }),
      //     ...(filters.endDate && { [Op.lte]: filters.endDate }),
      //   };
      // }

      // // Handle dateRange filter
      // if (filters.dateRange) {
      //   const now = new Date();
      //   switch (filters.dateRange) {
      //     case DateRangeEnum.THIRTY:
      //       whereClause.createdAt = {
      //         [Op.gte]: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      //       };
      //       break;
      //     case DateRangeEnum.SEVEN:
      //       whereClause.createdAt = {
      //         [Op.gte]: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      //       };
      //       break;
      //     case DateRangeEnum.THIS_YEAR:
      //       whereClause.createdAt = {
      //         [Op.gte]: new Date(now.getFullYear(), 0, 1), // Jan 1st this year
      //         [Op.lte]: new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999), // Dec 31st this year
      //       };
      //       break;
      //     case DateRangeEnum.LAST_YEAR:
      //       const lastYear = now.getFullYear() - 1;
      //       whereClause.createdAt = {
      //         [Op.gte]: new Date(lastYear, 0, 1),
      //         [Op.lte]: new Date(lastYear, 11, 31, 23, 59, 59, 999),
      //       };
      //       break;
      //   }
      // }

      // // Handle search query across multiple fields
      if (filters.searchQuery) {
        const likePattern = `%${filters.searchQuery}%`;
        whereClause[Op.or] = [
          { email: { [Op.iLike]: likePattern } },        // case-insensitive search
          { client_id: { [Op.iLike]: likePattern } },
          { first_name: { [Op.iLike]: likePattern } },
          { last_name: { [Op.iLike]: likePattern } },
          { phone_number: { [Op.iLike]: likePattern } },
        ];
      }

      const { count, rows } = await Clients.findAndCountAll({
        where: whereClause,
        limit,
        offset: skip,
        order: [['createdAt', order]],
      });

      const meta = new PaginationMetadataDto({
        pageOptionsDto: paginationDto,
        itemCount: count,
      });
      const result = new PaginationResultDto(rows, meta);

      await cacheService.set(cacheKey, result);

      return result;
    } catch (error) {
      logger.error('List clients error:', error);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }


  /**
   * Get a single client by primary key id
   * @param id - client primary key id (UUID)
   */
  async getSingleClient(id: string) {
    try {
      const client = await Clients.findByPk(id, {
        include: {
          model: User,
          as: 'manager',
          attributes: { exclude: ['password'] }
        }
      });
      if (!client) {
        throw new AppError('Client not found', ErrorCode.NOT_FOUND);
      }
      const formattedResponse = {
        ...client.toJSON(),
        activePolicies: 0,
        policiesExpiringSoon: 0,
        totalPremiumDue: 0
      };
      return formattedResponse;
    } catch (error) {
      logger.error('Get client error:', error);
      throw new AppError(error.message || 'Failed to get single client');
    }
  }

  /**
   * Update client by id
   * @param id - client id
   * @param ipAddress - Initiator ipAddress
   * @param userId - Initiator userId
   * @param userAgent - Device info of initiator
   * @param data - fields to update
   */
  async updateClient(id: string, ipAddress: string, userId: string, userAgent: string, data: Partial<Clients>) {
    try {
      const client = await Clients.findByPk(id);

      if (!client) {
        await AuditLogService.logEvent({
          userId: userId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.CLIENTS,
          action_description: 'Client not found',
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('Client not found', ErrorCode.NOT_FOUND);
      }
      if (data.email) {
        const existing = await Clients.findOne({
          where: {
            email: data.email,
            id: { [Op.ne]: id }
          }
        });
        await AuditLogService.logEvent({
          userId: userId,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.CLIENTS,
          action_description: 'Error updating client: Email already exists',
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        if (existing) throw new AppError('Email already exists', ErrorCode.CONFLICT);
      }

      if (data.phone_number) {
        const existing = await Clients.findOne({
          where: {
            phone_number: data.phone_number,
            id: { [Op.ne]: id }
          }
        });
        await AuditLogService.logEvent({
          userId: userId,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.CLIENTS,
          action_description: 'Error creating client: Email already exists',
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        if (existing) throw new AppError('Phone number already exists', ErrorCode.CONFLICT);
      }
      const user = await User.findByPk(userId, {
        attributes: ['first_name', 'last_name']
      });
      if (data.status === 'inactive') {
        await AuditLogService.logEvent({
          userId: userId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.CLIENTS,
          action_description:
            `Client ${client.first_name} ${client.last_name} was deactivated by ${user?.first_name} ${user?.last_name} on ${new Date().toISOString()}`,
          ip_address: ipAddress,
          device_info: userAgent,
        });
      }
      await client.update(data);
      await AuditLogService.logEvent({
        userId: userId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.CLIENTS,
        action_description:
          `Client ${client.first_name} ${client.last_name} was updated by ${user?.first_name} ${user?.last_name} on ${new Date().toISOString()} updated fields ${Object.keys(data).join(', ')}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      await cacheService.invalidateByPattern(`${CacheKeys.CLIENT_LIST}:*`);

      return client;
    } catch (error) {
      logger.error('Update client error:', error);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }


  /**
   * List Account managers
   * @returns users[] 
   */
  async listAccountManagers(paginationDto: PaginationDto): Promise<PaginationResultDto<User>> {
    const { skip, order, limit } = paginationDto;
    const { count, rows: users } = await User.findAndCountAll({
      where: { status: UserStatus.ACTIVE },
      limit,
      offset: skip,
      order: [['createdAt', order]],
      subQuery: false,
      distinct: true,
      attributes: { exclude: ['password'] },
      include: [
        {
          model: Role,
          as: 'role',
          required: true,
          attributes: [],
          include: [
            {
              model: Permission,
              as: 'permissions',
              required: true,
              where: {
                module: 'account_management',
              },
              attributes: []
            },
          ],
        },
      ],
    });

    const meta = new PaginationMetadataDto({
      pageOptionsDto: paginationDto,
      itemCount: count
    });

    return new PaginationResultDto(users, meta);
  }

  /**
 * Fetch client dashboard statistics.
 *
 * Returns aggregated data on:
 * - Total number of clients
 * - Number of active clients
 * - Total policies (sum of policies in clients model)
 * - Number of clients created this month
 * - Placeholder values for policy-related metrics
  */
  async clientDashboardData(): Promise<{
    totalClients: number;
    activeClients: number;
    clientThisMonth: number;
    inactivePolicies: number;
    totalPolicies: number;
    policiesThisMonth: number;
    inactivePoliciesThisMonth: number;
  }> {
    const cacheKey = cacheService.generateKey(CacheKeys.CLIENT_DASHBOARD, {});
    const cached = await cacheService.get<{
      totalClients: number;
      activeClients: number;
      clientThisMonth: number;
      inactivePolicies: number;
      totalPolicies: number;
      policiesThisMonth: number;
      inactivePoliciesThisMonth: number;
    }>(cacheKey);
    if (cached) return cached;

    const now = new Date();

    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999); // End of last day of the month

    const result = await Clients.findAll({
      attributes: [
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalClients'],
        [
          Sequelize.fn('COUNT', Sequelize.literal('CASE WHEN status = \'active\' THEN 1 END')),
          'activeClients',
        ],
        [
          Sequelize.fn(
            'COUNT',
            Sequelize.literal(`CASE WHEN "createdAt" BETWEEN '${startOfMonth.toISOString()}' AND '${endOfMonth.toISOString()}' THEN 1 END`)
          ),
          'clientThisMonth',
        ],
        [
          Sequelize.fn('SUM', Sequelize.col('policies')),
          'totalPolicies',
        ],
      ],
      raw: true,
    });

    const totalClients = Number(result[0]['totalClients'] ?? 0);
    const activeClients = Number(result[0]['activeClients'] ?? 0);
    const clientThisMonth = Number(result[0]['clientThisMonth'] ?? 0);
    const totalPolicies = Number(result[0]['totalPolicies'] ?? 0);

    const dashboardStats = {
      totalClients,
      activeClients,
      clientThisMonth,
      totalPolicies,
      inactivePolicies: 0,
      policiesThisMonth: 0,
      inactivePoliciesThisMonth: 0,
    };

    await cacheService.set(cacheKey, dashboardStats, 2 * 60 * 60);

    return dashboardStats;
  }


  /**
   * Notify a client
   * @param userId - Id of the user to notify
   * @param notifications - list of notifications to notify the user.
   * @param channel - Either sms or email
   */
  async notifyClients(data: {
    clientIds: string[];
    notifications: string[];
    channel: string;
    ipAddress: string;
    userId: string;
    userAgent: string;
  }) {
    const { clientIds, notifications, channel, ipAddress, userAgent, userId } = data;
    const queue = channel === NotificationChannelEnum.EMAIL ? EMAIL_QUEUE : SMS_QUEUE;

    const success: string[] = [];
    const failed: { clientId: string; reason: string }[] = [];

    for (const clientId of clientIds) {
      try {
        const client = await this.findById(clientId);

        for (const notification of notifications) {
          try {
            await AuditLogService.logEvent({
              userId,
              eventType: AuditLogEnum.CREATE_RESOURCE,
              module: ModuleEnum.CLIENTS,
              action_description: `Client ${client.email} notified for ${notification}`,
              ip_address: ipAddress,
              device_info: userAgent,
            });

            await this.messageQueue.sendToQueue(queue, {
              to: client.email,
              subject: `${notification.replace(/_/g, ' ').toUpperCase()}`,
              templateName: `${notification}.html`,
              data: {
                first_name: client.first_name,
              },
            });

            logger.info(`✅ Notification ${notification} sent to client ${client.email}`);
          } catch (err) {
            logger.error(`❌ Failed to notify ${client.email} for ${notification}. Error: ${err.message}`);
          }
        }

        success.push(clientId);
      } catch (error) {
        logger.error(`❌ Failed to notify clientId ${clientId}: ${error.message}`);
        failed.push({ clientId, reason: error.message });
      }
    }

    return { success, failed };
  }


  /**
   * List Notifications to be sent to client
   */
  async listNotificationTypes() {
    return Object.values(ClientNotificationEnum);
  }


  /**
   * Find a client by its id
   * @param clientId - Client id
   * @returns - Clients
   */
  async findById(clientId: string): Promise<Clients> {
    try {
      const client = await Clients.findByPk(clientId);

      if (!client) {
        throw new AppError('Client not found', ErrorCode.NOT_FOUND);
      }

      return client;
    } catch (error) {
      logger.error(`Error getting client by id ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }


  /**
     * Create a new client record
     * @param data - list of client data
     * @param ipAddress - IpAddress of the user who is creating the client
     * @param userAgent - Device info of the user who is creating the client
     * @param userId - Id of the user who is creating the client
     */
  async createBulkClients(
    ipAddress: string,
    userAgent: string,
    userId: string,
    data: CreateBulkClientsDTO
  ) {
    const results = [];
    const errors = [];

    const { clients, account_manager_id } = data;

    for (const clientData of clients) {
      try {
        const fullData = {
          ...clientData,
          account_manager_id,
        };

        const client = await this.createClient(ipAddress, userAgent, userId, fullData);
        results.push(client);
      } catch (error) {
        errors.push({
          data: clientData,
          message: error.message,
        });
      }
    }

    return { success: results, failed: errors };
  }


  /**
 * Assign an account manager to multiple clients
 * @param accountManagerId - The ID of the account manager
 * @param clientIds - An array of client IDs
 * @param metadata - audit-related metadata
 */
  async assignAccountManagerToClients(
    accountManagerId: string,
    clientIds: string[],
    metadata: {
      ipAddress: string,
      userAgent: string,
      userId: string
    }
  ) {
    const { ipAddress, userAgent, userId } = metadata;
    const failed: { clientId: string, reason: string }[] = [];
    const updatedClients = [];

    for (const clientId of clientIds) {
      try {
        const client = await Clients.findByPk(clientId);
        if (!client) {
          failed.push({ clientId, reason: 'Client not found' });
          continue;
        }

        client.account_manager_id = accountManagerId;
        await client.save();

        await AuditLogService.logEvent({
          userId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.CLIENTS,
          action_description: `Assigned account manager to client ${client.email}`,
          ip_address: ipAddress,
          device_info: userAgent,
        });

        updatedClients.push(clientId);
      } catch (error) {
        failed.push({ clientId, reason: error.message });
      }
    }

    return {
      success: updatedClients,
      failed,
    };
  }

  /**
   * 
   * @param clientId - Client id to mark as lost
   * @param reason - Reason for marking the client as lost
   * @param ipAddress - IpAddress of the user marking the client as lost
   * @param userAgent - Device info of the user marking the client as lost
   * @param userId - Id of the user marking the client as lost
   * @returns 
   */
  async markClientAsLost(
    clientId: string,
    reason: string,
    ipAddress: string,
    userAgent: string,
    userId: string,
  ) {
    try {
      const client = await Clients.findByPk(clientId);
      if (!client) {
        await AuditLogService.logEvent({
          userId: userId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.CLIENTS,
          action_description: 'Client not found',
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('Client not found', ErrorCode.NOT_FOUND);
      }
      client.lost_client_reason = reason;
      client.status = 'lost';
      await client.save();
      const user = await User.findByPk(userId, {
        attributes: ['first_name', 'last_name']
      });
      await AuditLogService.logEvent({
        userId: userId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.CLIENTS,
        action_description: `Client ${client.first_name} ${client.last_name} was marked as lost by ${user?.first_name} ${user?.last_name} on ${new Date().toISOString()}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      return client;
    } catch (error) {
      logger.error('Error finding client:', error);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }
}
