import { Request, Response, NextFunction } from 'express';
import { z, ZodError } from 'zod';

/**
 * Middleware for Zod validation of request body.
 * @param schema - Zod schema to validate the incoming request body.
 */
export const validateRequest = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.body) {
      res.status(400).json({
        message: 'payload is required',
        data: '',
      });
      return;
    }
    try {
      // Validate the request body with the provided Zod schema
      schema.parse(req.body);
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        res.status(400).json({
          message: error.errors[0].message,
          data: '',
        });
        return;
      }
      next(error);
    }
  };
};

/**
 * Middleware for Zod validation of UUID route parameters.
 * @param paramName - The name of the route parameter to validate.
 */
export const validateUUIDParam =
  (paramName: string) => (req: Request, res: Response, next: NextFunction) => {
    const schema = z.string().uuid();
    const paramValue = req.params[paramName];

    if (!paramValue) {
      res.status(400).json({
        message: 'Validation failed',
        errors: [{ message: `${paramName} is required` }],
      });
      return;
    }

    const result = schema.safeParse(paramValue);

    if (!result.success) {
      res.status(400).json({
        message: `Invalid UUID for parameter "${paramName}"`,
      });
      return;
    }

    next();
  };
