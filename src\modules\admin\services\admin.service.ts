import { literal, Op } from 'sequelize';
import { User } from '../../auth-service/models/user.model';
import { Role } from '../../auth-service/models/role.model';
import {
  PaginationDto,
  PaginationMetadataDto,
  PaginationResultDto,
} from '../../../utils/pagination';
import logger from '../../../utils/logger';
import { Invitation } from '../../auth-service/models/invitation.model';
import { InviteUserPayload } from '../../../interfaces/auth.interface';
import { PasswordHelper } from '../../../utils/password.utils';
import { AuditLogEnum, ErrorCode } from '../../../enum/trail-action.enum';
import { PhoneNumberHelper } from '../../../utils/phonenumber.util';
import { ModuleEnum } from '../../../enum/module.enum';
import { config } from '../../../config/env.config';
import { JWTService } from '../../../utils/jwt';
import { OtpHelper } from '../../../utils/otp-helper';
import { RoleEnum } from '../../../enum/role.enum';
import { Team } from '../../../modules/team/models/team.model';
import TeamService from '../../../modules/team/services/team.service';
import { UserStatus } from '../../../enum/user-status.enum';
import { SearchQuery } from '../../../interfaces/query.interface';
import { Company } from '../models/company.model';
import { EMAIL_QUEUE } from '../../../constants/role-permission.constant';
import { rabbitMQService } from '../../workers/rabbitmq/rabbitmq.service';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { AppError } from '../../../utils/custom-error';


/**
 * AdminService class to manage admin functionalities
 * @class AdminService
 */
export default class AdminService {
  private readonly teamService: TeamService;
  private readonly messageQueue = rabbitMQService;
  constructor() {
    this.teamService = new TeamService();
    // this.messageQueue = new RabbitMQService();
  }

  /**
   * Invite user method
   * @param email User email
   * @param first_name User first name
   * @param last_name User last name
   * @param phone_number Optional phone number
   * @param roleId Role assigned to the user
   * @returns Promise<{ id: string; email: string; role: string; first_name: string; last_name: string; phone_number?: string; roleId: string; active: boolean; permissions: Record<string, string[]> }>
   * @throws Error if the user already exists or if the role is not found
   */
  async InviteUser({
    email,
    first_name,
    last_name,
    phone_number,
    department,
    roleId,
    userId,
    ipAddress,
    userAgent,
  }: InviteUserPayload): Promise<{
    id: string;
    email: string;
    roleId: string;
    role: string;
    first_name: string;
    last_name: string;
    department: string;
    phone_number?: string;
    status: UserStatus;
    permissions: Record<string, string[]>;
  }> {
    try {
      if (!config.VALID_DOMAINS.some((domain) => email.endsWith(domain))) {
        await AuditLogService.logEvent({
          userId: null,
          eventType: AuditLogEnum.REGISTER_FAILED,
          module: ModuleEnum.AUTH,
          action_description: `Invalid email domain: ${email}`,
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('Invalid email domain. Please use an official company email', ErrorCode.BAD_REQUEST);
      }
      if (phone_number) {
        phone_number = PhoneNumberHelper.formatToCountryStandard(phone_number);
      }
      const whereClause: any[] = [{ email }];
      if (phone_number) {
        whereClause.push({ phone_number });
      }
      const [existingUser, existingInvitation] = await Promise.all([
        User.findOne({
          where: {
            [Op.or]: whereClause,
          },
        }),
        Invitation.findOne({
          where: {
            invitee_email: email,
          },
        }),
      ]);


      if (existingUser) {
        if (existingUser.email === email && existingInvitation?.is_accepted === true) {
          await AuditLogService.logEvent({
            userId: userId,
            eventType: AuditLogEnum.CREATE_USER,
            module: ModuleEnum.USERS,
            action_description: `Failed to invite new user with email: ${email}`,
            error_code: ErrorCode.CONFLICT,
            ip_address: ipAddress,
            device_info: userAgent,
          });
          throw new AppError('Email already exists', ErrorCode.CONFLICT);
        } else if (existingUser.email === email && existingInvitation?.is_accepted === false) {
          const { token, tokenExpiresIn } = JWTService.generate(existingUser, 604800); // 1 week expiration
          const resetLink = `${config.FRONTEND_URL[0]}/complete-registration?token=${token}&email=${email}&type=newUser`;

          await existingInvitation.update({
            invite_token: token,
            assigned_role_id: roleId,
            invited_by: userId,
            expires_at: new Date(tokenExpiresIn * 1000),
          });

          // send email to user
          await this.messageQueue.sendToQueue(EMAIL_QUEUE, {
            to: email,
            subject: 'Welcome to KBC Brokers',
            templateName: 'new-user-email.html',
            data: {
              first_name: existingUser.first_name,
              resetLink,
            },
          });
          return;
        }
        if (existingUser.phone_number === phone_number) {
          await AuditLogService.logEvent({
            userId: userId,
            eventType: AuditLogEnum.CREATE_USER,
            module: ModuleEnum.USERS,
            action_description: `Failed to invite new user with email: ${email}`,
            error_code: ErrorCode.CONFLICT,
            ip_address: ipAddress,
            device_info: userAgent,
          });
          throw new AppError('Phone number already exists', ErrorCode.CONFLICT);
        }
      }

      const password = OtpHelper.generatePassword(10);

      const hashedPassword = await PasswordHelper.hashPassword(password);

      const role = await Role.findByPk(roleId);
      if (!role) {
        await AuditLogService.logEvent({
          userId: userId,
          eventType: AuditLogEnum.CREATE_USER,
          module: ModuleEnum.USERS,
          action_description: `Failed to invite new user with email: ${email}`,
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError('Role not found', ErrorCode.NOT_FOUND);
      }

      const newUser = await User.create({
        email,
        password: hashedPassword,
        first_name,
        last_name,
        phone_number,
        department,
        roleId,
        user_type: role.name,
      });

      // const inviter = await User.findByPk(userId);

      // try {
      //   await this.handleTeamAssignment(role, inviter, newUser, userId);
      // } catch (error) {
      //   await AuditLogService.logEvent({
      //     userId: userId,
      //     eventType: AuditLogEnum.CREATE_USER,
      //     module: ModuleEnum.USERS,
      //     action_description: `Team Assignment failed for user with email: ${email}`,
      //     error_code: ErrorCode.BAD_REQUEST,
      //     ip_address: ipAddress,
      //     device_info: userAgent,
      //   });
      //   logger.error(`Error assigning team to user: ${error}`);
      // }
      const permissions = role.permissions?.reduce(
        (acc, perm) => {
          if (!acc[perm.module]) acc[perm.module] = [];
          acc[perm.module].push(perm.action);
          return acc;
        },
        {} as Record<string, string[]>,
      );

      const { token, tokenExpiresIn } = JWTService.generate(newUser, 604800); // 1 week expiration
      const resetLink = `${config.FRONTEND_URL[0]}/complete-registration?token=${token}&email=${email}&type=newUser`;

      await Invitation.create({
        invitee_email: email,
        invited_by: userId,
        invite_token: token,
        assigned_role_id: roleId,
        expires_at: new Date(tokenExpiresIn * 1000), // Expires in 1 week
      });

      // send email to user
      await this.messageQueue.sendToQueue(EMAIL_QUEUE, {
        to: newUser.email,
        subject: 'Welcome to KBC Brokers',
        templateName: 'new-user-email.html',
        data: {
          first_name: newUser.first_name,
          resetLink,
        },
      });
      const user = await User.findByPk(userId, {
        attributes: ['first_name', 'last_name'],
      });
      await AuditLogService.logEvent({
        userId: userId,
        eventType: AuditLogEnum.CREATE_USER,
        module: ModuleEnum.USERS,
        entity_id: newUser.id,
        action_description: `${user.first_name} ${user.last_name} Invited new user with email: ${newUser.email} and role: ${role.name} on ${new Date().toISOString()}`,
        ip_address: ipAddress,
        device_info: userAgent,
      });


      return {
        id: newUser.id,
        email: newUser.email,
        roleId: newUser.roleId,
        role: role.name,
        first_name: newUser.first_name,
        last_name: newUser.last_name,
        phone_number: newUser.phone_number,
        department: newUser.department,
        status: newUser.status,
        permissions,
      };
    } catch (error: any) {
      logger.error(`Error during user registration ${error}`);
      await AuditLogService.logEvent({
        userId: userId,
        eventType: AuditLogEnum.CREATE_USER,
        module: ModuleEnum.USERS,
        action_description: `Failed to invite new user with email: ${email} by user with ID: ${userId} on ${new Date().toISOString()}`,
        error_code: error.message,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Private method to check if a team should be created for a user
   * @param role - Role of the user
   * @param inviter - Inviter user object
   * @param newUser - New user object
   * @param invitedById - ID of the user who invited
   * 
   */

  private async handleTeamAssignment(
    role: Role,
    inviter: User,
    newUser: User,
    invitedById: string,
  ): Promise<void> {
    const isSystemAdmin = inviter.user_type === RoleEnum.SYSTEM_ADMIN;
    const isNewUserAdmin = role.name === RoleEnum.ADMIN;
    const isNewUserSystemAdmin = role.name === RoleEnum.SYSTEM_ADMIN;

    // Create a team if the new user is a SYSTEM_ADMIN
    // Or if a SYSTEM_ADMIN is inviting an ADMIN
    if (isNewUserSystemAdmin || (isSystemAdmin && isNewUserAdmin)) {
      const team = await Team.create({ ownerId: newUser.id });
      await this.teamService.addUserToTeam(newUser.id, team.id, invitedById);
    } else {
      await this.teamService.addUserToTeam(newUser.id, inviter.teamId, invitedById);
    }
  }


  /**
   * List All Users with Pagination
   * @param paginationDto - Pagination options
   * @param searchQuery - Search query options
   * @returns PaginationResultDto<User> - List of users with pagination metadata
   */
  async listAllUsers(paginationDto: PaginationDto, searchQuery: SearchQuery): Promise<PaginationResultDto<User>> {
    const { limit, order, skip } = paginationDto;
    const { searchQuery: search } = searchQuery;
    const whereClause: any = {};
    if (search) {
      const searchLower = search.toLowerCase();
      whereClause[Op.or] = [
        { first_name: { [Op.like]: `%${searchLower}%` } },
        { last_name: { [Op.like]: `%${searchLower}%` } },
        { email: { [Op.like]: `%${searchLower}%` } },
        { phone_number: { [Op.like]: `%${searchLower}%` } },
        { user_type: { [Op.like]: `%${searchLower}%` } },
        { department: { [Op.like]: `%${searchLower}%` } },
        literal(`CAST("status" as TEXT) LIKE '%${search}%'`),
      ];
    }

    const { count, rows } = await User.findAndCountAll({
      where: whereClause,
      limit,
      offset: skip,
      order: [['createdAt', order]],
      attributes: { exclude: ['password'] },
      include: [
        {
          model: Role,
          as: 'role',
        },
      ],
    });

    const meta = new PaginationMetadataDto({
      pageOptionsDto: paginationDto,
      itemCount: count,
    });

    return new PaginationResultDto(rows, meta);
  }

  /**
   * Activate or Deactivate User
   * @param userId - ID of the user to be activated or deactivated
   * @param action - 'activate' or 'deactivate'
   * @param ipAddress - IP address of the user making the request
   * @param userAgent - User agent of the user making the request
   * @returns string - Confirmation message
   */
  async activateOrDeactivateUser(data: { userId: string, action: 'activate' | 'deactivate', ipAddress: string, userAgent: string }): Promise<User> {
    const { userId, action, ipAddress, userAgent } = data;
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        await AuditLogService.logEvent({
          userId: userId,
          eventType: AuditLogEnum.UPDATE_RESOURCE,
          module: ModuleEnum.USERS,
          action_description: `Failed to ${action} user with ID: ${userId}`,
          error_code: ErrorCode.NOT_FOUND,
          ip_address: ipAddress,
          device_info: userAgent,
        });
        throw new AppError(`User with ID ${userId} not found.`, ErrorCode.NOT_FOUND);
      }
      user.status = action === 'activate' ? UserStatus.ACTIVE : UserStatus.DEACTIVATED;

      await AuditLogService.logEvent({
        userId: userId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.USERS,
        entity_id: user.id,
        action_description: `User with ID ${userId} has been ${action}d`,
        ip_address: ipAddress,
        device_info: userAgent,
      });

      await this.messageQueue.sendToQueue(EMAIL_QUEUE, {
        to: user.email,
        subject: action === 'activate' ? 'Your account has been activated' : 'Your account has been deactivated',
        templateName: action === 'activate' ? 'activation-email.html' : 'deactivation-email.html',
        data: {
          first_name: user.first_name,
          login_link: `${config.FRONTEND_URL[0]}/login`,
          support_email: config.DEFAULT_EMAIL_SENDER,
        },
      });

      await user.save();
      return user;
    } catch (error) {
      await AuditLogService.logEvent({
        userId: userId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.USERS,
        action_description: `Failed to ${action} user with ID: ${userId}`,
        error_code: error.message,
        ip_address: ipAddress,
        device_info: userAgent,
      });
      logger.error(`Error activating/deactivating user: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Assign Role to User
   * @param userId - ID of the user
   * @param roleId - ID of the role
   * @returns string - Confirmation message
   */
  async assignRoleToUser(userId: string, roleId: string, initiatorId: string): Promise<string> {
    try {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new AppError(`User with ID ${userId} not found.`, ErrorCode.NOT_FOUND);
      }

      if (user.id === initiatorId) {
        await AuditLogService.logEvent({
          userId: initiatorId,
          eventType: AuditLogEnum.CREATE_RESOURCE,
          module: ModuleEnum.ROLES,
          action_description: `Failed to assign role to user with ID: ${userId}`,
          error_code: ErrorCode.BAD_REQUEST,
          ip_address: 'N/A',
          device_info: 'N/A',
        });
        throw new AppError('You cannot assign a role to yourself.', ErrorCode.BAD_REQUEST);
      }

      const role = await Role.findByPk(roleId);
      if (!role) {
        throw new AppError(`Role with ID ${roleId} not found.`, ErrorCode.NOT_FOUND);
      }
      user.roleId = role.id.toString();
      user.user_type = role.name;

      await user.save();
      return `Role with ID ${roleId} assigned to user with ID ${userId}.`;
    } catch (error) {
      await AuditLogService.logEvent({
        userId: initiatorId,
        eventType: AuditLogEnum.CREATE_RESOURCE,
        module: ModuleEnum.ROLES,
        action_description: `Failed to assign role to user with ID: ${userId}`,
        error_code: ErrorCode.INTERNAL_SERVER_ERROR,
        ip_address: 'N/A',
        device_info: 'N/A',
      });
      logger.error(`Error assigning role to user: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * List All Modules
   * @returns Promise<[]> - List of all modules
   */
  async listAllModules(): Promise<ModuleEnum[]> {
    try {
      return Object.values(ModuleEnum);
    } catch (error) {
      logger.error(`Error listing all modules: ${error}`);
      throw new AppError(error.message || 'Internal server error', error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update System Preferences
   * @param ipAddress - ip address of the user
   * @param userAgent - device info of the user
   * @param data - fields to update
   */
  async updateSystemPreferences(
    userId: string,
    ipAddress: string,
    userAgent: string,
    data: {
      system_preferences?: Record<string, any>;
      security_policy?: Record<string, any>;
      general_parameters?: Record<string, any>;
    }
  ): Promise<Company> {
    const company = await this.getCompany();

    if (company) {
      const updatedSystemPreferences = {
        ...(company.system_preferences || {}),
        ...(data.system_preferences || {}),
      };

      const updatedSecurityPolicy = {
        ...(company.security_policy || {}),
        ...(data.security_policy || {}),
      };

      const updatedGeneralParameters = {
        ...(company.general_parameters || {}),
        ...(data.general_parameters || {}),
      };

      await company.update({
        system_preferences: updatedSystemPreferences,
        security_policy: updatedSecurityPolicy,
        general_parameters: updatedGeneralParameters,
      });

      await AuditLogService.logEvent({
        userId,
        eventType: AuditLogEnum.UPDATE_RESOURCE,
        module: ModuleEnum.SYSTEM,
        action_description: 'Company settings updated',
        ip_address: ipAddress,
        device_info: userAgent,
      });

      return await Company.findByPk(company.id);
    }

    const newCompany = await Company.create({
      system_preferences: data.system_preferences || {},
      security_policy: data.security_policy || {},
      general_parameters: data.general_parameters || {},
    });

    await AuditLogService.logEvent({
      userId,
      eventType: AuditLogEnum.UPDATE_RESOURCE,
      module: ModuleEnum.SYSTEM,
      action_description: 'Company settings created',
      ip_address: ipAddress,
      device_info: userAgent,
    });

    return newCompany;
  }


  /**
 * Retrieve a company by ID
 * @returns The company instance if found
 * @throws Error if company is not found
 */
  async getCompany(): Promise<Company | Record<string, never>> {
    const company = await Company.findOne();

    return company ?? null;
  }
}
