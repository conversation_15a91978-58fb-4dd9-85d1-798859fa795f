import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { InsurerStatus } from '../enum/insurer-status.enum';
import AuditLogService from '../modules/audit-trail-service/services/audit-trail.service';
import { JWTService } from '../utils/jwt';
import { Insurer } from '../modules/insurer/models/insurer.model';
import logger from '../utils/logger';
import { AuditLogEnum, ErrorCode } from '../enum/trail-action.enum';
import { ModuleEnum } from '../enum/module.enum';

export interface RequestWithInsurer extends Request {
  insurer?: any;
}

export const isInsurerAuthenticated = async (
  req: RequestWithInsurer,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const token = req.headers.authorization?.split(' ')[1];

  if (!token) {
    await AuditLogService.logEvent({
      userId: null,
      eventType: AuditLogEnum.UNAUTHORIZED,
      module: ModuleEnum.AUTH,
      action_description: 'Missing insurer token',
      error_code: ErrorCode.UNAUTHORIZED,
      ip_address: req.ip || '',
      device_info: req.headers['user-agent'] || '',
    });
    res.status(401).json({ message: 'Unauthorized' });
    return;
  }

  try {
    const payload = await JWTService.verifyToken(token);

    if (typeof payload === 'string' || !payload.insurerId) {
      await AuditLogService.logEvent({
        userId: null,
        eventType: AuditLogEnum.UNAUTHORIZED,
        module: ModuleEnum.AUTH,
        action_description: 'Invalid insurer token',
        error_code: ErrorCode.UNAUTHORIZED,
        ip_address: req.ip || '',
        device_info: req.headers['user-agent'] || '',
      });
      res.status(401).json({ message: 'Invalid token' });
      return;
    }

    const insurer = await Insurer.findByPk(payload.insurerId);

    if (!insurer || insurer.status !== InsurerStatus.ACTIVE) {
      await AuditLogService.logEvent({
        userId: null,
        eventType: AuditLogEnum.UNAUTHORIZED,
        module: ModuleEnum.AUTH,
        action_description: 'Inactive or missing insurer access attempt',
        error_code: ErrorCode.UNAUTHORIZED,
        ip_address: req.ip || '',
        device_info: req.headers['user-agent'] || '',
      });
      res.status(401).json({ message: 'Insurer is inactive or not found' });
      return;
    }

    req.insurer = payload;
    next();
  } catch (err) {
    if (err instanceof jwt.TokenExpiredError) {
      await AuditLogService.logEvent({
        userId: null,
        eventType: AuditLogEnum.UNAUTHORIZED,
        module: ModuleEnum.AUTH,
        action_description: 'Insurer token expired',
        error_code: ErrorCode.UNAUTHORIZED,
        ip_address: req.ip || '',
        device_info: req.headers['user-agent'] || '',
      });
      logger.error('Insurer token expired:', err);
      res.status(401).json({ message: 'Token expired' });
      return;
    }

    await AuditLogService.logEvent({
      userId: null,
      eventType: AuditLogEnum.UNAUTHORIZED,
      module: ModuleEnum.AUTH,
      action_description: 'Insurer token verification error',
      error_code: ErrorCode.UNAUTHORIZED,
      ip_address: req.ip || '',
      device_info: req.headers['user-agent'] || '',
    });
    logger.error(`Insurer token verification error: ${err}`);
    res.status(401).json({ message: 'Invalid token' });
    return;
  }
};
