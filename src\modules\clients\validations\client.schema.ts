import { ClientNotificationEnum } from '../../../enum/client-notification.enum';
import { NotificationChannelEnum } from '../../../enum/notification-channel.eum';
import { z } from 'zod';

export const createClientSchema = z.object({
  email: z
    .string({ required_error: 'email is required' })
    .email('invalid email format')
    .nonempty({ message: 'email cannot be empty' })
    .toLowerCase(),

  first_name: z
    .string({ required_error: 'first_name is required' })
    .nonempty({ message: 'first_name cannot be empty' }),

  last_name: z
    .string({ required_error: 'last_name is required' })
    .nonempty({ message: 'last_name cannot be empty' }),

  phone_number: z
    .string({ required_error: 'phone_number is required' })
    .nonempty({ message: 'phone_number cannot be empty' }),

  client_type: z.enum(['individual', 'corporate'], {
    required_error: 'client_type is required',
    invalid_type_error: 'client_type must be either individual or corporate',
  }),

  account_manager_id: z
    .string({ required_error: 'account_manager is required' })
    .uuid({ message: 'account_manager must be a valid UUID' })
    .nonempty({ message: 'account_manager cannot be empty' }),

  status: z
    .enum(['active', 'inactive', 'expired', 'lost'], {
      message: 'status must be one of active, inactive, expired, or lost',
    })
    .optional(),

  business_name: z
    .string({ required_error: 'business_name is required' })
    .optional()
});


export const updateClientSchema = createClientSchema.partial();

export const notifyClientsSchema = z.object({
  clientIds: z.array(z.string().uuid(), {
    required_error: 'clientIds is required',
  }),
  channel: z.nativeEnum(NotificationChannelEnum, {
    required_error: 'channel is required',
    message: `channel must be one of ${Object.values(NotificationChannelEnum).join(', ')}`,
  }),
  notifications: z.array(z.string(), {
    required_error: 'notifications is required',
  }).refine(arr =>
    arr.every(val => Object.values(ClientNotificationEnum).includes(val as ClientNotificationEnum)), {
    message: `Each notification must be one of: ${Object.values(ClientNotificationEnum).join(', ')}`,
  }),
});


export const assignAccountManagerSchema = z.object({
  accountManagerId: z.string({ required_error: 'accountManagerId is required' }).uuid({ message: 'accountManagerId should be a valid UUID' }),
  clientIds: z.array(z.string().uuid(), { required_error: 'clientIds is required' }).min(1, 'At least one client ID is required'),
});


export const createBulkClientsSchema = z.object({
  account_manager_id: z
    .string({ required_error: 'account_manager_id is required', invalid_type_error: 'account_manager_id should be a valid string' })
    .uuid({ message: 'account_manager_id must be a valid UUID' })
    .optional(),

  clients: z.array(
    createClientSchema.omit({ account_manager_id: true }).superRefine((data, ctx) => {
      if (data.client_type === 'corporate' && !data.business_name) {
        ctx.addIssue({
          path: ['business_name'],
          message: 'Business name is required for corporate clients',
          code: z.ZodIssueCode.custom,
        });
      }
    }),
    {
      required_error: 'Clients array is required',
      invalid_type_error: 'Clients must be an array',
    }
  ).min(1, 'At least one client is required')
    .superRefine((clients, ctx) => {
      const seenEmails = new Set<string>();
      const seenPhones = new Set<string>();

      clients.forEach((client, index) => {
        if (client.email) {
          const lowerEmail = client.email.toLowerCase();
          if (seenEmails.has(lowerEmail)) {
            ctx.addIssue({
              path: [index, 'email'],
              message: 'Duplicate email found',
              code: z.ZodIssueCode.custom,
            });
          } else {
            seenEmails.add(lowerEmail);
          }
        }

        if (client.phone_number) {
          const phone = client.phone_number.trim();
          if (seenPhones.has(phone)) {
            ctx.addIssue({
              path: [index, 'phone_number'],
              message: 'Duplicate phone number found',
              code: z.ZodIssueCode.custom,
            });
          } else {
            seenPhones.add(phone);
          }
        }
      });
    }),
});

export type CreateBulkClientsDTO = z.infer<typeof createBulkClientsSchema>

export const markAsLostSchema = z.object({
  reason: z.string({ required_error: 'reason is required' }).nonempty({ message: 'reason cannot be empty' }),
});