import { Router } from 'express';
import TeamController from '../controllers/team.controller';
import { ModuleEnum } from '../../../enum/module.enum';
import { ModuleAction } from '../../../enum/module-action';
import { checkPermissions } from '../../../middlewares/auth.middleware';
import { validateRequest, validateUUIDParam } from '../../../middlewares/zod-validation.middleware';
import { UpdateUserSchema } from '../validations/team.schema';

const router = Router();
const teamController = new TeamController();

router.patch(
  '/:userId/update-user-details',
  checkPermissions([{ module: ModuleEnum.USERS, action: ModuleAction.UPDATE }]),
  validateUUIDParam('userId'),
  validateRequest(UpdateUserSchema),
  teamController.updateUserDetails.bind(teamController),
);

router.get(
  '/members',
  checkPermissions([{ module: ModuleEnum.USERS, action: ModuleAction.READ }]),
  teamController.getAllTeamMembers.bind(teamController),
);

export default router;