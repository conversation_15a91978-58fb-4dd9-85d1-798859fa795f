import { Request, Response } from 'express';
import AdminService from '../services/admin.service';
import { PaginationDto } from '../../../utils/pagination';
import { RequestWithAuditMetadata } from '../../../interfaces/custom-request.interface';
import RolesService from '../../roles-service/services/roles.service';
import AuditLogService from '../../audit-trail-service/services/audit-trail.service';
import { AuditLogQueryParams } from '../../../interfaces/audit-log.interface';
import { DateHelper } from '../../../utils/date.utils';
import AlertService from '../../alerts/services/alert.service';

export default class AdminController {
  private adminService: AdminService;
  private roleService: RolesService;
  private auditLogService: AuditLogService;
  private alertService: AlertService;

  constructor() {
    this.adminService = new AdminService();
    this.roleService = new RolesService();
    this.auditLogService = new AuditLogService();
    this.alertService = new AlertService();
  }

  /**
   * Handles user Invitation by system admins
   * @param req - The request object containing the email, password, and roleId.
   * @param res - The response object to send back the result.
   */

  async InviteUserBySystemAdmin(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { email, first_name, last_name, phone_number, roleId, department } = req.body;

    const { ipAddress, userAgent } = req.auditMetadata;

    const { userId } = req.user;

    const data = {
      email: email,
      first_name: first_name,
      last_name: last_name,
      phone_number: phone_number,
      department: department,
      roleId: roleId,
      userId,
      ipAddress: ipAddress,
      userAgent: userAgent,
    };
    const result = await this.adminService.InviteUser(data);
    return res.status(201).json({ data: result, message: 'User Registration Successful' });
  }

  /**
   * List all users with pagination
   * @param req - Express request object
   * @param res - Express response object
   * @returns {Promise<void>}
   * @description This method retrieves all users from the database with pagination.
   */
  async listAllUsers(req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const paginationDto = new PaginationDto(req.query);
    const { searchQuery } = req.query;
    const data = await this.adminService.listAllUsers(paginationDto, {
      searchQuery: searchQuery as string,
    });
    return res.status(200).json({ data: data, message: 'Users retrieved successfully.' });
  }

  /**
   * Activate or deactivate a user
   * @param req - Express request object
   * @param res - Express response object
   * @returns {Promise<any>}
   */
  async activateOrDeactivateUser(req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const { userId } = req.params;
    const { action } = req.body;
    const { ipAddress, userAgent } = req.auditMetadata;

    const data = {
      userId,
      action,
      ipAddress,
      userAgent,
    };

    await this.adminService.activateOrDeactivateUser(data);
    return res.status(200).json({ data: '', message: `User ${action}d successfully.` });
  }

  /**
   * Assign a role to a user
   * @param req - Express request object
   * @param res - Express response object
   * @returns {Promise<any>}
   * @description This method assigns a role to a user.
   */
  async assignRoleToUser(req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const { userId, roleId } = req.body;
    const initiatorId = req.user.userId;

    await this.adminService.assignRoleToUser(userId, roleId, initiatorId);
    res.status(200).json({ data: '', message: 'Role assigned to user successfully.' });
  }

  /**
   * List all available permissions with pagination
   * @param req - Express request object
   * @param res - Express response object
   * @returns {Promise<void>}
   * @description This method retrieves all available permissions from the database with pagination.
   */
  async listAvailablePermissions(req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const { searchQuery } = req.query;
    const paginationDto = new PaginationDto(req.query);
    const data = await this.roleService.listAvailablePermissions(paginationDto, {
      searchQuery: searchQuery as string,
    });
    return res.status(200).json({ data, message: 'Permissions retrieved successfully.' });
  }

  /**
   * List all permissions associated to a role
   * @param req - Express request object
   * @param res - Express response object
   * @returns {Promise<void>}
   * @description This method retrieves all permissions associated to a role from the database with pagination.
   */
  async fetchRolePermissions(req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const { roleId } = req.params;
    const paginationDto = new PaginationDto(req.query);
    const data = await this.roleService.fetchRolePermissions(roleId, paginationDto);
    return res.status(200).json({ data, message: 'Role permissions retrieved successfully.' });
  }

  /**
   * Update role permissions
   * @description This method updates the permissions of a specific role.
   * @param req - The request object containing the role ID and permissions.
   * @param res - The response object to send back the result.
   * @returns - A promise that resolves to a response object containing the updated role permissions.
   */

  async updateRolePermission(req: RequestWithAuditMetadata, res: Response) {
    const { permissions, roleName, roleDescription } = req.body;
    const { roleId } = req.params;
    const { ipAddress, userAgent } = req.auditMetadata;
    const initiatorId = req.user.userId;
    const data = {
      roleId,
      permissions,
      roleName,
      roleDescription,
      initiatorId,
      ipAddress,
      userAgent,
    };
    const result = await this.roleService.updateRolePermissions(data);
    return res
      .status(200)
      .json({ message: 'Role permissions updated successfully', data: result });
  }

  /**
   * Create a new role
   * @description This method creates a new role in the system.
   * @param req - The request object containing the role ID.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the role.
   */

  async createRole(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { name, permissions, description } = req.body;
    const { ipAddress, userAgent } = req.auditMetadata;
    const initiatorId = req.user.userId;
    const data = {
      name,
      permissions,
      description,
      initiatorId,
      ipAddress,
      userAgent,
    };
    const result = await this.roleService.createRole(data);
    return res.status(201).json({ message: 'Role created successfully', data: result });
  }

  /**
   * Get all roles
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the roles.
   */
  async getAllRoles(req: Request, res: Response): Promise<Response> {
    const { searchQuery } = req.query;
    const paginationDto = new PaginationDto(req.query);
    const result = await this.roleService.listAllRoles(paginationDto, { searchQuery: searchQuery as string });
    return res.status(200).json({ message: 'Roles fetched successfully', data: result });
  }


  /**
   * Get role by ID
   * @description This method retrieves a specific role by its ID.
   * @param req - The request object containing the role ID.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the role.
   */

  async getRoleById(req: Request, res: Response): Promise<Response> {
    const { roleId } = req.params;
    const result = await this.roleService.getRoleById(roleId);
    return res.status(200).json({ message: 'Role fetched successfully', data: result });
  }

  /**
   * Delete a role
   * @description This method deletes a specific role by its ID.
   * @param req - The request object containing the role ID.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object indicating the result of the deletion.
   */
  async deleteRole(req: RequestWithAuditMetadata, res: Response): Promise<Response> {
    const { roleId } = req.params;
    const { ipAddress, userAgent } = req.auditMetadata;
    const initiatorId = req.user.userId;

    await this.roleService.deleteRole({ roleId, initiatorId, ipAddress, userAgent });
    return res.status(200).json({ message: 'Role deleted successfully', data: '' });
  }


  /**
   * Get audit logs with pagination
   * @description This method retrieves audit logs based on filters and pagination.
   * @param req - The request object containing the filter criteria.
   * @param res - The response object to send back the result.
   * @returns {Promise<Response>} - A promise that resolves to a response object containing the audit logs.
   */
  async getAuditLogs(req: Request, res: Response): Promise<Response> {
    const paginationDto = new PaginationDto(req.query);

    // Initialize filter object
    const filter: AuditLogQueryParams = {};

    if (req.query.user_id) {
      filter.user_id = req.query.user_id as string;
    }
    if (req.query.module) {
      filter.module = req.query.module as string;
    }
    if (req.query.date_from) {
      const startDate = req.query.date_from as string;
      if (!DateHelper.isValidDate(startDate)) {
        return res.status(400).json({ message: 'Invalid date format for "date_from". Expected format: YYYY-MM-DD' });
      }
      filter.startDate = new Date(startDate);
    }
    if (req.query.date_to) {
      const endDate = req.query.date_to as string;
      if (!DateHelper.isValidDate(endDate)) {
        return res.status(400).json({ message: 'Invalid date format for "date_to". Expected format: YYYY-MM-DD' });
      }
      filter.endDate = new Date(endDate);
    }

    if (req.query.event_type) {
      filter.event_type = req.query.event_type as string;
    }
    if (req.query.ip_address) {
      filter.ip_address = req.query.ip_address as string;
    }
    if (req.query.error_code) {
      filter.error_code = req.query.error_code as string;
    }
    if (req.query.entity_id) {
      filter.entity_id = req.query.entity_id as string;
    }

    if (req.query.searchQuery) {
      filter.searchQuery = req.query.searchQuery as string;
    }

    // Call the service to get filtered audit logs
    const data = await this.auditLogService.viewAuditLogs(filter, paginationDto);
    return res.status(200).json({ data, message: 'Audit logs retrieved successfully.' });
  }

  /**
   * List all Modules
   * @description This method retrieves all modules from the database.
   * @param req - Express request object
   * @param res - Express response object
   */

  async listAllModules(_req: Request, res: Response): Promise<any> {
    const data = await this.adminService.listAllModules();
    return res.status(200).json({ data, message: 'Modules retrieved successfully.' });
  }


  /**
 * Retrieve a company by ID
 * @description This method retrieves company by ID.
 * @param req - Express request object
 * @param res - Express response object
 */
  async getCompanyId(_req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const data = await this.adminService.getCompany();
    return res.status(200).json({ data, message: 'System preference retrieved successfully' });
  }


  /**
   * Update a company
   * @description This methods Updates a company 
   * @param req - Express request object
   * @param res - Express response object
   */
  async updateSystemPreference(req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const data = req.body;

    const response = await this.adminService.updateSystemPreferences(userId, ipAddress, userAgent, data);
    return res.status(200).json({ data: response, message: 'System preference updated successfully' });
  }

  /**
   * Create an alert configuration
   * @description This method creates a new alert configuration
   * @param req - Express request object
   * @param res - Express response object
   */
  async createAlert(req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const data = req.body;

    const response = await this.alertService.createAlert(userId, ipAddress, userAgent, data);
    return res.status(201).json({ data: response, message: 'Alert created successfully' });
  }


  /**
 * Update an alert configuration
 * @description This method updates an existing alert configuration
 * @param req - Express request object
 * @param res - Express response object
 */
  async updateAlert(req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const { alertId } = req.params;
    const data = req.body;

    const response = await this.alertService.updateAlert(alertId, data, userId, ipAddress, userAgent);
    return res.status(200).json({ data: response, message: 'Alert updated successfully' });
  }


  /**
 * Get all alerts configurations
 * @description This method fetches all alert configurations
 * @param req - Express request object
 * @param res - Express response object
 */
  async getAlerts(req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const paginationDto = new PaginationDto(req.query);

    const response = await this.alertService.getAlerts(paginationDto);
    return res.status(200).json({ data: response, message: 'Alerts fetched successfully' });
  }


  /**
   * Delete alert configurations
   * @description This method deletes an alert configuration
   * @param req - Express request object
   * @param res - Express response object
   */
  async deleteAlert(req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const { ipAddress, userAgent } = req.auditMetadata;
    const { alertId } = req.params;
    const { userId } = req.user;

    await this.alertService.deleteAlert({
      alertId,
      userId,
      ipAddress,
      userAgent,
    });
    return res.status(200).json({ data: '', message: 'Alert deleted successfully' });
  }


  /**
   * Create or update an alert configurations
   * @description This method creates or updates an alert configuration
   * @param req - Express request object
   * @param res - Express response object
   */
  async createOrUpdateAlerts(req: RequestWithAuditMetadata, res: Response): Promise<any> {
    const { ipAddress, userAgent } = req.auditMetadata;
    const { userId } = req.user;
    const data = req.body;

    const response = await this.alertService.createOrUpdateAlerts(
      userId,
      ipAddress,
      userAgent,
      data
    );
    return res.status(200).json({ data: response, message: 'Successful' });
  }
}
